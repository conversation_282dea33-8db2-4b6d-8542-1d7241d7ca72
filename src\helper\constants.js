// this operators for attribute table filter
export const OPERATIONS = [
  {
    name: "equal",
    operation: "=",
    key: [
      "esriFieldTypeInteger",
      "esriFieldTypeDouble",
      "esriFieldTypeString",
      "esriFieldTypeDate",
      "esriFieldTypeSmallInteger",
    ],
    forDomain: true,
  },
  {
    name: "not",
    operation: "<>",
    key: [
      "esriFieldTypeInteger",
      "esriFieldTypeDouble",
      "esriFieldTypeSmallInteger",
      "esriFieldTypeDate",
    ],
    forDomain: true,
  },
  {
    name: "lessOrEqual",
    operation: ">=",
    key: [
      "esriFieldTypeInteger",
      "esriFieldTypeDouble",
      "esriFieldTypeSmallInteger",
    ],
  },
  {
    name: "lessthan",
    operation: "<",
    key: [
      "esriFieldTypeInteger",
      "esriFieldTypeDouble",
      "esriFieldTypeSmallInteger",
    ],
  },
  {
    name: "morethan",
    operation: ">",
    key: [
      "esriFieldTypeInteger",
      "esriFieldTypeDouble",
      "esriFieldTypeSmallInteger",
    ],
  },
  {
    name: "isNull",
    operation: "is null",
    key: [
      "esriFieldTypeInteger",
      "esriFieldTypeDouble",
      "esriFieldTypeString",
      "esriFieldTypeDate",
      "esriFieldTypeSmallInteger",
    ],
    forDomain: true,
  },
  {
    name: "notNull",
    operation: "is not null",
    key: [
      "esriFieldTypeString",
      "esriFieldTypeInteger",
      "esriFieldTypeDouble",
      "esriFieldTypeSmallInteger",
      "esriFieldTypeDate",
    ],
  },
  { name: "includes", operation: "like", key: ["esriFieldTypeString"] },
  { name: "dateAfter", operation: ">=", key: ["esriFieldTypeDate"] },
  { name: "dateBefore", operation: "<=", key: ["esriFieldTypeDate"] },
];
// this is not used
export const tblDataBtns = {
  ownershipAttrTbl: "ownershipAttrTbl",
  royalDataAttrTbl: "royalDataAttrTbl",
  salesLandsAttrTbl: "salesLandsAttrTbl",
  ParcelConditionsBtn: "ParcelConditionsBtn",
  ParcelLicenseBtn: "ParcelLicenseBtn",
  ShopLicenseBtn: "ShopLicenseBtn",
  LandbaseParcelBtn: "LandbaseParcelBtn",
  farzSubmissions: "farzSubmissions",
  krokySubmissions: "krokySubmissions",
  updateContractSubmissions: "updateContractSubmissions",
  buildingLandLicenseBtn: "buildingLandLicenseBtn",
};

// this is for attribute table
export const externalBtnsForTblData = {
  zoomBtn: "zoomBtn",
  exportAttrTbl: "exportAttrTbl",
  exportKmlAttrTbl: "exportKmlAttrTbl",
  googleMapsAttrTbl: "googleMapsAttrTbl",
  exportXlAttrTbl: "exportXlAttrTbl",
  exportCadAttrTbl: "exportCadAttrTbl",
  exportShpAttrTbl: "exportShpAttrTbl",
  exportPdfAttrTbl: "exportPdfAttrTbl",
  filterAttrTblBtn: "filterAttrTblBtn",
  statisticsAttrTbl: "statisticsAttrTbl",
};

export const appIds = {
  mapExpolerer: 5,
};

export const modulesIDs = {
  // marsadModule: 47,            // for production
  marsadModule: 79,           // for test
  // exportFilesModule:49,     //for production
  exportFilesModule: 32, //for test
  importGoogle: 50,
  searchByAttrModule: 48,
  incidentsModule: 60,
  landsKPIsDashboard: 73,
  // layersMap: 82, //for production
  layersMap: 75, //for test
  // interactiveMap: 83, //for production
  interactiveMap: 76, //for test
  // updatingRequests: 84, //for production
  updatingRequests: 77, //for test
  // add interactive map module id
  // ownerShipLandsModule:25,
  // royalLandsModule:25
};

export const PARCEL_LANDS_LAYER_NAME = "Landbase_Parcel";
export const PLAN_DATA_LAYER_NAME = "PLAN_DATA";
export const DISTRICT_DATA_LAYER_NAME = "District_Boundary";

export const plannedLandsGroupsIDs = [3531];
export const plannedLandsGroupsIDsForDistrict = [5912];
