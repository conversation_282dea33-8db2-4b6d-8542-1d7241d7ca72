import React from "react";
import { useState } from "react";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import GenericChart from "../GenericApexCharts";
import TextCountSection from "../TextCountSection";
import { faPlusCircle, faExpandArrowsAlt } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Modal } from "antd";

function LeftSideChartsContainer({
  changeMapTablesShow,
  mapTablesShow,
  layersNames,
  selectedLayer,
  map,
  queryData,
}) {
  const { t } = useTranslation("dashboard");
  const [isModalOpen, setIsModalOpen] = useState(null);
 
  const [chartsDataArr, setChartsDataArr] = useState({
    data: [],
    layerName: "",
  });

  useEffect(() => {
    if (selectedLayer) {
      let layerDataWithCharts = layersNames.find(
        (lay) => lay?.englishName === selectedLayer
      );
      setChartsDataArr({
        data: layerDataWithCharts.dashboardCharts || [],
        layerName: selectedLayer,
      });
    }
  }, [selectedLayer]);
  let topChartsStyle =
    chartsDataArr.data.length === 3
      ? { flexBasis: "60%" }
      : chartsDataArr.data.length === 2
      ? { flexBasis: "50%" }
      : { flexBasis: "auto" };
  if (chartsDataArr?.data?.length)
    return (
      <div className="left-side-chart-container">
        <div
          className={
            chartsDataArr.data.filter((c) => c.position === "top").length <= 2
              ? "charts-layout-col"
              : "charts-layout-row"
          }
          style={
            chartsDataArr.data
              .filter((c) => c.position === "bottom")
              .find((i) => i.dependentLayer)
              ? { ...topChartsStyle, flexGrow: "0.8" }
              : { ...topChartsStyle }
          }>
          {chartsDataArr.data
            .filter((c) => c.position === "top")
            .map((item, index) => {
              if (chartsDataArr.length === 2)
                return (
                  <div
                    className="single-chart-item mapSquare"
                    key={"chart" + index + "apex"}>
                    <GenericChart
                      title={t(item.alias)}
                      onClickTitle={changeMapTablesShow}
                      sideTblTitle={mapTablesShow}
                      chartObj={item}
                      selectedLayer={chartsDataArr.layerName}
                      map={map}
                      layersNames={layersNames}
                      type={item.chartType}
                      queryData={queryData}
                    />
                    {/* <PieChart width={200} height={200} title={t(item.alias)} /> */}
                  </div>
                );
              else if (item?.relatedChildChart)
                return (
                  <React.Fragment key={"chart" + index + "apex"}>
                    <GenericChart
                      title={t(item.alias)}
                      onClickTitle={changeMapTablesShow}
                      sideTblTitle={mapTablesShow}
                      chartObj={item}
                      map={map}
                      selectedLayer={chartsDataArr.layerName}
                      layersNames={layersNames}
                      type={item.chartType}
                      queryData={queryData}
                    />
                  </React.Fragment>
                );
              else
                return (
                  <div
                    className="normal-chart-item mapSquare"
                    key={"chart" + index + "apex"}>
                    <FontAwesomeIcon
                      onClick={() => setIsModalOpen("chart" + index + "apex")}
                      className="faplusChart"
                      // icon={faPlusCircle}
                      icon={faExpandArrowsAlt}
                    />
                    <Modal
                      cancelText="إغلاق"
                      className=""
                      visible={
                        isModalOpen == "chart" + index + "apex" ? true : false
                      }
                      onCancel={() => setIsModalOpen(null)}
                      footer={null}
                      width={"50%"}>
                      <GenericChart
                        title={t(item.alias)}
                        onClickTitle={changeMapTablesShow}
                        sideTblTitle={mapTablesShow}
                        chartObj={item}
                        map={map}
                        selectedLayer={chartsDataArr.layerName}
                        layersNames={layersNames}
                        type={item.chartType}
                        queryData={queryData}
                      />
                    </Modal>{" "}
                    <GenericChart
                      title={t(item.alias)}
                      onClickTitle={changeMapTablesShow}
                      sideTblTitle={mapTablesShow}
                      chartObj={item}
                      map={map}
                      selectedLayer={chartsDataArr.layerName}
                      layersNames={layersNames}
                      type={item.chartType}
                      queryData={queryData}
                    />
                  </div>
                );
            })}
        </div>
        {chartsDataArr.data
          .filter((c) => c.position === "bottom")
          .map((item) => {
            if (item.dependentLayer)
              return (
                <div
                  className="last-chart-item mapSquare text-count-info"
                  key={"chart-last"}>
                  <TextCountSection
                    title={t(item.alias)}
                    dependentLayer={item.dependentLayer}
                    chartObj={item}
                    layersNames={layersNames}
                    map={map}
                    selectedLayer={chartsDataArr.layerName}
                    type={item.chartType}
                    queryData={queryData}
                  />
                </div>
              );
            else
              return (
                <div className="last-chart-item mapSquare" key={"chart-last"}>
                  <GenericChart
                    title={t(item.alias)}
                    onClickTitle={changeMapTablesShow}
                    sideTblTitle={mapTablesShow}
                    chartObj={item}
                    layersNames={layersNames}
                    map={map}
                    selectedLayer={chartsDataArr.layerName}
                    type={item.chartType}
                    queryData={queryData}
                  />
                </div>
              );
          })}
      </div>
    );
  else return null;
}

export default LeftSideChartsContainer;
