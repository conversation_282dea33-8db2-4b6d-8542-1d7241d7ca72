import React, { useRef, useState } from "react";

import { useTranslation } from "react-i18next";

const FloatingLabelTextarea = ({
  label = "Label",
  placeholder = "",
  onInputChange,
  error = false,
  value = "",
  maxLength = 1000,
  helperText = "",
  disabled = false,
  userInfo = null,
}) => {
  const { t, i18n } = useTranslation(["more"]);
  const [userInput, setUserInput] = useState(value);
  const textareaRef = useRef(null);

  const handleInputChange = (event) => {
    const newValue = event.target.value.slice(0, maxLength); // Enforce maxLength
    setUserInput(newValue);

    // Adjust height to match content
    if (textareaRef && textareaRef.current) {
      const textarea = textareaRef.current;
      const newHeight = Math.min(textarea.scrollHeight, 150); // Limit height to 150px
      textarea.style.height = `${newHeight}px`; // Set height dynamically
    }

    if (onInputChange) {
      onInputChange(newValue); // Pass the updated value to the parent
    }
  };

  return (
    <div
      style={{
        position: "relative",
        border: "1px solid #999",
        borderRadius: "16px",
        padding: "2px 0px",
        overflow: "hidden",
      }}
    >
      {/* Placeholder span */}
      {!userInput && (
        <span
          style={{
            position: "absolute",
            top: "12px",
            right: i18n.language === "en" ? "0" : "12px",
            left: i18n.language === "en" ? "12px" : "0",
            fontSize: "14px",
            lineHeight: "1.5",
            // color: "red", // Placeholder color
            pointerEvents: "none", // Ensure it doesn't block clicks
            display: "flex",
            alignItems: "center",
          }}
        >
          {placeholder}{" "}
          <span style={{ color: "red", margin: "0px 2px" }}>*</span>
        </span>
      )}
      {userInfo && (
        <span
          style={{
            position: "absolute",
            top: "12px",
            right: i18n.language === "en" ? "0" : "12px",
            left: i18n.language === "en" ? "12px" : "0",
            fontSize: "14px",
            lineHeight: "1.5",
            color: "#A8A8A8",
            fontWeight: 700,
            fontSize: "12px",
            transition: "all 250ms ease-out", // Faster, simpler transition
            pointerEvents: "none", // Ensure it doesn't block clicks
            display: "flex",
            alignItems: "center",
            background:"#fff"
          }}
        >
          {label}
        </span>
      )}

      <textarea
        ref={textareaRef} // Attach the ref
        value={userInput}
        onChange={handleInputChange} // Use the event handler
        style={{
          // height: "100px",
          // maxHeight: "150px", // Limit maximum height
          height: userInfo ? "auto" : "100px",  // If userInfo exists, set height to 100px; otherwise, let it adjust
          maxHeight: userInfo ? "none" : "150px", // Limit max height only if userInfo exists
          width: "100%",
          resize: "none",
          border: "none",
          outline: "none",
          padding: "12px",
          fontSize: "14px",
          lineHeight: "1.5",
          boxSizing: "border-box",
          overflowY: "auto", // Enable vertical scrolling
          background:"transparent",
          marginTop: `${userInfo ? "20px" : "0"}`,
          paddingBottom:`${userInfo && "6px"}`,
        }}
        disabled={disabled}
      />
      {helperText && (
        <div style={{ color: "red", fontSize: "12px", marginTop: "4px" }}>
          {helperText}
        </div>
      )}
    </div>
  );
};

export default FloatingLabelTextarea;
