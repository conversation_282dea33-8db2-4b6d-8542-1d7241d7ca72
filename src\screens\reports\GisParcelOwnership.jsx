/* eslint-disable react/jsx-key */
import React, { Component } from "react";
import { round, sortBy, find } from "lodash";
import { withTranslation } from "react-i18next";
import * as projection from "@arcgis/core/geometry/projection";

import Logo from "../../assets/images/amanaLogo.png";
import Vision from "../../assets/images/vision.png";
import Loader from "../../containers/Loader";

import {
  queryTask,
  getMapInfo,
  getFeatureDomainName,
  highlightFeature,
} from "../../helper/common_func";
import { akarReportFields, cornersBoundariesFields } from "../../helper/layers";
import { notificationMessage } from "../../helper/utilsFunc";
import {
  boundariesEnum,
  getCornersAndLength,
} from "./MapReportComponents/helpers";
import MapComponent from "./MapReportComponents/ReportMApViewer";

class GisParcelOwnership extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: true,
      where: localStorage.getItem("akarReportLandId"),
      layerName: "Landbase_Parcel",
      landParcelData: null,
      cornerData: null,
      boundariesData: null,
      maxReqIndex: 3, // one for feature info, second for site map and third for kroky map
    };
    this.requestsIndex = 0;
  }

  componentDidMount() {
    const { t } = this.props;
    if (!this.state.where) {
      this.props.navigateToServerErrPage();
      return;
    }
    this.setState({ zoom: true, loading: true });
    getMapInfo(window.mapUrl).then((data) => {
      this.requestsIndex += 1;
      this.layers = data.info.mapInfo.layers;
      let layer = find(
        this.layers,
        {
          name: this.state.layerName,
        },
        true
      );
      let layerID = layer.id;

      let queryParams = {
        url: window.mapUrl + "/" + layerID,
        notShowLoading: false,
        returnGeometry: true,
        outFields: akarReportFields.outFields,
        where: this.state.where,
      };
      queryTask({
        ...queryParams,
        callbackResult: ({ features }) => {
          if (features.length) {
            getFeatureDomainName(features, layerID).then(async (res) => {
              await this.loadProjection();
              let dataOfCornersAndLength = getCornersAndLength(
                res[0],
                projection
              );
              this.setState({
                landParcelData: res[0],
                loading:
                  this.requestsIndex === this.state.maxReqIndex ? false : true,
                cornerData: dataOfCornersAndLength.corners,
                boundariesData: dataOfCornersAndLength.boundaries,
              });
            });
          }
        },
        callbackError: (err) => {
          notificationMessage(t("common:retrievError"), 4);
          this.setState({ loading: false });
        },
      });
    });
  }
  // componentWillUnmount() {
  //   localStorage.removeItem("akarReportLandId");
  // }
  async loadProjection() {
    await projection.load();
  }
  stopLoading() {
    this.setState({ loading: false });
  }

  onMapCreate(feature, isKrokyGeneralSite, map) {
    highlightFeature(feature, map, {
      isZoom: true,
      layerName: "zoomGraphicLayer",
      zoomFactor: isKrokyGeneralSite ? 6 : 0,
      callback: () => {
        this.requestsIndex += 1;
        this.setState({
          loading: this.requestsIndex === this.state.maxReqIndex ? false : true,
        });
        map?._fixedPan && map?._fixedPan(-0.2 * map.width, 0);
      },
    });
  }

  removeBaseMap(feature, isLastMap, map) {
    highlightFeature(feature, map, {
      isZoom: true,
      layerName: "zoomGraphicLayer",
      zoomFactor: 50,
      isnoHightlight: true,
      isDashStyle: true,
      callback: () => {
        this.requestsIndex += 1;
        let removedBaseMap = map.allLayers.items.find(
          (i) => i.id === "baseMap"
        );
        if (removedBaseMap) {
          map?.remove(removedBaseMap);
        }
        map?._fixedPan && map?._fixedPan(-0.2 * map.width, 0);
        if (isLastMap) {
          setTimeout(() => {
            this.setState({
              loading:
                this.requestsIndex === this.state.maxReqIndex ? false : true,
            });
          }, 2000);
        }
        this.setState({
          loading: this.requestsIndex === this.state.maxReqIndex ? false : true,
        });
      },
    });
  }

  render() {
    let { zoom, landParcelData } = this.state;
    let { t } = this.props;

    return this.state.where ? (
      <div className="reportStyle2">
        {this.state.loading ? <Loader /> : null}
        {landParcelData && (
          <>
            <div
              style={{ padding: "10px", margin: "1%", textAlign: "justify" }}
              className="one-page"
            >
              <div>
                <div className="">
                  <div style={{ fontSize: "17px", display: "flex" }}>
                    {/* <div>
                      <img
                      alt=''
                        src={Logo}
                        className="img-logo-print2"
                        style={{ width: "130px" }}
                      />
                    </div> */}
                    <div
                      style={{
                        alignSelf: "center",
                        textAlign: "right",
                        paddingRight: "1rem",
                      }}
                    >
                      <h5>المملكة العربية السعودية</h5>
                      <h5>وزارة البلديات والإسكان</h5>
                      <h5>أمانة المنطقة الشرقية</h5>
                    </div>
                    <div className="investment_report_header">
                      <div>
                        <div>
                          <img
                            alt=""
                            src={Logo}
                            className="img-logo-print2"
                            style={{ width: "130px" }}
                          />
                        </div>
                        <h2>الصحيفة العقارية</h2>
                      </div>
                    </div>

                    <div>
                      <div>
                        <img
                          alt=""
                          src={Vision}
                          className="img-logo-print2"
                          style={{ width: "220px", height: "200px" }}
                        />
                      </div>
                    </div>
                  </div>
                  <div style={{ display: "flex", justifyContent: "end" }}>
                    <button
                      type="button"
                      style={{
                        width: "16%",
                        fontSize: "20px",
                        color: "white",
                        backgroundColor: "#0a8eb9",
                        borderColor: "#0a8eb9",
                        marginLeft: "2%",
                      }}
                      className="btn btn-warning print-button"
                      onClick={(e) => {
                        e.preventDefault();
                        window.print();
                      }}
                    >
                      طباعة
                    </button>
                  </div>
                </div>
                <h3 className="underlineStyle">بيانات الموقع</h3>
                {/* <h5 className=""> </h5> */}

                <div key={"p"}>
                  <div
                    style={{
                      gridTemplateColumns: "auto auto",
                      display: "grid",
                    }}
                  >
                    {Object.keys({
                      ...landParcelData.attributes,
                      googleMapsLink: "",
                    }).map((attribute, index) => {
                      if (attribute === "googleMapsLink") {
                        return (
                          <div
                            className="reportRow"
                            style={{ gridColumn: "1/3" }}
                          >
                            <div>{t(`layers:${attribute}`)}</div>

                            <div>
                              <a
                                href={
                                  "http://maps.google.com/maps?q=" +
                                  (landParcelData.geometry.latitude ||
                                    landParcelData.geometry.centroid.latitude) +
                                  "," +
                                  (landParcelData.geometry.longitude ||
                                    landParcelData.geometry.centroid.longitude)
                                }
                                target="blank"
                              >
                                رابط جوجل
                              </a>
                            </div>
                          </div>
                        );
                      } else
                        return (
                          akarReportFields.outFields.indexOf(attribute) > -1 &&
                          attribute !== "PARCEL_SPATIAL_ID" && (
                            <div key={"xyyy" + index}>
                              {akarReportFields.outFields.indexOf(attribute) >
                                -1 && (
                                <div className="reportRow">
                                  <div>
                                    {t(
                                      `layers:${
                                        [...akarReportFields.aliasOutFields][
                                          akarReportFields.outFields.indexOf(
                                            attribute
                                          )
                                        ]
                                      }`
                                    )}
                                  </div>

                                  <div
                                    style={
                                      attribute === "PARCEL_LAT_COORD" ||
                                      attribute === "PARCEL_LONG_COORD"
                                        ? {
                                            direction: "ltr",
                                            fontFamily: "arabicNums",
                                          }
                                        : { fontFamily: "arabicNums" }
                                    }
                                  >
                                    {landParcelData.attributes[attribute] &&
                                    !isNaN(
                                      landParcelData.attributes[attribute]
                                    ) &&
                                    attribute !== "PARCEL_LAT_COORD" &&
                                    attribute !== "PARCEL_LONG_COORD"
                                      ? round(
                                          Number(
                                            landParcelData.attributes[attribute]
                                          ),
                                          2
                                        )
                                      : [
                                          "<Null>",
                                          "",
                                          " ",
                                          "Null",
                                          undefined,
                                          null,
                                          "0",
                                          0,
                                        ].includes(
                                          landParcelData.attributes[attribute]
                                        )
                                      ? "غير متوفر"
                                      : attribute === "PARCEL_LAT_COORD"
                                      ? round(
                                          Number(
                                            landParcelData.geometry.latitude ||
                                              landParcelData.geometry.centroid
                                                .latitude
                                          ),
                                          6
                                        )
                                      : attribute === "PARCEL_LONG_COORD"
                                      ? round(
                                          Number(
                                            landParcelData.geometry.longitude ||
                                              landParcelData.geometry.centroid
                                                .longitude
                                          ),
                                          6
                                        )
                                      : landParcelData.attributes[attribute]
                                      ? landParcelData.attributes[attribute]
                                      : t("common:notFoundValue")}
                                  </div>
                                </div>
                              )}
                            </div>
                          )
                        );
                    })}
                  </div>

                  <h3 className="underlineStyle">إحداثيات أركان الموقع</h3>
                  {this.state.cornerData && (
                    <table className="table table-bordered">
                      <thead>
                        <tr>
                          {cornersBoundariesFields.corners &&
                            Object.keys(
                              cornersBoundariesFields.corners.outFields
                            ).map(
                              (attribute, i) =>
                                attribute !== "OBJECTID" && (
                                  <th
                                    key={"xyzq" + i}
                                    style={{
                                      color: "teal",
                                      fontSize: "14px",
                                    }}
                                  >
                                    {t(
                                      `layers:${cornersBoundariesFields.corners.aliasOutFields[i]}`
                                    )}
                                  </th>
                                )
                            )}
                        </tr>
                      </thead>
                      <tbody>
                        {this.state.cornerData ? (
                          sortBy(
                            this.state.cornerData,
                            (o) => o?.attributes?.CORNER_NO
                          ).map((corner, i) => {
                            return (
                              <tr>
                                {cornersBoundariesFields.corners.outFields.map(
                                  (attribute, keye) => {
                                    return (
                                      Object.keys(corner.attributes).indexOf(
                                        attribute
                                      ) > -1 &&
                                      corner.attributes[attribute] &&
                                      attribute !== "PARCEL_SPATIAL_ID" && (
                                        <td key={"x" + keye}>
                                          {attribute === "CORNER_NO"
                                            ? corner.attributes[attribute]
                                            : corner.attributes[
                                                attribute
                                              ].toFixed(4)}
                                        </td>
                                      )
                                    );
                                  }
                                )}
                              </tr>
                            );
                          })
                        ) : (
                          <tr>
                            <td>غير متوفر</td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  )}

                  {this.state.boundariesData && (
                    <div style={{ display: "flex" }}>
                      <div style={{ width: "100%" }}>
                        <h3 className="underlineStyle">
                          الحدود والأبعاد من المخطط
                        </h3>
                        {
                          <table className="table table-bordered">
                            <thead>
                              <tr>
                                {cornersBoundariesFields.boundaries.outFields.map(
                                  (attribute, i) =>
                                    attribute !== "OBJECTID" && (
                                      <th
                                        key={"xy" + i}
                                        style={{
                                          color: "teal",
                                          fontSize: "14px",
                                        }}
                                      >
                                        {t(
                                          `layers:${cornersBoundariesFields.boundaries.aliasOutFields[i]}`
                                        )}
                                      </th>
                                    )
                                )}
                              </tr>
                            </thead>
                            <tbody>
                              {this.state.boundariesData.length
                                ? sortBy(
                                    this.state.boundariesData,
                                    (o) => o?.attributes?.BOUNDARY_NO
                                  ).map((boundry, i) => (
                                    <tr>
                                      {cornersBoundariesFields.boundaries.outFields.map(
                                        (attribute, keyr) =>
                                          Object.keys(
                                            boundry.attributes
                                          ).indexOf(attribute) > -1 &&
                                          attribute !== "PARCEL_SPATIAL_ID" && (
                                            <td key={"xyz" + keyr}>
                                              {boundry.attributes[attribute]
                                                ? !isNaN(
                                                    boundry.attributes[
                                                      attribute
                                                    ]
                                                  ) &&
                                                  attribute !==
                                                    "BOUNDARY_DIRECTION"
                                                  ? round(
                                                      Number(
                                                        boundry.attributes[
                                                          attribute
                                                        ]
                                                      ),
                                                      2
                                                    )
                                                  : attribute ===
                                                    "BOUNDARY_DIRECTION"
                                                  ? t(
                                                      `layers:${
                                                        boundariesEnum[
                                                          boundry.attributes[
                                                            attribute
                                                          ]
                                                        ]
                                                      }`
                                                    )
                                                  : boundry.attributes[
                                                      attribute
                                                    ]
                                                : "بدون"}
                                            </td>
                                          )
                                      )}
                                    </tr>
                                  ))
                                : [1].map(() => (
                                    <>
                                      <tr>
                                        {[1].map(() => (
                                          <td>غير متوفر</td>
                                        ))}
                                      </tr>
                                    </>
                                  ))}
                            </tbody>
                          </table>
                        }
                      </div>
                    </div>
                  )}

                  <div
                    style={{ display: "flex" }}
                    className={zoom ? "zoomcenter" : ""}
                  >
                    <div className="divBorder" style={{ width: "50%" }}>
                      <label className="labelReport">كروكي الموقع</label>
                      <MapComponent
                        onMapCreate={this.onMapCreate.bind(
                          this,
                          landParcelData,
                          ""
                        )}
                        siteSpatial={
                          landParcelData.attributes.PARCEL_SPATIAL_ID
                        }
                        mapUrl={window.mapUrl}
                        mapId={`reportMap1`}
                        isStatlliteMap={true}
                        isOnlyfeature="true"
                        isReportMap="true"
                        style={{ width: "100%", height: "300px" }}
                      />
                    </div>
                    <div className="divBorder" style={{ width: "50%" }}>
                      <label className="labelReport">كروكي الموقع العام</label>
                      <MapComponent
                        onMapCreate={
                          //   this.removeBaseMap.bind(
                          //   this,
                          //   landParcelData,
                          //   true
                          // )
                          this.onMapCreate.bind(this, landParcelData, "true")
                        }
                        mapUrl={window.mapUrl}
                        mapId={`reportMapNoMap1`}
                        isStatlliteMap={true}
                        // isOnlyfeature="true"
                        isReportMap="true"
                        isLastMap={true}
                        stopLoading={this.stopLoading.bind(this)}
                        // isConditionMap="true"
                        siteSpatial={
                          landParcelData.attributes.PARCEL_SPATIAL_ID
                        }
                        style={{ width: "100%", height: "300px" }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <footer className="footer-report-print">
              {/* <img
            alt="footer"
              src={footer}
              className="img-logo-print2"
              align="left"
              style={{ width: "100px", marginLeft: "40px" }}
            /> */}
            </footer>
          </>
        )}
      </div>
    ) : !this.state.where ? (
      <h1 style={{ textAlign: "center" }}>حدث خطأ برجاء المحاولة مرة أخرى</h1>
    ) : (
      <Loader />
    );
  }
}

export default withTranslation(["common", "layers"])(GisParcelOwnership);
