import React, { useState, useEffect } from "react";
import GoogleMaps from "./GoogleMaps";
import InquiryTool from "./InquiryTool";
import LayersMenu from "./LayersMenu";
import CompareLayers from "./CompareLayers";
import SmallMap from "./SmallMap";
import Traffic from "./Traffic";
import Print from "./Print";
import UpdateLand from "./UpdateLand";
import ToolsMenu from "./ToolsMenu";
// import TocComponent from "./TocComponent/index"
import LegendComponent from "./LegendComponenet";

// Direct imports from Esri instead of using LoadModules
import SketchViewModel from "@arcgis/core/widgets/Sketch/SketchViewModel";
import GraphicsLayer from "@arcgis/core/layers/GraphicsLayer";
import Graphic from "@arcgis/core/Graphic";
import SimpleFillSymbol from "@arcgis/core/symbols/SimpleFillSymbol";
import SimpleLineSymbol from "@arcgis/core/symbols/SimpleLineSymbol";
import SimpleMarkerSymbol from "@arcgis/core/symbols/SimpleMarkerSymbol";
import TextSymbol from "@arcgis/core/symbols/TextSymbol";
import Query from "@arcgis/core/rest/support/Query";
import * as query from "@arcgis/core/rest/query";
import Polygon from "@arcgis/core/geometry/Polygon";
import Point from "@arcgis/core/geometry/Point";
import { getLayerId, getFeatureDomainName } from "../../helper/common_func";

const AllTools = (props) => {
  console.log("props", props);
  const [sketch, setSketch] = useState(null);

  const [isPolygonSelectionEnabled, setPolygonSelectionEnabled] =
    useState(false);
  const enablePolygonSelectionMode = (map, onResult) => {
    if (!map) {
      console.error("Map is undefined");
      return;
    }

    const graphicsLayer = map.findLayerById("highlightGraphicLayer");

    if (!graphicsLayer) {
      console.error("Graphics layer not found!");
      return;
    }

    // Create a separate layer for measurements and labels
    let measurementsLayer = map.findLayerById("MeasurementsLayer");
    if (!measurementsLayer) {
      measurementsLayer = new GraphicsLayer({ id: "MeasurementsLayer" });
      map.add(measurementsLayer);
    }

    let SketchLayer = map.findLayerById("SelectGraphicLayer");
    console.log("SketchLayer", SketchLayer);

    if (!SketchLayer) {
      SketchLayer = new GraphicsLayer({ id: "SelectGraphicLayer" });
      map.add(SketchLayer);
    }

    const view = window.__view || map.view;
    if (!view) {
      console.error("View is undefined");
      return;
    }

    const sketchViewModel = new SketchViewModel({
      view: view,
      layer: SketchLayer,
      creationMode: "update",
      availableCreateTools: ["polygon"],
      visibleElements: {
        createTools: { polygon: true },
        undoRedoMenu: true,
      },
      polygonSymbol: {
        type: "simple-fill",
        color: [255, 255, 255, 0.4],
        outline: {
          color: [255, 165, 0],
          width: 2,
        },
      },
    });

    props.setSketch(sketchViewModel);
    sketchViewModel.create("polygon");

    const createBufferPolygon = (point, bufferSize = 2) => {
      // Create a small square polygon around the point
      const x = point.x;
      const y = point.y;
      const half = bufferSize / 2;

      return new Polygon({
        rings: [
          [
            [x - half, y - half], // top left
            [x + half, y - half], // top right
            [x + half, y + half], // bottom right
            [x - half, y + half], // bottom left
            [x - half, y - half], // close the polygon
          ],
        ],
        spatialReference: point.spatialReference,
      });
    };

    // Function to calculate polygon area in square meters
    const calculateArea = (geometry) => {
      // Check if the geometry has a built-in method for area calculation
      if (geometry.area) {
        return geometry.area;
      }

      // Manual calculation for polygon area if needed
      // This is a simplified version - for actual implementation,
      // use appropriate geodesic calculations based on spatial reference
      return Math.abs(
        geometry.rings[0].reduce((area, point, i, points) => {
          const nextPoint = points[(i + 1) % points.length];
          return area + (point[0] * nextPoint[1] - nextPoint[0] * point[1]);
        }, 0) / 2
      );
    };

    // Function to calculate length between two points in map units
    const calculateDistance = (point1, point2) => {
      const dx = point2[0] - point1[0];
      const dy = point2[1] - point1[1];
      return Math.sqrt(dx * dx + dy * dy);
    };

    // Function to convert map point to geographic coordinates (latitude, longitude)
    const toLatLong = async (point, spatialReference) => {
      try {
        // Use ArcGIS projection functions to convert to geographic coordinates
        // This requires importing additional modules like geometryEngine or projection

        // For demonstration, assuming webMercatorUtils is available
        if (
          window.webMercatorUtils &&
          window.webMercatorUtils.webMercatorToGeographic
        ) {
          const geoPoint = window.webMercatorUtils.webMercatorToGeographic({
            x: point[0],
            y: point[1],
            spatialReference: spatialReference,
          });
          return { lat: geoPoint.y.toFixed(6), long: geoPoint.x.toFixed(6) };
        }

        // Fallback to original coordinates if conversion utils aren't available
        return {
          x: point[0].toFixed(2),
          y: point[1].toFixed(2),
          spatialReference: spatialReference,
        };
      } catch (error) {
        console.error("Error converting to lat/long:", error);
        return { x: point[0].toFixed(2), y: point[1].toFixed(2) };
      }
    };

    // Add measurements and labels to the polygon
    const addMeasurementsToPolygon = async (geometry) => {
      try {
        //  measurementsLayer.removeAll();

        if (!geometry || !geometry.rings || geometry.rings.length === 0) return;

        const rings = geometry.rings[0];
        const spatialReference = geometry.spatialReference;

        // Calculate and display edge lengths
        for (let i = 0; i < rings.length - 1; i++) {
          const point1 = rings[i];
          const point2 = rings[i + 1];

          // Calculate midpoint for placing the label
          const midPoint = [
            (point1[0] + point2[0]) / 2,
            (point1[1] + point2[1]) / 2,
          ];

          // Calculate distance between points
          const distance = calculateDistance(point1, point2);

          // Create text symbol for the distance
          const lengthLabel = new TextSymbol({
            color: "black",
            haloColor: "white",
            haloSize: 1,
            text: `${distance.toFixed(2)} م`,
            font: {
              size: 10,
              family: "sans-serif",
              weight: "bold",
            },
          });

          // Add length label at midpoint
          measurementsLayer.add(
            new Graphic({
              geometry: new Point({
                x: midPoint[0],
                y: midPoint[1],
                spatialReference: spatialReference,
              }),
              symbol: lengthLabel,
            })
          );
        }

        // Add corner coordinates (lat/long)
        for (let i = 0; i < rings.length - 1; i++) {
          const corner = rings[i];
          const coordinates = await toLatLong(corner, spatialReference);

          // Create marker for corner point
          const markerSymbol = new SimpleMarkerSymbol({
            style: "circle",
            color: "red",
            size: 6,
            outline: {
              color: "white",
              width: 1,
            },
          });

          // Add corner point
          measurementsLayer.add(
            new Graphic({
              geometry: new Point({
                x: corner[0],
                y: corner[1],
                spatialReference: spatialReference,
              }),
              symbol: markerSymbol,
            })
          );

          // Create text for coordinates
          const coordLabel = new TextSymbol({
            color: "blue",
            haloColor: "white",
            haloSize: 1,
            text: `احداثيات: ${coordinates.x}, ${coordinates.y}`,
            font: {
              size: 8,
              family: "sans-serif",
            },
            yoffset: 15,
          });

          // Add coordinate label
          measurementsLayer.add(
            new Graphic({
              geometry: new Point({
                x: corner[0],
                y: corner[1],
                spatialReference: spatialReference,
              }),
              symbol: coordLabel,
            })
          );
        }

        // Calculate centroid (simplified)
        let sumX = 0,
          sumY = 0;
        for (let i = 0; i < rings.length - 1; i++) {
          sumX += rings[i][0];
          sumY += rings[i][1];
        }

        const centroidX = sumX / (rings.length - 1);
        const centroidY = sumY / (rings.length - 1);

        // Calculate area
        const area = calculateArea(geometry);

        // Create area label at centroid
        const areaLabel = new TextSymbol({
          color: "darkgreen",
          haloColor: "white",
          haloSize: 2,
          text: `المساحة: ${area.toFixed(2)} م²`,
          font: {
            size: 12,
            family: "sans-serif",
            weight: "bold",
          },
        });

        // Add area label at centroid
        measurementsLayer.add(
          new Graphic({
            geometry: new Point({
              x: centroidX,
              y: centroidY,
              spatialReference: spatialReference,
            }),
            symbol: areaLabel,
          })
        );
      } catch (error) {
        console.error("Error adding measurements:", error);
      }
    };

    const handleQuery = (geometry) => {
      try {
        const queryObject = new Query({
          geometry,
          spatialRelationship: "intersects",
          outFields: ["*"],
          returnGeometry: true,
        });

        // Using the imported query module directly
        return query
          .executeQueryJSON(
            `${window.mapUrl}/${getLayerId(map.__mapInfo, "Landbase_Parcel")}`,
            queryObject
          )
          .then((result) => {
            if (result.features?.length > 0) {
              return getFeatureDomainName(
                result.features,
                getLayerId(map.__mapInfo, "Landbase_Parcel")
              ).then((rfeatures) => {
                const highlightSymbol = new SimpleFillSymbol({
                  color: [0, 0, 0, 0],
                  outline: new SimpleLineSymbol({
                    color: [0, 0, 0],
                    width: 3,
                  }),
                });

                graphicsLayer.removeAll();

                result.features.forEach((land) => {
                  const highlightGraphic = new Graphic({
                    geometry: land.geometry,
                    symbol: highlightSymbol,
                  });
                  graphicsLayer.add(highlightGraphic);

                  // Add measurements to the selected land parcel
                  addMeasurementsToPolygon(land.geometry);
                });

                return rfeatures;
              });
            } else {
              console.log("No intersecting lands found.");
              measurementsLayer.removeAll();
              return [];
            }
          })
          .catch((error) => {
            console.error("Error executing query:", error);
            throw error;
          });
      } catch (error) {
        console.error("Error in query setup:", error);
        throw error;
      }
    };

    sketchViewModel.on("create", (event) => {
      if (event.state === "start") {
        try {
          // Create a buffer polygon around the initial point
          const point = new Point({
            x: event.graphic.geometry.rings[0][0][0],
            y: event.graphic.geometry.rings[0][0][1],
            spatialReference: event.graphic.geometry.spatialReference,
          });

          const bufferPolygon = createBufferPolygon(point);
          handleQuery(bufferPolygon)
            .then((rfeatures) => {
              console.log("Start point query result:", rfeatures);
              if (onResult) onResult(rfeatures);
            })
            .catch((error) => {
              console.error("Error during start point query:", error);
            });
        } catch (error) {
          console.error("Error creating buffer polygon:", error);
        }
      } else if (
        event.state === "complete" ||
        event.toolEventInfo?.type === "vertex-add"
      ) {
        try {
          handleQuery(event.graphic.geometry)
            .then((rfeatures) => {
              console.log("Create result:", rfeatures);
              if (onResult) onResult(rfeatures);

              // Also add measurements to the sketch polygon itself
              //     addMeasurementsToPolygon(event.graphic.geometry);
            })
            .catch((error) => {
              console.error("Error during create:", error);
            });
        } catch (error) {
          console.error("Error during create setup:", error);
        }
      }
    });

    sketchViewModel.on("update", (event) => {
      if (event.state === "complete" && !event.aborted) {
        try {
          handleQuery(event.graphics[0].geometry)
            .then((rfeatures) => {
              console.log("Update result:", rfeatures);
              if (onResult) onResult(rfeatures);
            })
            .catch((error) => {
              console.error("Error during update:", error);
            });
        } catch (error) {
          console.error("Error during update setup:", error);
        }
      }
    });
  };

  const Cleanup = () => {
    if (props.sketch) {
      props.sketch.destroy();
      props.setSketch(null);
    }

    // Clean up measurements layer when component unmounts or tool changes
    if (props.map) {
      const measurementsLayer = props.map.findLayerById("MeasurementsLayer");
      const SketchLayer = props.map.findLayerById("SelectGraphicLayer");
      const graphicsLayer = props.map.findLayerById("highlightGraphicLayer");
      if (measurementsLayer) {
        measurementsLayer.removeAll();
      }
      if (SketchLayer) {
        SketchLayer.removeAll();
      }
      if (graphicsLayer) {
        graphicsLayer.removeAll();
      }
    }
  };
  // Use useEffect to handle the "select" tool activation
  // useEffect(() => {
  //   if (props.activeTool === "select" && props.map) {
  //     if (props.sketch) {
  //       props.setSketch(null);
  //     }

  //     enablePolygonSelectionMode(props.map, (rfeatures) => {
  //       // Handle the results here
  //       props.setSelectedFeatures(rfeatures);
  //       console.log("Selected features:", rfeatures);
  //     });
  //   } else {
  //   //  Cleanup();
  //   }

  //   // Cleanup function

  //   // return
  // }, [props.activeTool, props.map]);

  // // Return null for the "select" tool to prevent rendering issues
  // if (props.activeTool === "select") {
  //   return  <UpdateLand
  //         languageState={props.languageState}
  //         mainData={props.mainData}
  //         map={props.map}
  //         activeTool={props.activeTool}
  //         closeToolsData={props.closeToolsData}
  //         openToolsData={props.openToolsData}
  //         openToolData={props.openToolData}
  //         selectedFeatures ={selectedFeatures}
  //       />
  // }

  return (
    <div className="allToolsPage">
      {props.activeTool === "menu" ? (
        <ToolsMenu
          activeTool={props.activeTool}
          closeToolsData={props.closeToolsData}
          openToolsData={props.openToolsData}
          openToolData={props.openToolData}
        />
      ) : props.activeTool === "inquiry" ? (
        props.map && (
          <InquiryTool
            map={props.map}
            mainData={props.mainData}
            languageState={props.languageState}
            setPopupInfo={props.setPopupInfo}
            popupInfo={props.popupInfo}
            activeTool={props.activeTool}
            closeToolsData={props.closeToolsData}
            openToolsData={props.openToolsData}
            openToolData={props.openToolData}
          />
        )
      ) : props.activeTool === "smallMap" ? (
        <SmallMap
          map={props.map}
          activeTool={props.activeTool}
          closeToolsData={props.closeToolsData}
          openToolsData={props.openToolsData}
          openToolData={props.openToolData}
        />
      ) : props.activeTool === "layersMenu" ? (
        <LayersMenu
          languageState={props.languageState}
          mainData={props.mainData}
          map={props.map}
          activeTool={props.activeTool}
          closeToolsData={props.closeToolsData}
          openToolsData={props.openToolsData}
          openToolData={props.openToolData}
        />
      ) : props.activeTool === "compareLayers" ? (
        <CompareLayers
          languageState={props.languageState}
          mainData={props.mainData}
          map={props.map}
          activeTool={props.activeTool}
          closeToolsData={props.closeToolsData}
          openToolsData={props.openToolsData}
          openToolData={props.openToolData}
        />
      ) : props.activeTool === "googleMaps" ? (
        <GoogleMaps
          map={props.map}
          activeTool={props.activeTool}
          closeToolsData={props.closeToolsData}
          openToolsData={props.openToolsData}
          openToolData={props.openToolData}
        />
      ) : props.activeTool === "traffic" ? (
        <Traffic
          map={props.map}
          activeTool={props.activeTool}
          closeToolsData={props.closeToolsData}
          openToolsData={props.openToolsData}
          openToolData={props.openToolData}
        />
      ) : props.activeTool === "print" ? (
        <Print
          languageState={props.languageState}
          mainData={props.mainData}
          map={props.map}
          activeTool={props.activeTool}
          closeToolsData={props.closeToolsData}
          openToolsData={props.openToolsData}
          openToolData={props.openToolData}
        />
      ) : props.activeTool === "legend" ? (
        <LegendComponent
          languageState={props.languageState}
          mainData={props.mainData}
          map={props.map}
          activeTool={props.activeTool}
          closeToolsData={props.closeToolsData}
          openToolsData={props.openToolsData}
          openToolData={props.openToolData}
          Cleanup={Cleanup}
        />
      ) : props.activeTool === "selectToUpdate" && props.selectedFeatures.length > 0 ? (
        <UpdateLand
          languageState={props.languageState}
          mainData={props.mainData}
          map={props.map}
          activeTool={props.activeTool}
          closeToolsData={props.closeToolsData}
          openToolsData={props.openToolsData}
          openToolData={props.openToolData}
          selectedFeatures={props.selectedFeatures}
          Cleanup={Cleanup}
        />
      ) : null}
    </div>
  );
};

export default AllTools;
