import React from "react";
import { Table } from "react-bootstrap";
import { useTranslation } from "react-i18next";
import ReactToPrint from "react-to-print";
import logo from "../../../assets/images/amanaLogo.png";
import visionLogo from "../../../assets/images/vision.png";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPrint } from "@fortawesome/free-solid-svg-icons";
export default function DataTable({
  title,
  chartsData,
  hasArea,
  isIncident,
  boundAdmin,
}) {
  const { t } = useTranslation("print", "dashboard");
  const componentRef = React.useRef(null);
  // const handlePrint = useReactToPrint({
  //   content: () => componentRef.current,
  // });
  const reqTitle = React.useMemo(() => {
    if (!isIncident) {
      return title === "MUNICIPALITY_NAME"
        ? t("dashboard:mainMunicipalities")
        : title === "SUB_MUNICIPALITY_NAME"
        ? t("dashboard:secMunicipalities")
        : title === "PLAN_NO"
        ? t("dashboard:plans")
        : title === "DISTRICT_NAME"
        ? t("dashboard:districts")
        : t("dashboard:sumForLayer");
    } else {
      if (
        boundAdmin?.selectedPreNeededBoundary &&
        boundAdmin?.preNeededBoundariesArr &&
        title === "DISTRICT_NAME"
      ) {
        let reqBoundAdmin =
          boundAdmin?.preNeededBoundariesArr?.find(
            (i) => i.value === boundAdmin?.selectedPreNeededBoundary
          )?.label || "";
        return t("dashboard:PlanForIncidentPerMun") + ` (${reqBoundAdmin})`;
      } else
        return title === "MUNICIPALITY_NAME"
          ? t("dashboard:mainMunicipalitiesForIncident")
          : t("dashboard:incidentsTotal");
    }
  }, [title, boundAdmin?.selectedPreNeededBoundary]);
  return (
    <div className="dashDataPage" ref={componentRef}>
      <nav className="printNav printOnly">
        <img
          className="ml-2"
          alt="logo"
          src={logo}
          style={{
            width: "90px",
            position: "absolute",
            right: "0",
            top: "50%",
            transform: " translate(-50%, -50%)",
          }}
        />{" "}
        <img
          className="ml-2"
          alt="logo"
          src={visionLogo}
          style={{
            width: "100px",
            position: "absolute",
            top: "50%",
            transform: " translate(-50%, -50%)",
            left: "7vw",
            height: "80px",
          }}
        />
      </nav>{" "}
      <h4>{reqTitle}</h4>
      {/* <ReactToPrint
        trigger={() =>  */}
      <button
      className="printBtn mt-5 mx-4 py-2"
      size="large"
        htmlType="submit"
        onClick={()=>window.print()}
        >
        <FontAwesomeIcon
          icon={faPrint}
          className="mx-2"
          style={{ fontSize: "15px" }}
        />

        {t("print:print")}
      </button>
    {/* }
    content={() => componentRef.current}
  /> */}
      <Table className="table  dashDataTable">
        <thead>
          {hasArea ? <th>{t("dashboard:totalArea")} </th> : null}
          <th>{t("dashboard:totalCount")}</th>
          <th>{t("dashboard:name")}</th>
        </thead>

        <tbody>
          {typeof chartsData.count === "object" && hasArea ? (
            chartsData.count.map((item, index) => (
              <tr key={"tbl" + index}>
                <td>
                  {parseFloat(
                    chartsData.areaOrLength.value[index]["AREA"]
                  ).toFixed(2)}
                </td>
                <td>{item["COUNT"]}</td>
                <td>{item[title] ? item[title] : t("dashboard:NotDefined")}</td>
              </tr>
            ))
          ) : typeof chartsData.count === "object" && !hasArea ? (
            chartsData.count.map((item, index) => (
              <tr key={"tbl" + index}>
                <td>{item["COUNT"]}</td>
                <td>{item[title] ? item[title] : t("dashboard:NotDefined")}</td>
              </tr>
            ))
          ) : typeof chartsData.count !== "object" && hasArea ? (
            <tr>
              <td>{parseFloat(chartsData.areaOrLength.value).toFixed(2)}</td>
              <td>{chartsData.count}</td>
              <td>{t("dashboard:all")}</td>
            </tr>
          ) : (
            <tr>
              <td>{chartsData.count}</td>
              <td>{t("dashboard:all")}</td>
            </tr>
          )}
        </tbody>
      </Table>
    </div>
  );
}
