import React, { useEffect, useState } from "react";
import { Row, Col, Button, message } from "antd";
import { toArabic } from "arabic-digits";
import axios from "axios";
// import { layersSetting } from "../../helper/layers";
import { useTranslation } from "react-i18next";

import ExportCSVFile from "../../tables/Modals/PlanLandsData/ExportCSVFile";
import {
  faAngleDoubleDown,
  faSearchPlus,
  faExpandArrowsAlt,
  faFileCsv,
} from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Tooltip } from "@mui/material";
import zoom from "../../assets/icons/zoom.svg";
import expand from "../../assets/icons/expand.svg";
import fn1 from "../../assets/icons/fn1.svg";
import fn2 from "../../assets/icons/fn2.svg";
import googleLocation from "../../assets/icons/google.svg";
import {
  clearCanvasLine,
  clearGraphicLayer,
  drawLine,
  getFeatureDomainName,
  highlightFeature,
  navigateToGoogle,
  queryTask,
  zoomToFeatureByObjectId,
  convertToArabic,
  zoomToFeatureBySpatialID,
  zoomToFeatureByFilter,
  showLoading,
} from "../../helper/common_func";
import { PARCEL_LANDS_LAYER_NAME } from "../../helper/constants";
import { alphbetNumbers, getNumbersInStringsByValue, megawrahNumbersInString, municipilitiesForRoyal, royalPrivateLandsPrintFields, subdivisionTypes } from "../../helper/layers";

export default function OuterSearchResultsMenu(props) {
  const { t } = useTranslation("map", "print", "layers", "common");
  const [result, setShownData] = useState(null);
  const [exportedData, setExportedData] = React.useState({
    dataSet: [],
    columns: [],
    labels: [],
    layerName: "",
    whereClause: "",
  });
  let isPrivateLandOrRoyalLand = ["PARCEL_PRIVACY", "LGR_ROYAL", "SALES_LANDS"].includes(
    props.outerSearchResult?.layerName
  );
  let isPrivateLand = ["PARCEL_PRIVACY"].includes(
    props.outerSearchResult?.layerName
  );
  // const [landBaseParcelData, setLandBaseParcelData] = React.useState();
  const navigateGeometry = (index) => {


    let featDataGeom = isPrivateLandOrRoyalLand
      ? props.landBaseParcelData?.geometry
      : result[index]?.geometry;
    if (featDataGeom) {
      if (featDataGeom?.rings?.length || featDataGeom?.paths?.length || featDataGeom?.x)
        navigateToGoogle(
          featDataGeom.latitude || featDataGeom.centroid.latitude,
          featDataGeom.longitude || featDataGeom.centroid.longitude
        );
      else message.open({
        type: "info",
        content: ["PARCEL_PRIVACY", "LGR_ROYAL", "Landbase_Parcel", "SALES_LANDS"].includes(
          props.outerSearchResult?.layerName
        ) ? t("common:doesntReflectGDB") : t('common:featureDoesntReflectGDB'),
      });
    } else {
      if (!isPrivateLandOrRoyalLand)
        zoomToFeatureByObjectId(result[index], props.map, false, (feature) => {
          result[index].geometry = feature.geometry;
          if (feature?.rings?.length || feature?.paths?.length || feature?.x)
            navigateToGoogle(
              result[index].geometry.latitude ||
              result[index].geometry.centroid.latitude,
              result[index].geometry.longitude ||
              result[index].geometry.centroid.longitude
            );
          else message.open({
            type: "info",
            content: ["PARCEL_PRIVACY", "LGR_ROYAL", "Landbase_Parcel", "SALES_LANDS"].includes(
              props.outerSearchResult?.layerName
            ) ? t("common:doesntReflectGDB") : t('common:featureDoesntReflectGDB'),
          });
        });
      else {
        if (!result[index]?.id)
          message.open({
            type: "info",
            content: ["PARCEL_PRIVACY", "LGR_ROYAL", "Landbase_Parcel", "SALES_LANDS"].includes(
              props.outerSearchResult?.layerName
            ) ? t("common:doesntReflectGDB") : t('common:featureDoesntReflectGDB'),
          });
        else {
          if ((result.list || result)[index].geometry) {
            zoomToFeatureByObjectId(
              {
                geometry: (result.list || result)[index].geometry
              },
              props.map,
              false,
            );
          } else zoomToFeatureBySpatialID(
            result[index],
            PARCEL_LANDS_LAYER_NAME,
            props.map,
            false,
            (feature) => {
              // props.setLandBaseParcelData(feature);
              result[index].geometry = feature.geometry;
              if (feature?.rings?.length || feature?.paths?.length || feature?.x)
                navigateToGoogle(
                  result[index].geometry.latitude ||
                  result[index].geometry.centroid.latitude,
                  result[index].geometry.longitude ||
                  result[index].geometry.centroid.longitude
                );
              else message.open({
                type: "info",
                content: ["PARCEL_PRIVACY", "LGR_ROYAL", "Landbase_Parcel", "SALES_LANDS"].includes(
                  props.outerSearchResult?.layerName
                ) ? t("common:doesntReflectGDB") : t('common:featureDoesntReflectGDB'),
              });
            },
            () =>
              message.open({
                type: "info",
                content: t("common:retrievError"),
              })
          );
        }
      }
    }
  };

  if (result && result.length && !result.find((x) => !(x.geometry?.rings?.length || x.geometry?.paths?.length || x.geometry?.x))) {
    highlightFeature(result, props.map, {
      layerName: "ZoomGraphicLayer",
      isZoom: true,
      zoomDuration: 1000,
      isDashStyle: true,
    });
  }

  useEffect(() => {
    if (props.outerSearchResult && props.outerSearchResult.length == 1) {
      props.outerOpenResultdetails(props.outerSearchResult[0]);
    }
    console.log("set LandBaseParcelData with undefined");
    // props.setLandBaseParcelData();

    clearGraphicLayer("ZoomGraphicLayer", props.map);
    let features = props.outerSearchResult?.list;
    if (features?.length) {
      zoomToAll(features);
    }
    setShownData(props.outerSearchResult);
    return () => {
      clearGraphicLayer("ZoomGraphicLayer", props.map);
      clearGraphicLayer("SelectGraphicLayer", props.map);
      setShownData(null);
    };
  }, []);

  const gotoFeature = (feature) => {
    /*if (feature.geometry) {
      highlightFeature(feature, props.map, {
        layerName: "SelectGraphicLayer",
      });
    }*/
  };

  const onMouseMoveOnFeature = (feature, e) => {
    if (feature.geometry?.rings?.length || feature.geometry?.paths?.length || feature.geometry?.x) {
      highlightFeature(feature, props.map, {
        layerName: "SelectGraphicLayer",
        isAnimatedLocation: true,
      });
      drawLine({ feature: feature, map: props.map, event: e });
    }
  };

  const clearFeatures = () => {
    props.map.findLayerById("SelectGraphicLayer").removeAll();
    clearCanvasLine();
  };

  const getDisplayField = (attributes) => {
    let layersSetting = props.mainData.layers;
    return attributes[layersSetting[attributes["layerName"]].displayField];
  };

  const searchForMoreData = async (e) => {

    if (!isPrivateLandOrRoyalLand) {
      let queryObj = { ...result.queryObj };
      queryObj.start = result.list.length;

      queryTask({
        ...queryObj,
        callbackResult: ({ features }) => {
          if (features.length) {
            getFeatureDomainName(features, queryObj.layerdId).then((res) => {
              let mappingRes = res.map((f) => {
                return {
                  layerName: queryObj.layerName,
                  id: f.attributes["OBJECTID"],
                  ...f.attributes,
                  geometry: f.geometry,
                };
              });

              setShownData({
                ...result,
                queryObj: queryObj,
                list: result.list.concat(mappingRes),
              });
            });
          }
        },
        callbackError(error) {
          console.log({ error });
          message.open({
            type: "info",
            content: t("common:retrievError"),
          })
        },
      });
    } else {
      let queryObj = { ...result };

      // let isPrivateLand = ["PARCEL_PRIVACY"].includes(
      //   props.outerSearchResult?.layerName
      // );
      // if (!isPrivateLand) return;
      if (queryObj.currentPage >= queryObj.totalPages) return;
      // pivate land layer
      try {
        let url = queryObj.fetchURL + "&pagNum=" + parseInt(queryObj.currentPage) + 1;
        let token = props.mainData.user?.token;

        showLoading(true);

        let { data } = await axios.get(url, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });
        let features = data?.results || [];

        let mappingRes = features.map((f) => {
          return {
            layerName: queryObj.layerName,
            id: f["spatial_id"] || f["parcel_part_no"],
            ...f,
            MUNICIPALITY_NAME: queryObj.MUNICIPALITY_NAME,
            MUNICIPALITY_NAME_Code: queryObj.MUNICIPALITY_NAME_Code,
            geometry: null,
          };
        });

        setShownData({
          ...result,
          // queryObj: queryObj,
          currentPage: result.currentPage + 1,
          list: result.list.concat(mappingRes),
        });
      } catch (err) {
        console.log({ error: err });
        showLoading(false);

        message.open({
          type: "info",
          content: t("common:retrievError"),
        })
      }
    }
  };

  const openFeatureDetails = (attributes) => {
    //TODO: in case of royal or private lands fetch arc server to get land data and set its attributes if found
    props.outerOpenResultdetails(attributes);
    clearCanvasLine();
  };

  const zoomToAll = (feats) => {
    clearGraphicLayer("ZoomGraphicLayer", props.map);
    if (feats.length && feats.find((d) => d.geometry)) {
      let features = feats.filter((d) => {
        if (d.geometry?.rings?.length || d.geometry?.paths?.length || d.geometry?.x) return d;
        else return;
      });
      if (features?.length)
        highlightFeature(features, props.map, {
          layerName: "SelectGraphicLayer",
          isZoom: true,
          zoomDuration: 1000,
        });
    }
  };
  async function exportCSVFile(evt, item) {
    if (exportedData.whereClause && evt.target !== evt.currentTarget && evt.currentTarget.querySelector("#main-elem-for-export")) return;
    if (props.outerSearchResult?.whereClause) {
      if (item.layerName === 'PARCEL_PRIVACY') {
        let queryObj = { ...result };

        if (queryObj.currentPage >= queryObj.totalPages) {

          let settedData = props.outerSearchResult.list.map(it => {
            if (it.decision_dateg) it.decision_dateg = it.decision_dateg.split(" ")[0];
            if (it.decision_date_hijri) {
              let year = it.decision_date_hijri.slice(0, 4);
              let month = it.decision_date_hijri.slice(4, 6);
              let day = it.decision_date_hijri.slice(6);
              it.decision_date_hijri = `${day}/${month}/${year}`;
            }
            return it
          })
          setExportedData({
            dataSet: settedData,
            columns: royalPrivateLandsPrintFields[item.layerName].columns,
            labels: royalPrivateLandsPrintFields[item.layerName].labels?.map(
              (i) =>
                (i).match(
                  "[\u0600-\u06ff]|[\u0750-\u077f]|[\ufb50-\ufbc1]|[\ufbd3-\ufd3f]|[\ufd50-\ufd8f]|[\ufd92-\ufdc7]|[\ufe70-\ufefc]|[\uFDF0-\uFDFD]"
                )
                  ? i
                  : t(`layers:${i}`)
            ),
            layerName: item.layerName,
            whereClause: item.whereClause,
            fetchURL: queryObj.fetchURL
          });
        } else {

          try {
            let url = queryObj.fetchURL + "&isExcel=true";
            let token = props.mainData.user?.token;

            showLoading(true);

            let { data } = await axios.get(url, {
              headers: {
                Authorization: `Bearer ${token}`,
              },
            });
            let features = data?.results || [];

            let list = features.map((f) => {
              return {
                layerName: queryObj.layerName,
                id: f["spatial_id"] || f["parcel_part_no"],
                ...f,
                MUNICIPALITY_NAME: queryObj.MUNICIPALITY_NAME,
                MUNICIPALITY_NAME_Code: queryObj.MUNICIPALITY_NAME_Code,
                geometry: null,
              };
            });
            let settedData = list.map(it => {
              if (it.decision_dateg) it.decision_dateg = it.decision_dateg.split(" ")[0];
              if (it.decision_date_hijri) {
                let year = it.decision_date_hijri.slice(0, 4);
                let month = it.decision_date_hijri.slice(4, 6);
                let day = it.decision_date_hijri.slice(6);
                it.decision_date_hijri = `${day}/${month}/${year}`;
              }
              return it
            })
            setExportedData({
              dataSet: settedData,
              columns: royalPrivateLandsPrintFields[item.layerName].columns,
              labels: royalPrivateLandsPrintFields[item.layerName].labels?.map(
                (i) =>
                  (i).match(
                    "[\u0600-\u06ff]|[\u0750-\u077f]|[\ufb50-\ufbc1]|[\ufbd3-\ufd3f]|[\ufd50-\ufd8f]|[\ufd92-\ufdc7]|[\ufe70-\ufefc]|[\uFDF0-\uFDFD]"
                  )
                    ? i
                    : t(`layers:${i}`)
              ),
              layerName: item.layerName,
              whereClause: item.whereClause,
              fetchURL: queryObj.fetchURL
            });

          } catch (err) {
            console.log({ error: err });
            showLoading(false);

            message.open({
              type: "info",
              content: t("common:retrievError"),
            })
          }
        }
      } else {
        let queryObj = { ...result };
        if (queryObj.currentPage >= queryObj.totalPages) {

          let settedData = props.outerSearchResult.list;
          setExportedData({
            dataSet: settedData,
            columns: royalPrivateLandsPrintFields[item.layerName].columns,
            labels: royalPrivateLandsPrintFields[item.layerName].labels?.map(
              (i) =>
                (i).match(
                  "[\u0600-\u06ff]|[\u0750-\u077f]|[\ufb50-\ufbc1]|[\ufbd3-\ufd3f]|[\ufd50-\ufd8f]|[\ufd92-\ufdc7]|[\ufe70-\ufefc]|[\uFDF0-\uFDFD]"
                )
                  ? i
                  : t(`layers:${i}`)
            ),
            layerName: item.layerName,
            whereClause: item.whereClause,
            fetchURL: queryObj.fetchURL

          });
        } else {
          try {
            let url = queryObj.fetchURL + "&isExcel=true";
            let token = props.mainData.user?.token;

            showLoading(true);

            let { data } = await axios.get(url, {
              headers: {
                Authorization: `Bearer ${token}`,
              },
            });
            let features = data?.results || [];

            let list = features.map((f) => {
              return {
                layerName: queryObj.layerName,
                id: f["spatial_id"] || f["parcel_part_no"],
                ...f,
                MUNICIPALITY_NAME: queryObj.MUNICIPALITY_NAME,
                MUNICIPALITY_NAME_Code: queryObj.MUNICIPALITY_NAME_Code,
                geometry: null,
              };
            });
            let settedData = list;
            setExportedData({
              dataSet: settedData,
              columns: royalPrivateLandsPrintFields[item.layerName].columns,
              labels: royalPrivateLandsPrintFields[item.layerName].labels?.map(
                (i) =>
                  (i).match(
                    "[\u0600-\u06ff]|[\u0750-\u077f]|[\ufb50-\ufbc1]|[\ufbd3-\ufd3f]|[\ufd50-\ufd8f]|[\ufd92-\ufdc7]|[\ufe70-\ufefc]|[\uFDF0-\uFDFD]"
                  )
                    ? i
                    : t(`layers:${i}`)
              ),
              layerName: item.layerName,
              whereClause: item.whereClause,
              fetchURL: queryObj.fetchURL
            });

          } catch (err) {
            console.log({ error: err });
            showLoading(false);

            message.open({
              type: "info",
              content: t("common:retrievError"),
            })
          }
        }
      }
    } else {
      //show a message to user
      message.open({
        type: "info",
        content: t("common:noDataForExtract"),
      });
      return;
    }
  }

  const getWhereClauseFromRoyalPrivacyLandInfo = (landInfo, layerName) => {
    if (layerName === "LGR_ROYAL") {
      let where = "USING_SYMBOL NOT LIKE '%خ%'";
      let fieldsNames = ["city_name", "plan_no", "block_no", "land_no", "district_desc", "center_desc", "category_desc", "parcel_part_no"];
      let centerDescValue = landInfo['center_desc'];
      let districtDescValue = landInfo['district_desc'];
      fieldsNames.forEach((fieldName) => {
        let munCode;
        // 1- mun code 
        let fieldValue = landInfo[fieldName];
        if (fieldName === "city_name" && fieldValue && fieldValue !== "بدون") {
          if (fieldValue) munCode = municipilitiesForRoyal.find(i => {
            if (fieldValue && fieldValue.includes(i.name)) return i;
            return i.name.includes(fieldValue);
          })?.GISCode;
          if (munCode) where += `${where ? " AND " : ""}MUNICIPALITY_NAME=${munCode} `;
        } else if (fieldName === 'plan_no' && fieldValue && fieldValue !== "بدون") {
          if (fieldValue) {
            where += `${where ? " AND " : ""}PLAN_NO='${fieldValue}' `;
          }
        } else if (fieldName === "land_no" && fieldValue && fieldValue !== "بدون") {
          if (fieldValue) {
            where += `${where ? " AND " : ""}PARCEL_PLAN_NO='${fieldValue}' `;
          }
        } else if (fieldName === "block_no" && fieldValue && fieldValue !== "0" && fieldValue !== "بدون") {
          if (fieldValue) {
            where += `${where ? " AND " : ""}PARCEL_BLOCK_NO='${fieldValue}' `;
          }
        }
        else if (fieldName === 'district_desc' && fieldValue && fieldValue !== "0" && fieldValue !== "بدون" && !centerDescValue) {
          let megawarahWhere = `SUBDIVISION_TYPE=${subdivisionTypes.megawrah} AND SUBDIVISION_DESCRIPTION LIKE '%مجاور%' `;
          let numbers = fieldValue.match(/\d+/g);
          let megawrahObj = megawrahNumbersInString.find(i => numbers[0] === i.value);
          if (typeof megawrahObj.like === 'string') {
            megawarahWhere += `${megawarahWhere ? " AND " : ""}  SUBDIVISION_DESCRIPTION LIKE '%${megawrahObj.like}%'`
          } else {
            // like: [{OR:["أحد","حادي"]}, {AND:"عشر"}]

            if (megawrahObj.like?.OR && !megawrahObj.like?.AND) {
              megawarahWhere += (megawarahWhere ? " AND " : "") + megawrahObj.like?.OR.map(word => `(SUBDIVISION_DESCRIPTION LIKE '%${word}%')`).join(" OR ");
            } else if (megawrahObj.like?.OR && megawrahObj.like?.AND) {
              megawarahWhere += (megawarahWhere ? " AND " : "") + megawrahObj.like?.OR.map(word => `(SUBDIVISION_DESCRIPTION LIKE '%${word}%' AND SUBDIVISION_DESCRIPTION LIKE '%${megawrahObj.like.AND}%')`).join(" OR ");

            }
          }
          if (megawrahObj.NOT) {
            megawarahWhere += ` ${megawarahWhere ? " AND " : ""}  ( SUBDIVISION_DESCRIPTION NOT LIKE '%${megawrahObj.NOT}%')`;
          }
          where += (where ? " AND " : "") + `(${megawarahWhere})`
        } else if (fieldName === 'center_desc' && fieldValue && fieldValue !== "بدون") {
          // todo:
          let centerSplitStr = fieldValue.split(" ")?.slice(1);
          let centerNumber = centerSplitStr?.join(" ");
          let getStringNumberByValue = getNumbersInStringsByValue(centerNumber);
          let centerWhere = "";
          if (typeof getStringNumberByValue.like === 'string') {
            centerWhere = ` (SUBDIVISION_DESCRIPTION LIKE '%${getStringNumberByValue.like}%')`
          } else {
            // like: [{OR:["أحد","حادي"]}, {AND:"عشر"}]

            if (getStringNumberByValue.like?.OR && !getStringNumberByValue.like?.AND) {
              centerWhere = (centerWhere ? " AND " : "") + getStringNumberByValue.like?.OR.map(word => `(SUBDIVISION_DESCRIPTION LIKE '%${word}%')`).join(" OR ");
            } else if (getStringNumberByValue.like?.OR && getStringNumberByValue.like?.AND) {
              centerWhere = (centerWhere ? " AND " : "") + getStringNumberByValue.like?.OR.map(word => `(SUBDIVISION_DESCRIPTION LIKE '%${word}%' AND SUBDIVISION_DESCRIPTION LIKE '%${getStringNumberByValue.like.AND}%')`).join(" OR ");

            }
          }
          if (getStringNumberByValue.NOT && !districtDescValue) {
            centerWhere += ` ${centerWhere ? " AND " : ""}( SUBDIVISION_DESCRIPTION NOT LIKE '%${getStringNumberByValue.NOT}%')`;
          }
          centerWhere += ` ${centerWhere ? " AND " : ""} SUBDIVISION_TYPE=${subdivisionTypes.megawrah} AND ( ${districtDescValue ? `SUBDIVISION_DESCRIPTION LIKE '%مجاور%'` : ``} OR (SUBDIVISION_DESCRIPTION LIKE '%حي%' OR SUBDIVISION_DESCRIPTION LIKE '%حى%'))`;

          if (districtDescValue) {
            let megawarahWhere = ``;
            let numbers = districtDescValue.match(/\d+/g);
            let megawrahObj = megawrahNumbersInString.find(i => numbers[0] === i.value);
            if (typeof megawrahObj.like === 'string') {
              megawarahWhere += `${megawarahWhere ? " AND " : ""}  SUBDIVISION_DESCRIPTION LIKE '%${megawrahObj.like}%'`
            } else {
              // like: [{OR:["أحد","حادي"]}, {AND:"عشر"}]

              if (megawrahObj.like?.OR && !megawrahObj.like?.AND) {
                megawarahWhere += (megawarahWhere ? " AND " : "") + megawrahObj.like?.OR.map(word => `(SUBDIVISION_DESCRIPTION LIKE '%${word}%')`).join(" OR ");
              } else if (megawrahObj.like?.OR && megawrahObj.like?.AND) {
                megawarahWhere += (megawarahWhere ? " AND " : "") + megawrahObj.like?.OR.map(word => `(SUBDIVISION_DESCRIPTION LIKE '%${word}%' AND SUBDIVISION_DESCRIPTION LIKE '%${megawrahObj.like.AND}%')`).join(" OR ");

              }
            }
            if (megawrahObj.NOT) {
              // megawarahWhere += ` ${megawarahWhere ? " AND " : ""}  ( SUBDIVISION_DESCRIPTION NOT LIKE '%${megawrahObj.NOT}%')`;
            }
            centerWhere += (centerWhere ? " AND " : "") + `(${megawarahWhere})`;
            where += (where ? " AND " : "") + `(${centerWhere})`;
          }
        } else if (fieldName === 'category_desc' && fieldValue !== "بدون" && fieldValue) {
          // todo:
          let categoryAlphabet = fieldValue.match(/\(\s*(.)\s*\)/);
          let valueFromAlphabetCatregory = fieldValue;
          if (categoryAlphabet.length) {

            valueFromAlphabetCatregory = alphbetNumbers.find(al => {
              if (typeof al.name === 'string') return al.name === categoryAlphabet[1];
              else {
                return al.name.includes(categoryAlphabet[1])
              }
            });
          }
          where += `${where ? " AND (" : "("} (SUBDIVISION_TYPE=${subdivisionTypes.category} OR SUBDIVISION_TYPE=${subdivisionTypes.district}) AND (${typeof valueFromAlphabetCatregory.name === 'string' ? `SUBDIVISION_DESCRIPTION LIKE '%${valueFromAlphabetCatregory.name}%'` : "( " + valueFromAlphabetCatregory.name.map(n => `SUBDIVISION_DESCRIPTION LIKE '%${n}%'`).join(" OR ") + " ) "} OR SUBDIVISION_DESCRIPTION LIKE '%${valueFromAlphabetCatregory.value}%' OR SUBDIVISION_DESCRIPTION LIKE '%فئة%' OR SUBDIVISION_DESCRIPTION LIKE '%فئه%') )`;

        }
      })
      return where;
    } else if (layerName === "SALES_LANDS") {
      let where = "";
      let fieldsNames = ["city_name", "plan_no", "block_no", "land_no", "spatial_id"];
      // let centerDescValue = landInfo['center_desc'];
      // let districtDescValue = landInfo['district_desc'];
      fieldsNames.forEach((fieldName) => {
        let munCode;
        // 1- mun code 
        let fieldValue = landInfo[fieldName];
        if (fieldName === "city_name" && fieldValue && fieldValue !== "بدون") {
          if (fieldValue) munCode = municipilitiesForRoyal.find(i => {
            if (fieldValue && fieldValue.includes(i.name)) return i;
            return i.name.includes(fieldValue);
          })?.GISCode;
          if (munCode) where += `${where ? " AND " : ""}MUNICIPALITY_NAME=${munCode} `;
        } else if (fieldName === 'plan_no' && fieldValue && fieldValue !== "بدون") {
          if (fieldValue) {
            where += `${where ? " AND " : ""}PLAN_NO='${fieldValue}' `;
          }
        } else if (fieldName === "land_no" && fieldValue && fieldValue !== "بدون") {
          if (fieldValue) {
            where += `${where ? " AND " : ""}PARCEL_PLAN_NO='${fieldValue}' `;
          }
        } else if (fieldName === "block_no" && fieldValue && fieldValue !== "0" && fieldValue !== "بدون") {
          if (fieldValue) {
            where += `${where ? " AND " : ""}PARCEL_BLOCK_NO='${fieldValue}' `;
          }
        }
        // else if (fieldName === 'district_desc' && fieldValue && fieldValue !== "0" && fieldValue !== "بدون" && !centerDescValue) {
        //   let megawarahWhere = `SUBDIVISION_TYPE=${subdivisionTypes.megawrah} AND SUBDIVISION_DESCRIPTION LIKE '%مجاور%' `;
        //   let numbers = fieldValue.match(/\d+/g);
        //   let megawrahObj = megawrahNumbersInString.find(i => numbers[0] === i.value);
        //   if (typeof megawrahObj.like === 'string') {
        //     megawarahWhere += `${megawarahWhere ? " AND " : ""}  SUBDIVISION_DESCRIPTION LIKE '%${megawrahObj.like}%'`
        //   } else {
        //     // like: [{OR:["أحد","حادي"]}, {AND:"عشر"}]

        //     if (megawrahObj.like?.OR && !megawrahObj.like?.AND) {
        //       megawarahWhere += (megawarahWhere ? " AND " : "") + megawrahObj.like?.OR.map(word => `(SUBDIVISION_DESCRIPTION LIKE '%${word}%')`).join(" OR ");
        //     } else if (megawrahObj.like?.OR && megawrahObj.like?.AND) {
        //       megawarahWhere += (megawarahWhere ? " AND " : "") + megawrahObj.like?.OR.map(word => `(SUBDIVISION_DESCRIPTION LIKE '%${word}%' AND SUBDIVISION_DESCRIPTION LIKE '%${megawrahObj.like.AND}%')`).join(" OR ");

        //     }
        //   }
        //   if (megawrahObj.NOT) {
        //     megawarahWhere += ` ${megawarahWhere ? " AND " : ""}  ( SUBDIVISION_DESCRIPTION NOT LIKE '%${megawrahObj.NOT}%')`;
        //   }
        //   where += (where ? " AND " : "") + `(${megawarahWhere})`
        // } else if (fieldName === 'center_desc' && fieldValue && fieldValue !== "بدون") {
        //   // todo:
        //   let centerSplitStr = fieldValue.split(" ")?.slice(1);
        //   let centerNumber = centerSplitStr?.join(" ");
        //   let getStringNumberByValue = getNumbersInStringsByValue(centerNumber);
        //   let centerWhere = "";
        //   if (typeof getStringNumberByValue.like === 'string') {
        //     centerWhere = ` (SUBDIVISION_DESCRIPTION LIKE '%${getStringNumberByValue.like}%')`
        //   } else {
        //     // like: [{OR:["أحد","حادي"]}, {AND:"عشر"}]

        //     if (getStringNumberByValue.like?.OR && !getStringNumberByValue.like?.AND) {
        //       centerWhere = (centerWhere ? " AND " : "") + getStringNumberByValue.like?.OR.map(word => `(SUBDIVISION_DESCRIPTION LIKE '%${word}%')`).join(" OR ");
        //     } else if (getStringNumberByValue.like?.OR && getStringNumberByValue.like?.AND) {
        //       centerWhere = (centerWhere ? " AND " : "") + getStringNumberByValue.like?.OR.map(word => `(SUBDIVISION_DESCRIPTION LIKE '%${word}%' AND SUBDIVISION_DESCRIPTION LIKE '%${getStringNumberByValue.like.AND}%')`).join(" OR ");

        //     }
        //   }
        //   if (getStringNumberByValue.NOT && !districtDescValue) {
        //     centerWhere += ` ${centerWhere ? " AND " : ""}( SUBDIVISION_DESCRIPTION NOT LIKE '%${getStringNumberByValue.NOT}%')`;
        //   }
        //   centerWhere += ` ${centerWhere ? " AND " : ""} SUBDIVISION_TYPE=${subdivisionTypes.megawrah} AND ( ${districtDescValue ? `SUBDIVISION_DESCRIPTION LIKE '%مجاور%'` : ``} OR (SUBDIVISION_DESCRIPTION LIKE '%حي%' OR SUBDIVISION_DESCRIPTION LIKE '%حى%'))`;

        //   if (districtDescValue) {
        //     let megawarahWhere = ``;
        //     let numbers = districtDescValue.match(/\d+/g);
        //     let megawrahObj = megawrahNumbersInString.find(i => numbers[0] === i.value);
        //     if (typeof megawrahObj.like === 'string') {
        //       megawarahWhere += `${megawarahWhere ? " AND " : ""}  SUBDIVISION_DESCRIPTION LIKE '%${megawrahObj.like}%'`
        //     } else {
        //       // like: [{OR:["أحد","حادي"]}, {AND:"عشر"}]

        //       if (megawrahObj.like?.OR && !megawrahObj.like?.AND) {
        //         megawarahWhere += (megawarahWhere ? " AND " : "") + megawrahObj.like?.OR.map(word => `(SUBDIVISION_DESCRIPTION LIKE '%${word}%')`).join(" OR ");
        //       } else if (megawrahObj.like?.OR && megawrahObj.like?.AND) {
        //         megawarahWhere += (megawarahWhere ? " AND " : "") + megawrahObj.like?.OR.map(word => `(SUBDIVISION_DESCRIPTION LIKE '%${word}%' AND SUBDIVISION_DESCRIPTION LIKE '%${megawrahObj.like.AND}%')`).join(" OR ");

        //       }
        //     }
        //     if (megawrahObj.NOT) {
        //       // megawarahWhere += ` ${megawarahWhere ? " AND " : ""}  ( SUBDIVISION_DESCRIPTION NOT LIKE '%${megawrahObj.NOT}%')`;
        //     }
        //     centerWhere += (centerWhere ? " AND " : "") + `(${megawarahWhere})`;
        //     where += (where ? " AND " : "") + `(${centerWhere})`;
        //   }
        // } 
        // else if (fieldName === 'category_desc' && fieldValue !== "بدون" && fieldValue) {
        //   // todo:
        //   let categoryAlphabet = fieldValue.match(/\(\s*(.)\s*\)/);
        //   let valueFromAlphabetCatregory = fieldValue;
        //   if (categoryAlphabet.length) {

        //     valueFromAlphabetCatregory = alphbetNumbers.find(al => {
        //       if (typeof al.name === 'string') return al.name === categoryAlphabet[1];
        //       else {
        //         return al.name.includes(categoryAlphabet[1])
        //       }
        //     });
        //   }
        //   where += `${where ? " AND (" : "("} (SUBDIVISION_TYPE=${subdivisionTypes.category} OR SUBDIVISION_TYPE=${subdivisionTypes.district}) AND (${typeof valueFromAlphabetCatregory.name === 'string' ? `SUBDIVISION_DESCRIPTION LIKE '%${valueFromAlphabetCatregory.name}%'` : "( " + valueFromAlphabetCatregory.name.map(n => `SUBDIVISION_DESCRIPTION LIKE '%${n}%'`).join(" OR ") + " ) "} OR SUBDIVISION_DESCRIPTION LIKE '%${valueFromAlphabetCatregory.value}%' OR SUBDIVISION_DESCRIPTION LIKE '%فئة%' OR SUBDIVISION_DESCRIPTION LIKE '%فئه%') )`;

        // }
      })
      return where;
    } else {
      let where = "";
      let fieldsNames = ["munCode", "planNo", "districtName", "part_no", "subdivision_type", "subdivision_description", "parcel_part_no"];
      fieldsNames.forEach((fieldName) => {
        let munCode;
        // 1- mun code 
        let fieldValue = landInfo[fieldName];
        if (fieldName === "munCode" && fieldValue && fieldValue !== "بدون") {
          if (munCode) where += `${where ? " AND " : ""}MUNICIPALITY_NAME=${munCode} `;
        } else if (fieldName === 'planNo' && fieldValue && fieldValue !== "بدون") {
          if (fieldValue) {
            where += `${where ? " AND " : ""}PLAN_NO='${fieldValue}' `;
          }
        } else if (fieldName === "districtName" && fieldValue && fieldValue !== "0" && fieldValue !== "بدون") {
          if (fieldValue) {
            where += `${where ? " AND " : ""}DISTRICT_NAME='${fieldValue}' `;
          }
        } else if (fieldName === "part_no" && fieldValue && fieldValue !== "بدون") {
          if (fieldValue) {
            where += `${where ? " AND " : ""}PARCEL_PLAN_NO='${fieldValue}' `;
          }
        }
        else if (fieldName === "subdivision_type" && fieldValue && fieldValue !== "بدون") {
          if (fieldValue) {
            where += `${where ? " AND " : ""}SUBDIVISION_TYPE='${fieldValue}' `;
          }
        }
        else if (fieldName === "subdivision_description" && fieldValue && fieldValue !== "بدون") {
          if (fieldValue) {
            where += `${where ? " AND " : ""}SUBDIVISION_DESCRIPTION='${fieldValue}' `;
          }
        }
        else if (fieldName === "parcel_part_no" && fieldValue && fieldValue !== "بدون") {
          if (fieldValue) {
            where += `${where ? " AND " : ""}PARCEL_SPATIAL_ID='${fieldValue}' `;
          }
        }


      })
      return where;
    }
  }
  return (
    <div className="generalSearchResult ">
      <div class="resultTitle">
        {result && result.statisticsInfo && (
          <div style={{ display: "flex", gridGap: "10px" }}>
            <div style={{
              textAlign: "right", background: "#edf1f5",
              padding: "10px",
              borderRadius: "10px"
            }}>
              <img src={fn1} />
              <strong className="px-2">{t("map:mapTools.resultNum")} </strong>
              {toArabic(result.statisticsInfo.COUNT)}
            </div>
            <div style={{
              textAlign: "right", background: "#edf1f5",
              padding: "10px",
              borderRadius: "10px"
            }}>
              <img src={fn2} />
              <strong className="px-2">{"المساحة:"} </strong>
              {"45511 كم"}
            </div>


          </div>
        )}

        {result && result.statisticsInfo && result.statisticsInfo.AREA && (
          <div style={{ textAlign: "left", width: "25%" }}>
            <strong className="px-2"> {t("map:mapTools.area")} </strong>
            {toArabic((result.statisticsInfo.AREA / 1000).toFixed(2))}{" "}
            {t("map:km2")}
          </div>
        )}
        {result && result.statisticsInfo && (
          <div style={{ textAlign: "left", width: "25%" }}>
            {!["PARCEL_PRIVACY", "LGR_ROYAL", "SALES_LANDS"].includes(
              props.outerSearchResult?.layerName
            ) ? (
              <Button
                className="tableHeaderBtn outerSearchZoomAll"
                onClick={() => {
                  let features = props.outerSearchResult.list;
                  zoomToAll(features);
                }}
              >
                <Tooltip
                  placement="top-start"
                  title={t("map:mapTools.zoomToAll")}
                >
                  <img src={expand} />
                </Tooltip>
              </Button>
            ) : (
              <Button
                style={{ cursor: "pointer" }}
                title={props.outerSearchResult?.layerName === 'PARCEL_PRIVACY' ? t('common:extractPrivateLandReport') : t('common:extractRoyalLandReport')}
                onClick={(e) => exportCSVFile(e, props.outerSearchResult)}
                shape="circle"
                size='large'
              >
                {exportedData?.dataSet?.length &&
                  props.outerSearchResult.whereClause ===
                  exportedData.whereClause ? (
                  <ExportCSVFile
                    isForceClick={true}
                    isPlanLandModal={false}
                    isGeneralSearch={true}
                    fileName={props.outerSearchResult?.layerName === 'PARCEL_PRIVACY' ?
                      t('common:privateLandReport') :
                      props.outerSearchResult?.layerName === 'SALES_LANDS' ? t('common:salesLandReport') : t('common:royalLandReport')}
                    {...exportedData}
                  />
                ) : (
                  <Tooltip
                    placement="top-start"
                    title={props.outerSearchResult?.layerName === 'PARCEL_PRIVACY' ? t('common:extractPrivateLandReport') :
                      props.outerSearchResult?.layerName === 'SALES_LANDS' ? t('common:salesLandReport') : t('common:extractRoyalLandReport')}
                  >
                    <FontAwesomeIcon icon={faFileCsv} style={{ fontSize: '1.5em' }} />
                  </Tooltip>
                )}
              </Button>
            )}
          </div>
        )}
      </div>
      {result &&
        (result.list || result).map((attributes, index) => (
          <div className="generalSearchCard" key={index}>
            <Row
              onMouseLeave={clearFeatures.bind(this)}
              onMouseMove={(e) => onMouseMoveOnFeature(attributes, e)}
              onMouseEnter={gotoFeature.bind(this, attributes)}
            >
              <Col span={16} onClick={() => openFeatureDetails(attributes)}>
                <h5>{convertToArabic(getDisplayField(attributes))}</h5>
                <p>
                  <span className="munSpan">
                    {attributes.MUNICIPALITY_NAME}
                  </span>
                  -
                  <span className="distSpan">
                    {attributes.DISTRICT_NAME || attributes.plan_no}
                  </span>
                </p>
              </Col>
              <Col span={8} style={{ margin: "auto", textAlign: "center" }}>
                <Tooltip title={t("map:mapTools.zoomIn")} placement="top">
                  <button
                    className="tooltipButton"
                    onClick={() => {
                      //todo: call zoomToFeatureBySpatialID in case of royal and private land
                      if (
                        !["PARCEL_PRIVACY", "LGR_ROYAL", "SALES_LANDS"].includes(
                          props.outerSearchResult?.layerName
                        )
                      )
                        zoomToFeatureByObjectId(
                          attributes,
                          props.map,
                          false,
                          (feature) => {
                            (result.list || result)[index].geometry =
                              feature.geometry;
                          }
                        );
                      else {
                        if ((result.list || result)[index].geometry) {
                          zoomToFeatureByObjectId(
                            {
                              geometry: (result.list || result)[index].geometry
                            },
                            props.map,
                            false,
                          );
                        } else if (attributes?.id)
                          zoomToFeatureBySpatialID(
                            attributes,
                            PARCEL_LANDS_LAYER_NAME,
                            props.map,
                            false,
                            (data) => (result.list || result)[index].geometry =
                              data.geometry,
                            () =>
                              message.open({
                                type: "info",
                                content: t("common:retrievError"),
                              })
                          );
                        else {
                          zoomToFeatureByFilter(
                            getWhereClauseFromRoyalPrivacyLandInfo(attributes, props.outerSearchResult?.layerName),
                            PARCEL_LANDS_LAYER_NAME,
                            props.map
                          );
                        }
                        // message.open({
                        //   type: "info",
                        //   content: t("common:doesntReflectGDB"),
                        // });
                      }
                    }}
                  >
                    <img src={zoom} />
                    {/* <FontAwesomeIcon
                      className="zoomIcon"
                      icon={faSearchPlus}
                      style={{
                        cursor: "pointer",
                      }}
                    /> */}
                  </button>
                </Tooltip>

                <Tooltip
                  title={t("map:mapToolsServices.googleMaps2")}
                  placement="top"
                >
                  <button
                    className="tooltipButton"
                    onClick={() => navigateGeometry(index)}
                  >
                    <img
                      // style={{ width: "20px" }}
                      src={googleLocation}
                      alt="googleLocation"
                    />
                  </button>
                </Tooltip>
              </Col>
            </Row>
          </div>
        ))}

      {((!isPrivateLandOrRoyalLand && result &&
        result.list &&
        result.list.length >= window.paginationCount) || (
          isPrivateLandOrRoyalLand && result &&
          result.list &&
          result.currentPage < result.totalPages
        )) && (
          <div style={{ textAlign: "center" }}>
            <button
              onClick={searchForMoreData}
              className="seeMoreBtn px-3 py-2  mt-3"
              size="large"
              htmlType="submit"
            >
              <FontAwesomeIcon
                className="closeIconsIcon"
                icon={faAngleDoubleDown}
                style={{ cursor: "pointer" }}
              />
            </button>
          </div>
        )}
    </div>
  );
}
