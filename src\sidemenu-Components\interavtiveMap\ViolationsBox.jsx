import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { MdKeyboardArrowDown } from "react-icons/md";

import violation_logo from "../../assets/images/interactive-map/violation_logo.svg";

import violation_1 from "../../assets/images/interactive-map/violation_1.svg";
import violation_2 from "../../assets/images/interactive-map/violation_2.svg";
import violation_3 from "../../assets/images/interactive-map/violation_3.svg";
import violation_4 from "../../assets/images/interactive-map/violation_4.svg";
import violation_5 from "../../assets/images/interactive-map/violation_5.svg";
import violation_6 from "../../assets/images/interactive-map/violation_6.svg";

export default function ViolationsBox(props) {
  const { t } = useTranslation("sidemenu");

  const violations = [
    { name: "gun", src: violation_1, label: t("hunt", { ns: "common" }) },
    {
      name: "cut_wood",
      src: violation_2,
      label: t("cut_wood", { ns: "common" }),
    },
    {
      name: "garbage",
      src: violation_3,
      label: t("garbage", { ns: "common" }),
    },
    { name: "camel", src: violation_4, label: t("camel", { ns: "common" }) },
    { name: "babon", src: violation_5, label: t("babon", { ns: "common" }) },
    {
      name: "dead_bird",
      src: violation_6,
      label: t("dead_bird", { ns: "common" }),
    },
  ];
  const [showViolations, setShowViolations] = useState(true);

  const handleSelectViolation = (tool) => {
    if (
      props.generalSelectedItem &&
      props.generalSelectedItem.index === tool.index &&
      props.generalSelectedBox === "violations"
    ) {
      props.setGeneralSelectedItem(null);
      props.setGeneralSelectedBox("");
      props.updateGraphicState(undefined);

      return;
    }

    console.log(tool);

    props.setGeneralSelectedItem(tool);
    props.setGeneralSelectedBox("violations");
    props.updateGraphicState({
      graphicsLayerName: "Violation_InteractiveGraphicLayer",
      symbolName: tool.name,
    });
  };

  const handleRemoveAll = () => {
    props.setGeneralSelectedItem(null);
    props.setGeneralSelectedBox("");
    let graphicLayer = props.map.layers.items.find(
      (layer) => layer.id == "InteractiveMapGraphicLayer"
    );

    let graphicsToRemove = graphicLayer.graphics.items.filter(
      (graphic) => graphic._layerName == "Violation_InteractiveGraphicLayer"
    );

    if (graphicsToRemove.length > 0) {
      props.history.current.undoStack.push(graphicsToRemove);
      graphicLayer.graphics.removeMany(graphicsToRemove);
    }
    props.updateGraphicState(undefined);
  };

  return (
    <div className="box">
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          color: "#fff",
        }}
      >
        <div style={{ display: "flex", gap: "5px", alignItems: "center" }}>
          <img
            style={{
              filter:
                "brightness(0) saturate(100%) invert(22%) sepia(56%) saturate(706%) hue-rotate(183deg) brightness(97%) contrast(91%)",
            }}
            src={violation_logo}
            alt="violation logo"
          />
          <span className="logo-span">{t("violations")}</span>
        </div>

        <div style={{ display: "flex", gap: "5px", alignItems: "center" }}>
          <Button className="delete_all_toolsbox" onClick={handleRemoveAll}>
            {t("clearAll")}
          </Button>
          <MdKeyboardArrowDown
            size={20}
            style={{
              cursor: "pointer",
              transform: `rotate(${!showViolations ? "180deg" : 0})`,
            }}
            onClick={() => setShowViolations(!showViolations)}
            className="MdKeyboardArrowDown_icon"
          />
        </div>
      </div>

      {showViolations && (
        <div
          className="images"
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(6, 1fr)",
            gap: "10px",
            marginTop: "10px",
          }}
        >
          {violations.map((tool, indx) => (
            <Tooltip title={tool.label} placement="bottom">
              <div
                key={indx}
                name={tool.name}
                className="image"
                style={{
                  background: props.generalSelectedItem
                    ? props.generalSelectedItem.index === indx &&
                      props.generalSelectedBox === "violations"
                      ? "#1976D2"
                      : "transparent"
                    : "transparent",
                }}
                onClick={() =>
                  handleSelectViolation({
                    name: tool.name,
                    src: tool.src,
                    index: indx,
                  })
                }
              >
                <img
                  src={tool.src}
                  alt="tool"
                  style={{
                    filter: props.generalSelectedItem
                      ? props.generalSelectedItem.index === indx &&
                        props.generalSelectedBox === "violations"
                        ? "brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(2%) hue-rotate(264deg) brightness(105%) contrast(101%)"
                        : "brightness(0) saturate(100%) invert(22%) sepia(56%) saturate(706%) hue-rotate(183deg) brightness(97%) contrast(91%)"
                      : "brightness(0) saturate(100%) invert(22%) sepia(56%) saturate(706%) hue-rotate(183deg) brightness(97%) contrast(91%)",
                  }}
                />
              </div>
            </Tooltip>
          ))}
        </div>
      )}
    </div>
  );
}
