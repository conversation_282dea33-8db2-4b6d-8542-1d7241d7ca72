import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { MdKeyboardArrowDown } from "react-icons/md";

import more_logo from "../../assets/images/interactive-map/more_logo.svg";
import more_1 from "../../assets/images/interactive-map/more_1.svg";
import more_2 from "../../assets/images/interactive-map/more_2.svg";
import more_3 from "../../assets/images/interactive-map/more_3.svg";
import more_4 from "../../assets/images/interactive-map/more_4.svg";
import more_5 from "../../assets/images/interactive-map/more_5.svg";
import more_6 from "../../assets/images/interactive-map/more_6.svg";
import more_7 from "../../assets/images/interactive-map/more_7.svg";

export default function MoreInteractiveTools(props) {
  const { t } = useTranslation("sidemenu");

  const tools = [
    { name: "building", src: more_1, label: t("building", { ns: "common" }) },
    { name: "tent", src: more_2, label: t("tent", { ns: "common" }) },
    { name: "flag", src: more_3, label: t("flag", { ns: "common" }) },
    { name: "ruler", src: more_4, label: t("ruler", { ns: "common" }) },
    { name: "castle", src: more_5, label: t("castle", { ns: "common" }) },
    { name: "chair", src: more_6, label: t("chair", { ns: "common" }) },
    { name: "rocks", src: more_7, label: t("rocks", { ns: "common" }) },
    { name: "engineer", src: more_7, label: t("engineer", { ns: "common" }) },
  ];

  const [showTools, setShowTools] = useState(true);

  const handleSelectTool = (tool) => {
    if (
      props.generalSelectedItem &&
      props.generalSelectedItem.index === tool.index &&
      props.generalSelectedBox === "more-tools"
    ) {
      props.setGeneralSelectedItem(null);
      props.setGeneralSelectedBox("");
      props.updateGraphicState(undefined);

      return;
    }

    props.setGeneralSelectedItem(tool);
    props.setGeneralSelectedBox("more-tools");
    props.updateGraphicState({
      graphicsLayerName: "More_InteractiveGraphicLayer",
      symbolName: tool.name,
    });
  };

  const handleRemoveAll = () => {
    props.setGeneralSelectedItem(null);
    props.setGeneralSelectedBox("");
    let graphicLayer = props.map.layers.items.find(
      (layer) => layer.id == "InteractiveMapGraphicLayer"
    );

    let graphicsToRemove = graphicLayer.graphics.items.filter(
      (graphic) => graphic._layerName == "More_InteractiveGraphicLayer"
    );

    if (graphicsToRemove.length > 0) {
      props.history.current.undoStack.push(graphicsToRemove);
      graphicLayer.graphics.removeMany(graphicsToRemove);
    }
    props.updateGraphicState(undefined);
  };

  return (
    <div className="box">
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          color: "#fff",
        }}
      >
        <div style={{ display: "flex", gap: "5px", alignItems: "center" }}>
          <img
            style={{
              filter:
                "brightness(0) saturate(100%) invert(22%) sepia(56%) saturate(706%) hue-rotate(183deg) brightness(97%) contrast(91%)",
            }}
            src={more_logo}
            alt="more tools logo"
          />
          <span className="logo-span">{t("general")}</span>
        </div>

        <div style={{ display: "flex", gap: "5px", alignItems: "center" }}>
          <Button className="delete_all_toolsbox" onClick={handleRemoveAll}>
            {t("clearAll")}
          </Button>
          <MdKeyboardArrowDown
            size={20}
            style={{
              cursor: "pointer",
              transform: `rotate(${!showTools ? "180deg" : 0})`,
            }}
            onClick={() => setShowTools(!showTools)}
            className="MdKeyboardArrowDown_icon"
          />
        </div>
      </div>

      {showTools && (
        <div
          className="images"
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(6, 1fr)",
            gap: "10px",
            marginTop: "10px",
          }}
        >
          {tools.map((tool, indx) => (
            <Tooltip title={tool.label} placement="bottom">
              <div
                key={indx}
                className="image"
                name={tool.name}
                style={{
                  background: props.generalSelectedItem
                    ? props.generalSelectedItem.index === indx &&
                      props.generalSelectedBox === "more-tools"
                      ? "#1976D2"
                      : "transparent"
                    : "transparent",
                }}
                onClick={() =>
                  handleSelectTool({
                    name: tool.name,
                    src: tool.src,
                    index: indx,
                  })
                }
              >
                <img
                  src={tool.src}
                  alt="tool"
                  style={{
                    filter: props.generalSelectedItem
                      ? props.generalSelectedItem.index === indx &&
                        props.generalSelectedBox === "more-tools"
                        ? "brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(2%) hue-rotate(264deg) brightness(105%) contrast(101%)"
                        : "brightness(0) saturate(100%) invert(22%) sepia(56%) saturate(706%) hue-rotate(183deg) brightness(97%) contrast(91%)"
                      : "brightness(0) saturate(100%) invert(22%) sepia(56%) saturate(706%) hue-rotate(183deg) brightness(97%) contrast(91%)",
                  }}
                />
              </div>
            </Tooltip>
          ))}
        </div>
      )}
    </div>
  );
}
