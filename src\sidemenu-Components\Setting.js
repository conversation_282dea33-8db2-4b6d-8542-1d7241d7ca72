import React, { Component } from "react";
import { Select, Form, Button } from "antd";
import { withTranslation } from "react-i18next";

class Setting extends Component {
  state = {
    resolutionType: +localStorage.getItem("DPI") || null,
    resolutionTypes: [
      { key: 96, name: "Low (96 DPI)" },
      { key: 200, name: "Medium (200 DPI)" },
      { key: 396, name: "High (396 DPI)" },
    ],
    validateForm: false,
    previewVisible: false,
    previewImageUrl: "",
  };

  handleChange = (value) => {
    this.setState({ resolutionType: value });
  };

  takeScreenshot = async () => {
    const { resolutionType } = this.state;
    const { view } = this.props.map;

    const scaleFactor = resolutionType / 96;
    const width = Math.round(view.width * scaleFactor);
    const height = Math.round(view.height * scaleFactor);

    return await view.takeScreenshot({ width, height });
  };

  // Completely reset the view
  hardRefreshView = () => {
    const { view } = this.props.map;
    const currentExtent = view.extent.clone();
    view.extent = currentExtent;
  };

  saveSettings = async () => {
    localStorage.setItem("DPI", this.state.resolutionType);
    this.hardRefreshView();
  };

  previewMapImage = async () => {
    const { resolutionType } = this.state;

    if (!resolutionType) {
      this.setState({ validateForm: true });
      return;
    }

    try {
      const screenshot = await this.takeScreenshot();
      this.setState({
        previewVisible: true,
        previewImageUrl: screenshot.dataUrl,
      });
    } catch (error) {
      console.error("Preview error:", error);
    }
  };

  render() {
    const {
      resolutionType,
      resolutionTypes,
      validateForm,
      previewVisible,
      previewImageUrl,
    } = this.state;
    const { t } = this.props;

    return (
      <div
        style={{
          padding: "20px",
          display: "flex",
          flexDirection: "column",
          gap: "10px",
        }}
      >
        {/* <p>{t("selectResolution")}</p> */}
        {/* <Select
          virtual={false}
          suffixIcon={<DownCircleFilled />}
          className="searchInput englishFont"
          showSearch
          placeholder={t("selectResolution")}
          style={{
            width: "100%",
            borderColor: !resolutionType && validateForm ? "red" : "black",
            borderRadius: "5px",
            border:
              !resolutionType && validateForm
                ? "1px solid #ff1313"
                : "1px solid #ccc",
          }}
          onChange={this.handleChange}
          value={resolutionType}
          optionFilterProp="v"
          filterOption={(input, option) =>
            option.v.toLowerCase().includes(input.toLowerCase())
          }
        >
          {resolutionTypes.map((m) => (
            <Select.Option v={m.name} key={m.key} value={m.key}>
              {m.name}
            </Select.Option>
          ))}
        </Select> */}

        <div>
          <Form.Item label={"درجة الوضوح"} className="select-cust">
            <Select
              virtual={false}
              className="searchInput englishFont"
              showSearch
              placeholder={t("selectResolution")}
              onChange={this.handleChange}
              value={resolutionType}
              optionFilterProp="v"
              filterOption={(input, option) =>
                option.v.toLowerCase().includes(input.toLowerCase())
              }
            >
              {resolutionTypes.map((m) => (
                <Select.Option v={m.name} key={m.key} value={m.key}>
                  {m.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          {validateForm && !resolutionType && (
            <div
              style={{ textAlign: "center", color: "red", marginTop: "10px" }}
            >
              - {t("selectResolution")} -
            </div>
          )}
        </div>

        {/* <button
          className="SearchBtn"
          style={{ margin: "auto", display: "block" }}
          onClick={this.saveSettings}
        >
          {t("save")}
        </button> */}

        <Button
          className="addMark"
          size="large"
          onClick={this.saveSettings}
        >
          {t("save")}
        </Button>
      </div>
    );
  }
}

export default withTranslation("print")(Setting);
