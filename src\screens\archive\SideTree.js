import React, { useEffect } from "react";
import { Tree } from "antd";
import { DownCircleFilled } from "@ant-design/icons";
import Loader from "../../containers/Loader";
import { getUniqeID } from "../../helper/utilsFunc";
//elements of the tree

class Node {
  constructor(data) {
    this.title = data.Name.replace(/\d/g, (d) => "٠١٢٣٤٥٦٧٨٩"[d]).replace(
      /\./g,
      "٫"
    );
    this.Path = data.Path;
    this.parent = null;
    this.children = [];
    this.key = data.Path;
  }
}
class TreeDataType {
  constructor(data) {
    let node = new Node(data);
    this._root = node;
  }

  //other Tree methods...
  //returns the node that has the given data, or null
  //use a depth-first search
  find(data, node = this._root) {
    //if the current node matches the data, return it
    if (node.Path == data.Path) return node;
    //recurse on each child node
    for (let child of node.children) {
      //if the data is found in any child node it will be returned here
      let subChild = this.find(data, child);
      if (subChild) return subChild;
    }

    //otherwise, the data was not found
    return null;
  }
  //create a new node with the given data and add it to the specified parent node
  add(data, parentData) {
    let node = new Node(data);
    let parent = this.find(parentData);
    //if the parent exists, add this node
    if (parent) {
      let isExist = this.find(data);
      if (isExist) return null;
      else {
        parent.children.push(node);
        node.parent = parent;
        //return this node
        return node;
      }
    }
    //otherwise throw an error
    else {
      throw new Error(
        `Cannot add node: parent with data ${parentData} not found.`
      );
    }
  }
  //removes the node with the specified data from the tree
  remove(data) {
    //find the node
    let node = this.find(data);
    //if the node exists, remove it from its parent
    if (node) {
      //find the index of this node in its parent
      let parent = node.parent;
      let indexOfNode = parent.children.indexOf(node);
      //and delete it from the parent
      parent.children.splice(indexOfNode, 1);
    }
    //otherwise throw an error
    else {
      throw new Error(`Cannot remove node: node with data ${data} not found.`);
    }
  }
}
const { DirectoryTree } = Tree;
const SideTree = (props) => {
  const [treeData, setTreeData] = React.useState([]);
  const [expandedKeys, setExpandedKeys] = React.useState([]);
  const [createdTree, setCreatedTree] = React.useState();
  useEffect(() => {
    if (props?.archiveTreeElements?.length)
      setTreeData(getTreeData(props.archiveTreeElements));
  }, [props.archiveTreeElements]);
  const onExpand = (keys, info) => {
    let otherKeys = [...keys];
    setExpandedKeys(otherKeys);
  };
  const getTreeData = (data) => {
    let treee = createdTree,
      expandedKeysArr = [],
      filterdDataTree = [...data];


    filterdDataTree.forEach((item) => {
      let hasChild = item.Path.split("\\");
      if (hasChild.length === 1) {
        treee = new TreeDataType(item);
        expandedKeysArr.push(treee._root.key);
      } else {
        hasChild.forEach((ch, idx) => {
          if (!idx && !treee) {
            let reqItem = { ...item, Path: ch, Name: ch };
            treee = new TreeDataType(reqItem);
            expandedKeysArr.push(treee._root.key);
          } else if (!idx && treee) {
            if (!expandedKeysArr.includes(treee._root.key))
              expandedKeysArr.push(treee._root.key);
            return;
          } else {
            treee.add(
              {
                Path: hasChild.slice(0, idx + 1).join("\\"),
                Name: ch,
              },
              {
                Path: hasChild.slice(0, idx).join("\\"),
              }
            );
            let childKey = treee.find({
              Path: hasChild.slice(0, idx + 1).join("\\"),
              Name: ch,
            })?.key;
            if (!expandedKeysArr.includes(childKey))
              expandedKeysArr.push(
                treee.find({
                  Path: hasChild.slice(0, idx + 1).join("\\"),
                  Name: ch,
                })?.key
              );
          }
        });
      }
    });
    setExpandedKeys(expandedKeysArr);
    setCreatedTree(treee);
    return treee ? [treee._root] : [];
  };
  // if (!treeData.length) return <Loader />;
  // else

  // console.log(numLatinToAr("501-506-281(281)"));

  return (
    <DirectoryTree
      multiple
      defaultExpandAll
      expandedKeys={expandedKeys}
      // showLine
      selectedKeys={[...props.selectedNode]}
      onSelect={props.onSelectNode}
      onExpand={onExpand}
      treeData={treeData}
      switcherIcon={<DownCircleFilled />}
    />
  );
};
export default SideTree;
