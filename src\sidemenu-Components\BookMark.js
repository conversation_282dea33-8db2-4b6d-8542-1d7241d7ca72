import React, { useEffect, useState } from "react";
import { Form, Input, Row, Col, Button, message, Modal } from "antd";
import { Container } from "react-bootstrap";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faPlus,
  faEdit,
  faStar,
  faTrash,
} from "@fortawesome/free-solid-svg-icons";
import Upload from "../components/Upload/Upload";
import axios from "axios";
import { UploadOutlined } from "@ant-design/icons";
import Extent from "@arcgis/core/geometry/Extent";
import { useTranslation } from "react-i18next";
import { convertToArabic } from "../helper/common_func";
import {
  showGeneralDataTable,
  executeGPTool,
  showLoading,
} from "../helper/common_func";

import { FaBookmark } from "react-icons/fa6";
import { FiEdit } from "react-icons/fi";
import { MdDelete } from "react-icons/md";

import bookmark_icon from "../assets/icons/Bookmark.svg";
import edit_icon from "../assets/icons/Library.svg";
import delete_icon from "../assets/icons/Trash Bin 2.svg";

export default function BookMark(props) {
  const { t } = useTranslation("sidemenu");

  const [formValues, setFormValues] = useState({
    bookmark: "",
  });
  const [bookmarks, setBookmarks] = useState([]);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [activeBookMark, setActiveBookMark] = useState(-1);
  const User = localStorage.user ? JSON.parse(localStorage.user) : null; // console.log(e.target.value);
  // console.log(convertToArabic(e.target.value));

  const handleChangeInput = (e) => {
    if (e?.target?.name) {
      setFormValues({ ...formValues, [e.target.name]: e.target.value });
    }
  };

  useEffect(() => {
    if (User) {
      axios
        .get(window.ApiUrl + "/Bookmark/GetAll?filter_key=user_id&q=" + User.id)
        .then(({ data }) => {
          setBookmarks(data.results || []);
        });
    } else {
      setBookmarks(
        (localStorage.bookmarks && JSON.parse(localStorage.bookmarks)) || []
      );
    }
  }, []);

  const zoomToBookmark = (bookmark, index) => {
    setActiveBookMark(index);

    if (typeof bookmark.extent == "string") {
      bookmark.extent = JSON.parse(bookmark.extent);
    }

    props.map.view.goTo(
      new Extent({
        xmin: bookmark.extent.xmin,
        ymin: bookmark.extent.ymin,
        xmax: bookmark.extent.xmax,
        ymax: bookmark.extent.ymax,
        spatialReference: bookmark.extent.spatialReference,
      })
    );
  };

  const addBookMark = (e) => {
    let temp = [...bookmarks];

    if (User) {
      axios
        .post(window.ApiUrl + "/api/Bookmark", {
          title: formValues.bookmark,
          extent: JSON.stringify(props.map.view.extent),
          attachment: formValues.attachment,
          user_id: User.id,
        })
        .then(({ data }) => {
          temp.push(data);
          setBookmarks(temp);
        });
    } else {
      temp.push({
        title: formValues.bookmark,
        extent: JSON.stringify(props.map.view.extent),
        attachment: formValues.attachment,
      });
      localStorage.bookmarks = JSON.stringify(temp);
      setBookmarks(temp);
    }

    setFormValues({});
    message.success(t("saveLocationToBookMark"));
  };

  const removeBookMark = (bookmark, index) => {
    let arr = [...bookmarks];
    arr.splice(index, 1);

    if (User) {
      axios.delete(window.ApiUrl + "/Bookmark/" + bookmark.id).then(() => {});
    } else {
      localStorage.bookmarks = JSON.stringify(arr);
    }

    message.success(t("removed"));

    setBookmarks(arr);
  };

  const showEdit = (bookmark, index) => {
    zoomToBookmark(bookmark, index);
    setFormValues({
      ...formValues,
      editName: bookmark.title,
      attachment: bookmark.attachment,
    });
    setEditModalVisible(true);
  };

  const afterEditModal = () => {
    bookmarks[activeBookMark].title = formValues.editName;
    bookmarks[activeBookMark].attachment = formValues.attachment;
    bookmarks[activeBookMark].extent = JSON.stringify(
      bookmarks[activeBookMark].extent
    );
    setEditModalVisible(false);

    if (User) {
      axios
        .put(window.ApiUrl + "/api/Bookmark/" + bookmarks[activeBookMark].id, {
          ...bookmarks[activeBookMark],
        })
        .then(() => {});
    } else {
      let temp = [...bookmarks];
      temp[activeBookMark].title = formValues.editName;
      temp[activeBookMark].attachment = formValues.attachment;

      localStorage.bookmarks = JSON.stringify(temp);
    }

    setFormValues({});
    message.success(t("edited"));
  };
  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      setFormValues({
        ...formValues,
        bookmark: convertToArabic(formValues.bookmark),
      });
    }, 1000);

    return () => clearTimeout(delayDebounceFn);
  }, [formValues.bookmark]);

  const getFileNameFromUrl = (url) => {
    try {
      if (!url?.startsWith("http")) return url;
      const urlObject = new URL(url);
      const pathname = urlObject.pathname;
      const fileName = pathname.substring(pathname.lastIndexOf("/") + 1);
      return fileName;
    } catch (error) {
      console.error("Invalid URL:", error);
      return null;
    }
  };

  const config = {
    headers: {
      "content-type": "multipart/form-data",
    },
  };
  const setFile = (e) => {
    const status = e.file.status;
    if (status !== "uploading") {
      console.log(e.file, e.fileList);
    }
    if (status === "done") {
      const formData = new FormData();
      formData.append(
        `file[${0}]`,
        e.fileList[e.fileList.length - 1].originFileObj
      );

      axios
        .post(window.ApiUrl + "/uploadMultifiles", formData, config)
        .then((res) => {
          setFormValues({
            ...formValues,
            attachment: res.data?.map((file) => file.data).join(", "),
          });

          // setFormValues({ ...formValues, bookmark: "" });
        })
        .catch((err) => {
          message.error(t("sidemenu:uploadFilesError"));
        });
    }
  };
  return (
    <div className="coordinates mb-4 mt-4">
      <Container>
        <Form
          className="GeneralForm"
          layout="vertical"
          name="validate_other"
          onFinish={addBookMark}
        >
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              gap: "10px",
            }}
          >
            <Form.Item
              label={t("bookmark")}
              rules={[
                {
                  message: t("enterBookmark"),
                  required: true,
                },
              ]}
              className="select-cust"
            >
              <Input
                type="text"
                //  onKeyPress={e=>setFormValues({ ...formValues,bookmark: convertToArabic(e.target.value) })}
                name="bookmark"
                onChange={handleChangeInput}
                // onBlur={(e) => {
                //   setFormValues({ ...formValues, [e.target.name]: convertToArabic(e.target.value) });
                // }}
                value={formValues.bookmark}
                placeholder={t("entre-name")}
              />
              {/* {console.log(convertToArabic(formValues.bookmark))} */}
            </Form.Item>
            <Form.Item>
              <Upload
                label={"إضافة صور/ملفات اخري"}
                name="attachment"
                fileType={"image/*,.kmz,.kml,.dwg,.pdf"}
                multiple={true}
                onInputChange={(value) => {
                  setFormValues({ ...formValues, attachment: value });
                  //message.success(t("sidemenu:uploadSuccess"));
                }}
                value={
                  (!editModalVisible && formValues.attachment?.split(", ")) ||
                  []
                }
              />
            </Form.Item>
            <Button
              className="addMark mt-2 "
              size="large"
              htmlType="submit"
              disabled={formValues.bookmark !== "" ? false : true}
            >
              اضافة
            </Button>
          </div>
        </Form>
        {/* ===== */}
        {/* <Upload
            name="fileUpload"
            multiple={true}
            onChange={setFile}
            accept=".kmz, .kml, .dwg , .pdf"
            type="file"
            action={window.ApiUrl + "/uploadMultifiles"}
            // customRequest={() => {}} // disables automatic upload
          >
            <button
              className="SearchBtn mt-3 "
              size="large"
              // htmlType="submit"
              block
            >
              {t("sidemenu:upload")} <UploadOutlined />
            </button>
          </Upload> */}
        {/* <Upload
          name="fileUpload"
          multiple={true}
          onChange={setFile}
          accept="image/*,.kmz,.kml,.dwg , .pdf"
          showUploadList={true}
          fileList={
            (!editModalVisible &&
              formValues.attachment?.split(",").map((file, index) => ({
                uid: index + 1,
                name: getFileNameFromUrl(file),
                status: "done",
                url: `${window.filesURL}${file}`,
              }))) ||
            []
          }
          customRequest={({ file, onSuccess, onError }) => {
            const formData = new FormData();
            formData.append("file[0]", file);
            // setTimeout(() => onSuccess("ok"), 0);

            axios
              .post(window.ApiUrl + "/uploadMultifiles", formData, {
                headers: { "Content-Type": "multipart/form-data" },
              })
              .then((res) => {
                const uploadedUrls = res.data
                  ?.map((file) => file.data)
                  .join(",");
                setFormValues((prev) => ({
                  ...prev,
                  attachment: uploadedUrls,
                }));
                onSuccess("ok");
              })
              .catch((err) => {
                message.error("Upload failed");
                onError(err);
              });
          }}
          //                      fileList={formValues.attachment?.split(',')?.map((file, index) => ({
          //   uid: index + 1,
          //   name: getFileNameFromUrl(file),
          //   status: 'done',
          //   url: file,
          // })) || []}
        >
          <Button icon={<UploadOutlined />}>{t("sidemenu:upload")}</Button>
        </Upload> */}

        {bookmarks.map((b, index) => (
          <div className="generalSearchCard">
            <div
              // className="bookmarkRowEnglish"
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                flexDirection: "row-reverse",
                padding: "15px",
              }}
            >
              <div
                style={{
                  display: "flex",
                  flexDirection: "row-reverse",
                  alignItems: "center",
                  gap: "10px",
                  flex: 1,
                }}
              >
                {/* <FaBookmark size={24} color="#284587" /> */}

                <img src={bookmark_icon} alt="" />

                <div
                  onClick={() => zoomToBookmark(b, index)}
                  style={{
                    whiteSpace: "break-spaces",
                    wordBreak: "break-word",
                    paddingRight: "5px",
                  }}
                >
                  <p style={{ margin: "0px", color: "#284587" }}>{b.title}</p>
                </div>
              </div>

              <div
                style={{
                  display: "flex",
                  gap: "8px",
                  flexDirection: "row-reverse",
                }}
              >
                {/* <FiEdit
                  size={16}
                  color="#284587"
                  onClick={() => showEdit(b, index)}
                />
                <MdDelete
                  size={16}
                  onClick={() => removeBookMark(b, index)}
                  color="red"
                /> */}

                <img
                  src={edit_icon}
                  alt=""
                  onClick={() => showEdit(b, index)}
                />
                <img
                  src={delete_icon}
                  alt=""
                  onClick={() => removeBookMark(b, index)}
                />
              </div>
            </div>
          </div>
        ))}
      </Container>
      <Modal
        title={t("editBookmarkName")}
        centered
        visible={editModalVisible}
        onOk={() => afterEditModal()}
        onCancel={() => {
          setEditModalVisible(false);
          setFormValues({});
        }}
        okText={t("edit")}
        cancelText={t("cancel")}
      >
        <Input
          name="editName"
          onChange={handleChangeInput}
          value={formValues.editName}
          placeholder={t("bookmarkName")}
        />
        {/* <Upload
          name="fileUpload_modal"
          multiple={true}
          accept="image/*,.kmz,.kml,.dwg,.pdf"
          showUploadList={true}
          fileList={
            (editModalVisible &&
              formValues.attachment?.split(",").map((file, index) => ({
                uid: index + 1,
                name: getFileNameFromUrl(file),
                status: "done",
                url: `${window.filesURL}${file}`,
              }))) ||
            []
          }
          customRequest={({ file, onSuccess, onError }) => {
            const formData = new FormData();
            formData.append("file[0]", file);

            axios
              .post(window.ApiUrl + "/uploadMultifiles", formData, {
                headers: { "Content-Type": "multipart/form-data" },
              })
              .then((res) => {
                const uploadedUrls = res.data
                  ?.map((file) => file.data)
                  .join(",");
                setFormValues((prev) => ({
                  ...prev,
                  attachment: uploadedUrls,
                }));
                onSuccess("ok");
              })
              .catch((err) => {
                message.error("Upload failed");
                onError(err);
              });
          }}
        >
          <Button icon={<UploadOutlined />}>{t("sidemenu:upload")}</Button>
        </Upload> */}
        <Upload
          label={"إرفاق ملف"}
          name="attachment"
          fileType={"image/*,.kmz,.kml,.dwg,.pdf"}
          multiple={true}
          onInputChange={(value) => {
            setFormValues({ ...formValues, attachment: value });
            //message.success(t("sidemenu:uploadSuccess"));
          }}
          value={(editModalVisible && formValues.attachment?.split(", ")) || []}
        />
      </Modal>
    </div>
  );
}
