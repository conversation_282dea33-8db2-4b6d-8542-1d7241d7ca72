.loader {
  position: relative;
  margin: auto;
  /* right: 50px; */
  /* top: 50%; */
  /* transform: translateY(-50%); */
  z-index: 10;
}

.loaderForm {
  position: absolute;
  left: 20px;
  transform: translateY(-130%);
  z-index: 10;
}

.loaderForm_Deals {
  position: absolute;
  left: 20px;
  transform: translateY(-10%);
  z-index: 10;
}

.loaderFormSearchBtn_115 {
  position: absolute;
  left: 110px;
  transform: translateY(-115%);
  z-index: 10;
}

.loaderFormSearchBtn_favorites {
  position: absolute;
  left: 80px;
  transform: translateY(-100%);
  z-index: 10;
}

.loaderFormSearchBtn_110 {
  position: absolute;
  left: 100px;
  transform: translateY(-110%);
  z-index: 10;
  
}

.loaderFormSearchBtn_130 {
  position: absolute;
  left: 100px;
  transform: translateY(-130%);
  z-index: 10;
}

.defaultButton {
  margin-top: 5px;
  padding: 5px 10px;
}

.removeButton {
  margin-top: 5px;
  background-color: #ff4d4d;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 5px 10px;
  font-size: 12px;
  cursor: pointer;
  width: 100%;
}

.removeButton:hover {
  background-color: #ff3333;
}

.PDFContent {
  padding: 10px;
  border-radius: 16px;
  border: 1px solid #a8a8a8;
  display: flex;
  /* align-items: center; */
  justify-content: center;
  cursor: pointer;
}

.PDFContent p {
  margin: 0;
  padding: 0;
  font-size: 12px;
  font-weight: 700;
  text-align: center;
  color: #a8a8a8;
}

.addPhoto {
  /* font-size: 6.73px !important; */
  font-size: 10px !important;
  font-weight: 700 !important;
  text-align: center !important;
  color: #338C9A !important;
  /* color: #A8A8A8; */
}


.iconSrtick{
  color: red;
  padding: 0px 3px;
}