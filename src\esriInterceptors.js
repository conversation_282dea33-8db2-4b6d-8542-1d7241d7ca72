import esriConfig from "@arcgis/core/config";
export default (urls = [window.mapUrl], scope) => {
 // debugger;
  if (esriConfig) {
    if (esriConfig.request.interceptors.length) {
      esriConfig.request.interceptors = [];
    }
    esriConfig.request.interceptors.push({
      // set the `urls` property to the URL of the FeatureLayer so that this
      // interceptor only applies to requests made to the FeatureLayer URL
      urls: /\/(MapServer|FeatureServer|ImageServer)/i,
      // use the BeforeInterceptorCallback to add token to query
      before: function (params) {
        if (params.requestOptions.query) {
          params.requestOptions.query = params.requestOptions.query || {};
          const pixelRatio = window.devicePixelRatio || 1.2;
          const standardDPI = +localStorage.getItem("DPI") || 0;
          const calculatedDPI = Math.round(standardDPI * pixelRatio);
          params.requestOptions.query.dpi =
            (calculatedDPI > 0 && calculatedDPI) ||
            params.requestOptions.query.dpi;
        }
      },
    });
  }
};
