import React, { useEffect, useState } from "react";
import ReactApex<PERSON>hart from "react-apexcharts";
import { useTranslation } from "react-i18next";
import { getMinMax } from "../../../../helper/utilsFunc";

function BarChartComp(props) {
  const { i18n, t } = useTranslation("dashboard");
  const [series, setSeries] = useState();
  const [labels, setLabels] = useState();
  const [options, setOptions] = useState();
  useEffect(() => {
    let isChildChart = props.isChildChart;
    if (
      (isChildChart && props.preparedChartData) ||
      props.preparedChartData?.length
    ) {
      //todo: set directly series in case of isChildChart
      if (!isChildChart) setChartDataForNormalChart();
      else {
        setChartDataForChildChart();
      }
    } else {
      setSeries([]);
      setOptions({});
      setLabels([]);
    }
    return () => {
      setSeries();
      setOptions();
      setLabels();
    };
  }, [props.preparedChartData]);
  useEffect(() => {
    if (props.sideTblTitle === props.title && series) {
      let isChildChart = props.isChildChart;

      props.onClickTitle({
        data: series,
        chartOriginalData: props?.preparedChartData?.chartOriginalData,
        title: props.title,
        labels,
        isChildChart,
      });
    }
  }, [series]);
  const setChartDataForNormalChart = () => {
    let max = getMinMax(props.preparedChartData.map((i) => i.count)).max;

    setSeries([
      {
        name: props.title,
        data:
          max < 10000
            ? props.preparedChartData.map((i) => i.count)
            : props.preparedChartData.map((i) => i.count / 1000),
      },
    ]);
    setLabels(
      props.preparedChartData.map((i) => {
        return {
          value: i.label,
          countInK: !(max < 10000),
        };
      })
    );

    setOptions({
      colors: ["#2374A3", "#F64E60", "#8950FC"],
      chart: {
        defaultLocale: i18n.language === "ar" ? "ar" : "en",
        locales: [
          {
            name: i18n.language === "ar" ? "ar" : "en",
            options: {
              toolbar: {
                exportToSVG: t("exportToSVG"),
                exportToPNG: t("exportToPNG"),
                exportToCSV: t("exportToCSV"),
                menu: t("menu"),
                selection: t("selection"),
                selectionZoom: t("selectionZoom"),
                download: t("downloadSVG"),
                zoomIn: t("zoomIn"),
                zoomOut: t("zoomOut"),
                pan: t("pan"),
                reset: t("reset"),
              },
            },
          },
        ],
        toolbar: {
          tools: {
            download: true,
          },
          export: {
            csv: {
              filename: props.title,
              columnDelimiter: ",",
              headerCategory: props.title,
              headerValue: max < 10000 ? t("count") : `${t("count")} *1000`,
              dateFormatter(timestamp) {
                return new Date(timestamp).toDateString();
              },
            },
            svg: {
              filename: props.title,
            },
            png: {
              filename: props.title,
            },
          },
        },
        id: "basic-bar",
        height: "100%",
      },
      legend: { show: false },
      plotOptions: {
        bar: {
          // dataLabels: {
          //     position: 'bottom', // top, center, bottom
          // },

          horizontal: props.type === "bar" ? true : false,
          distributed: true,
          dataLabels: {
            position: "top",
          },
          columnWidth: "90%",
        },
      },
      xaxis: {
        categories: props.preparedChartData.map((i) => {
          return i.label?.split(" ");
        }),
        labels: {
          trim: true,
          hideOverlappingLabels: true,
          show: props.preparedChartData
            .map((i) => {
              return i.label?.split(" ");
            })
            .every((i) => i.length <= 2),
        },
        title:
          props.type === "bar"
            ? {
                text: max < 10000 ? t("count") : `${t("count")} *1000`,
              }
            : {},
      },
      dataLabels: {
        enabled: false,
        formatter: function (val) {
          return val * 1000;
        },
        offsetY: -6,
        style: {
          fontSize: "12px",
          colors: ["#304758"],
        },
      },
      grid: {
        xaxis: {
          tickPlacement: "between",
          labels: {
            trim: true,
          },
          lines: {
            show: true,
          },
        },
      },
      yaxis: {
        reversed: false,

        tickPlacement: "between",
        labels: {
          show: props.type === "bar" ? false : true,
          trim: true,
        },
        title: props.type === "bar" ? { text: props.title } : {},
        axisTicks: {
          show: true,
        },
      },
      // title: {
      //     text: props.title,
      //     align: 'center',
      //     style: {
      //         fontSize: '16px',
      //         fontWeight: 'bold',
      //         fontFamily: "NeoSansArabic",

      //     },
      // },

      tooltip: {
        shared: true,
        intersect: false,
        // followCursor: true,
        // fillSeriesColor: true,
        // onDatasetHover: {
        //   highlightDataSeries: true,
        // },
        // items: {
        //   display: "flex",
        // },
        // fixed: {
        //   // enabled: true,
        //   // position: "topRight",
        //   offsetX: 0,
        //   offsetY: 0,
        // },
        x: {
          formatter: function (
            value,
            { series, seriesIndex, dataPointIndex, w }
          ) {
            return (
              props.title +
              ": " +
              `<strong>${w.globals.labels[dataPointIndex]}</strong>`
            );
          },
        },
        y: {
          formatter: function (value) {
            return max < 10000
              ? `${t("count")}:${value}`
              : `${t("CountK")}:${value}`;
          },
          title: {
            formatter: (seriesName) => "",
          },
        },
      },
    });
  };
  const setChartDataForChildChart = () => {
    let { data, categories, chartOriginalData } = props.preparedChartData;
    let max = getMinMax(data.flatMap((j) => j.data)).max;
    // let labels = data.map(item=>item.name);
    setSeries(data);
    setLabels(categories);

    setOptions({
      chart: {
        defaultLocale: "ar",
        locales: [
          {
            name: i18n.language === "ar" ? "ar" : "en",
            options: {
              toolbar: {
                exportToSVG: t("exportToSVG"),
                exportToPNG: t("exportToPNG"),
                exportToCSV: t("exportToCSV"),
                menu: t("menu"),
                selection: t("selection"),
                selectionZoom: t("selectionZoom"),
                download: t("downloadSVG"),
                zoomIn: t("zoomIn"),
                zoomOut: t("zoomOut"),
                pan: t("pan"),
                reset: t("reset"),
              },
            },
          },
        ],
        toolbar: {
          tools: {
            download: true,
          },
          export: {
            csv: {
              filename: props.title,
              columnDelimiter: ",",
              headerCategory: props.title,
              headerValue: max < 10000 ? t("count") : `${t("count")} *1000`,
              dateFormatter(timestamp) {
                return new Date(timestamp).toDateString();
              },
            },
            svg: {
              filename: props.title,
            },
            png: {
              filename: props.title,
            },
          },
        },
        id: props.title || "basic-bar",
        height: "100%",
      },
      legend: { show: false },
      plotOptions: {
        bar: {
          horizontal: props.type === "bar" ? true : false,
          columnWidth: "80%",
          endingShape: "rounded",
        },
      },
      xaxis: {
        categories: categories.map((i) => {
          return i?.split(" ");
        }),
        labels: {
          trim: true,
          hideOverlappingLabels: true,
          show: categories
            .map((i) => {
              return i?.split(" ");
            })
            .every((i) => i.length <= 2),
        },
      },
      dataLabels: {
        enabled: false,
      },
      stroke: {
        show: true,
        width: 2,
        colors: ["transparent"],
      },
      // grid: {
      //   xaxis: {
      //     tickPlacement: 'between',
      //     labels: {
      //       trim: true,

      //     },
      //     lines: {
      //       show: true,
      //     },
      //   },
      // },
      yaxis: {
        reversed: false,

        tickPlacement: "between",
        labels: {
          show: props.type === "bar" ? false : true,
          trim: true,
        },
        title: props.type === "bar" ? { text: props.title } : {},
        axisTicks: {
          show: true,
        },
      },

      tooltip: {
        shared: true,
        intersect: false,

        x: {
          formatter: function (
            value,
            { series, seriesIndex, dataPointIndex, w }
          ) {
            console.log(
              "x label",
              value,
              { series, seriesIndex, dataPointIndex, w },
              { chartOriginalData }
            );
            return `<strong>${w.globals.labels[dataPointIndex]}</strong>`;
          },
        },
        y: {
          formatter: function (
            value,
            { series, seriesIndex, dataPointIndex, w }
          ) {
            console.log(
              "y label",
              value,
              { series, seriesIndex, dataPointIndex, w },
              { chartOriginalData }
            );
            if (
              chartOriginalData[dataPointIndex]?.data
                .map((i) => i.value)
                .includes(w.globals.seriesNames[seriesIndex])
            )
              return `${w.globals.seriesNames[seriesIndex]}:${value}`;
          },
          title: {
            formatter: (seriesName) => "",
          },
        },
      },
    });
  };

  return (
    <div
      className="ape-chart bar"
      style={
        i18n.language === "ar" ? { direction: "ltr" } : { direction: "rtl" }
      }>
      <div className="col text-center">
        <h6
          title={t("ClickHereForDetails")}
          onClick={() => {
            let isChildChart = props.isChildChart;

            if (
              options &&
              series &&
              Object.keys(options).length &&
              series.length
            )
              props.onClickTitle({
                data: series,
                title: props.title,
                labels,
                isChildChart,
                chartOriginalData: props?.preparedChartData?.chartOriginalData,
              });
            //todo: show a toast there is no data to show in else
          }}
          style={{
            fontFamily: "NeoSansArabic",
            textAlign: "center",
            fontWeight: "bold",
            cursor: "pointer",
          }}>
          {props.title}
        </h6>
        {/* <img src={props.mapLogoSrc} className="map-pointer" alt="map logo" onClick={handleHeatMap} /> */}
      </div>
      {options && series && (!Object.keys(options).length || !series.length) ? (
        <h4 className="text-center"> لا يوجد بيانات للعرض</h4>
      ) : options && series && Object.keys(options).length && series.length ? (
        <ReactApexChart
          options={options}
          series={series}
          // height={'auto'}
          width={"100%"}
          type={"bar"}
        />
      ) : null}
    </div>
  );
}

export default BarChartComp;
