import React, { useState } from "react";
import OuterSearchResultDetails from "../outerSearchComponents/OuterSearchResultDetails";

export default function GeneralResultDetails(props) {
  React.useEffect(()=>{
    return ()=>{
      console.log("unmount GeneralResultDetails comp");
      // props.setLandBaseParcelData();
      props.resultDetailsDataRef.current.landBaseParcelData = null;
    }
  })
  return (

    <OuterSearchResultDetails {...props} />
  );
}
