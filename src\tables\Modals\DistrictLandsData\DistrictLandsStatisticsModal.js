import React, { useState, useEffect } from "react";
import { Modal, Row, Col, message, Tooltip } from "antd";
import { Spinner, Table } from "react-bootstrap";
import { useTranslation } from "react-i18next";

import {DownloadOutlined } from '@ant-design/icons'
import ExportCSVFile from "./ExportCSVFile";
import {
  getFeatureDomainName,
  getFromEsriRequest,
  getLayerId,
  queryTask,
} from "../../../helper/common_func";
import { PARCEL_LANDS_LAYER_NAME } from "../../../helper/constants";
import generateRandomColor from "../../../helper/utilsFunc";
import BarChartComp from "../../../screens/dashboard/dashboardCharts/ApexCharts/BarChart";

const DistrictLandStatisticsModal = (props) => {
  const handleOk = async () => {};
  const { t } = useTranslation("print", "layers", "common");
  const [statColorsTable, setStatColorsTable] = useState([]);
  const [hoverData, setHoverData] = useState(null);
  const [exportedData, setExportedData] = React.useState({
    dataSet: [],
    columns: [],
    labels: [],
    layerName: "",
    whereClause: "",
  });
  useEffect(() => {
    let settedData = [];
    if (props.shownData?.length) {
      console.log("before", { data: props.shownData });
      settedData = props.shownData
        // .sort((a, b) => b.value - a.value)
        .map((c, index) => {
          console.log(c.name);
          return {
            id: c.id,
            name: c.label,
            color:c?.color ? c?.color : generateRandomColor(),
            size: c.value,
            whereClause: c.whereClause,
            dontShowOnChart:c.dontShowOnChart,
            percentage: c.percentage ? Number(c.percentage).toFixed(2)*100 : 0
          };
        });
      setStatColorsTable(settedData);
    } else {
      if (hoverData) setHoverData(null);
      if (statColorsTable.length) setStatColorsTable([]);
    }
    return () => {
      setHoverData(null);
      setStatColorsTable([]);
      props.openDistrictLandsStatModal(false);
    };
  }, [props.showDistrictLandsStat]);

 async function exportCSVFile(evt, item) {
    if (evt.target !== evt.currentTarget && evt.currentTarget.querySelector("#main-elem-for-export")) return;
    if (!item?.whereClause || !item.size) {
      message.open({
        type: "info",
        content: t("common:noDataForExtract"),
      });
      return;
    }
    let mapInfo = await getFromEsriRequest(window.dashboardMapUrl + "?f=pjson");
      let layerID = getLayerId({
        info:{
          $layers:mapInfo
        }
      }, PARCEL_LANDS_LAYER_NAME);

    let queryParams = {
      url: window.dashboardMapUrl + "/" + layerID,
      notShowLoading: false,
      returnGeometry: false,
      where: item.whereClause,
    };
    queryTask({
      ...queryParams,
      callbackResult: ({ features }) => {
        if (features.length)
          getFeatureDomainName(features, layerID).then((feats) => {
            let reqData = feats.map((f) => {
              return { ...f.attributes };
            });
            setExportedData({
              dataSet: reqData,
              columns: props.landbaseParcelData.fields?.map((i) => i.fieldName),
              labels: props.landbaseParcelData.fields?.map((i) =>
                i?.alias &&
                (i?.alias).match(
                  "[\u0600-\u06ff]|[\u0750-\u077f]|[\ufb50-\ufbc1]|[\ufbd3-\ufd3f]|[\ufd50-\ufd8f]|[\ufd92-\ufdc7]|[\ufe70-\ufefc]|[\uFDF0-\uFDFD]"
                )
                  ? i.alias
                  : t(`layers:${i.alias}`)
              ),
              layerName: item.name.slice(4),
              whereClause: item.whereClause,
            });
          });
      },
    });
  }
  return (
    <Modal
      className="metaStatModal"
      visible={props.showDistrictLandsStat ? true : false}
      onOk={handleOk}
      onCancel={props.openDistrictLandsStatModal}
      width={"50%"}
    >
      <h5 className="statTitle mb-3">
        {t("common:districtLandsStatistics")} ({ props.districtData?.value})
      </h5>
      <Row align="top">
        <Col flex={2}>
          <Table className="table plan-lands-statistics-tbl metastatTable">
            <thead>
              <th>{t("print:statement")}</th>

              <th> {t("print:count")} </th>
              <th> {t("print:percent")} </th>
              <th> {t("print:drawKey")} </th>
              <th>{t("print:ExtractExcelFile")}</th>
            </thead>

            <tbody>
              {statColorsTable.map((s, index) => (
                <tr>
                  <td>{s.name}</td>
                  <td>{s.size}</td>
                  <td>%{(s.percentage).toFixed(2)}</td>
                  <td>
                    <p
                      className="colorBall"
                      style={{ background: s.color }}
                    ></p>
                  </td>
                  <td
                  className="text-center"
                    style={{ cursor: "pointer" }}
                    onClick={(e) => exportCSVFile(e, s)}
                  >
                    {exportedData.dataSet.length &&
                    s.whereClause === exportedData.whereClause ? (
                      <ExportCSVFile
                        isForceClick
                        isPlanLandModal
                        {...exportedData}
                      />
                    ) : (
                        <Tooltip
                        placement="topLeft"
                        title={t("common:extractExcelFile")}
                      >
                        <DownloadOutlined style={{fontSize:'2rem'}} />
                      </Tooltip>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </Table>
        </Col>
        <Col flex={3}>
          {statColorsTable.length ? (
            <div>

            <BarChartComp
            setHoverData={setHoverData}
              data={statColorsTable.filter((i) => !i.dontShowOnChart)}
              title={t("common:planLandsStatistics") + " (" +props.districtData?.value+")"}
            />
              </div>
          ) : (
            <Spinner animation="grow" />
          )}
        </Col>{" "}
      </Row>
      <h5 className="text-center white-color">
              {props.shownData.find((i) => i?.dontShowOnChart) && props.shownData.find((i) => i?.dontShowOnChart)?.label}: {props.shownData.find((i) => i?.dontShowOnChart)?.value}
            </h5>
      <div className="metaStatBtns">
        {" "}
        <button
          onClick={() => props.openDistrictLandsStatModal()}
          className="SearchBtn mt-3 w-25"
          size="large"
          htmlType="submit"
        >
          {t("print:close")}
        </button>
      </div>
    </Modal>
  );
};

export default DistrictLandStatisticsModal;
