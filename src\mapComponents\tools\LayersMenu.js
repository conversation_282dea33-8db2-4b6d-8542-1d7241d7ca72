import React from "react";
import Fade from "react-reveal/Fade";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTimes } from "@fortawesome/free-solid-svg-icons";
import TocComponent from "./TocComponent";

export default function LayersMenu(props) {
  return (
    <Fade left collapse>
      <div className="toolsMenu inquiryTool layersMenu">
        {/* <span
          style={{
            width: "100%",
            float: "left",
            textAlign: "left",
          }}
        >
          {" "}
          <FontAwesomeIcon
            className="close_icon"
            icon={faTimes}
            onClick={props.closeToolsData}
          />
        </span> */}
        <TocComponent
          languageState={props.languageState}
          mainData={props.mainData}
          map={props.map}
          closeToolsData={props.closeToolsData}
        />
      </div>
    </Fade>
  );
}
