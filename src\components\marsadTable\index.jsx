import React from "react";
import { useTranslation } from "react-i18next";

export default function MarsadTable(props) {
  const { t } = useTranslation("common");
  return props.data ? (
    <div style={{ padding : "10px"}}>
      <table
        class="table table-hover result-table"
        style={{ borderCollapse: "separate", borderSpacing: "0px 10px" }}
      >
        <thead style={{ backgroundColor: "#338C9A33", color: "#284587" }}>
          <th
            style={{
              textAlign: "center",
              padding: "10px",
              borderLeft: "2px solid #ddd",
            }}
          >
            {t("indicator")}
          </th>
          <th
            style={{
              textAlign: "center",
              padding: "10px",
              borderLeft: "2px solid #ddd",
            }}
          >
            {t("color")}
          </th>
          {props.data &&
            props.data[Object.keys(props.data)[0]].govs.map((gov) => {
              return (
                <th
                  style={{
                    textAlign: "center",
                    padding: "10px",
                    borderLeft: "2px solid #ddd",
                  }}
                >
                  {gov.attributes.GOV_NAME}
                </th>
              );
            })}
          {props.data &&
            props.data[Object.keys(props.data)[0]].govs.length == 1 && (
              <th
                style={{
                  textAlign: "center",
                  padding: "10px",
                  borderLeft: "2px solid #ddd",
                }}
              >
                {" "}
                {t("indicatorUnit")}{" "}
              </th>
            )}
        </thead>
        <tbody>
          {Object.keys(props.data).map((key, value) => {
            return (
              <tr>
                <td
                  style={{
                    padding: "5px",
                    textAlign: "center",
                    borderLeft: "2px solid #284587",
                    borderBottom: "0px",
                    verticalAlign: "middle",
                  }}
                >
                  {key}
                </td>
                <td
                  style={{
                    padding: "5px",
                    textAlign: "center",
                    borderLeft: "2px solid #284587",
                    borderBottom: "0px",
                    verticalAlign: "middle",
                  }}
                >
                  <div
                    style={{
                      backgroundColor: props.data[key].color,
                      margin: "5px",
                      width: "20px",
                      height: "20px",
                      marginLeft: "auto",
                      marginRight: "auto",
                    }}
                  ></div>
                </td>
                {props.data[key].govs.map((gov) => {
                  return (
                    <td
                      style={{
                        padding: "5px",
                        textAlign: "center",
                        borderLeft: "2px solid #284587",
                        borderBottom: "0px",
                        verticalAlign: "middle",
                      }}
                    >
                      {gov.attributes.INDICATOR_VALUE}
                    </td>
                  );
                })}
                {props.data[key].govs.length == 1 &&
                  props.data[key].govs.map((gov) => {
                    return (
                      <td
                        style={{
                          padding: "5px",
                          textAlign: "center",
                          borderLeft: "2px solid #284587",
                          borderBottom: "0px",
                          verticalAlign: "middle",
                        }}
                      >
                        {gov.attributes.INDICATOR_MEASURE}
                      </td>
                    );
                  })}
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  ) : (
    <></>
  );
}
