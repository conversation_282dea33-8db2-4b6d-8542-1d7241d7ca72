import React, { useState, useEffect, useCallback } from "react";
import { Input, Select, Button, message, Form } from "antd";
import { BgColorsOutlined, UploadOutlined } from "@ant-design/icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTimes } from "@fortawesome/free-solid-svg-icons";
import { withTranslation } from "react-i18next";
import Fade from "react-reveal/Fade";
import i18n from "../../i18n";
import axios from "axios";
import { Grid } from "@material-ui/core";
import styles from "../../sidemenu-Components/Deals.module.css";
import Upload from "../../components/Upload/Upload";
import {
  showGeneralDataTable,
  executeGPTool,
  showLoading,
} from "../../helper/common_func";
import { MdArrowDropDown } from "react-icons/md";

import closeIconFigma from "../../assets/icons/closeIconFigma.svg";

const { TextArea } = Input;
const { Option } = Select;

const UpdateLand = ({
  t,
  selectedFeatures,
  closeToolsData,
  Cleanup,
  map,
  onClose,
}) => {
  // State Management
  const [formValues, setFormValues] = useState({
    landesNumbers: "",
    streetNumbers: "",
    planNumbers: "",
    description: "",
    ticketTypeId: "",
    ticketTypeName: "",
    files: [],
    additionalFiles: [],
  });

  const [formErrors, setFormErrors] = useState({
    landesNumbers: [],
    streetNumbers: [],
    planNumbers: [],
    description: [],
    ticketTypeId: [],
    ticketTypeName: [],
    files: [],
    selectedFeatures: [],
  });

  const [formTouched, setFormTouched] = useState({
    landesNumbers: false,
    streetNumbers: false,
    planNumbers: false,
    description: false,
    ticketTypeId: false,
    ticketTypeName: false,
    files: false,
    selectedFeatures: false,
  });

  const [ticketTypes, setTicketTypes] = useState([]);
  const [appData, setAppData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedLandes, setSelectedLandes] = useState([]);
  const [landesNumbers, setLandesNumbers] = useState("");
  const [planNumbers, setPlanNumbers] = useState("");
  const [streetNumbers, setStreetNumbers] = useState("");
  const [selectedFeaturesState, setSelectedFeaturesState] = useState({
    landes: [],
    plans: [],
    streets: [],
  });
  const [fileList, setFileList] = useState([]);

  // Load ticket types and app data on mount
  useEffect(() => {
    const loadTicketTypes = async () => {
      try {
        const { data } = await axios.get(
          `${window.ApiUrl}/ticket-type?isMadinaty=true`
        );
        if (data.length) {
          const translations = {
            "تحديث قطعة الأرض": "Update Land Parcel",
            "تحديث المخطط": "Update Plan",
            "تحديث عرض الشارع": "Update Street Width",
            "تحديث البلك": "Update Block",
          };
          const localizedData = data.map((ticket) => ({
            ...ticket,
            originalName: ticket.name,
            name:
              i18n.language === "en"
                ? translations[ticket.name] || ticket.name
                : ticket.name,
          }));
          setTicketTypes(localizedData);
        }
      } catch (error) {
        message.error(t("common:generalErrorMessage"));
      }
    };

    const loadAppData = async () => {
      try {
        const { data } = await axios.get(
          `${window.ApiUrl}/applications/getall`
        );
        const app = data?.results.filter((item) => item.id === 32);
        setAppData(app);
      } catch (error) {
        message.error(t("common:generalErrorMessage"));
      }
    };

    loadTicketTypes();
    loadAppData();
  }, [t]);

  // Update selected features when props change
  useEffect(() => {
    if (selectedFeatures !== selectedLandes) {
      setSelectedLandes(selectedFeatures);

      const newLandesNumbers = Array.from(
        new Set(selectedFeatures.map((item) => item.attributes.PARCEL_PLAN_NO))
      ).join(", ");
      const newPlanNumbers = Array.from(
        new Set(selectedFeatures.map((item) => item.attributes.PLAN_NO))
      ).join(", ");
      const newStreetNumbers = Array.from(
        new Set(selectedFeatures.map((item) => item.attributes.STREET_FULLNAME))
      ).join(", ");

      const landesSpatialID = Array.from(
        new Set(
          selectedFeatures.map((item) => item.attributes.PARCEL_SPATIAL_ID)
        )
      );
      const planSpatialID = Array.from(
        new Set(
          selectedFeatures.map((item) =>
            item.attributes.PLAN_SPATIAL_ID
              ? item.attributes.PLAN_SPATIAL_ID
              : null
          )
        )
      );
      const streetObjectID = Array.from(
        new Set(
          selectedFeatures.map((item) =>
            item.attributes.STREET_FULLNAME ? item.attributes.OBJECTID : null
          )
        )
      );

      setLandesNumbers(newLandesNumbers);
      setPlanNumbers(newPlanNumbers);
      setStreetNumbers(newStreetNumbers);
      setSelectedFeaturesState({
        landes: landesSpatialID[0] ? landesSpatialID : [],
        plans: planSpatialID[0] ? planSpatialID : [],
        streets: streetObjectID[0] ? streetObjectID : [],
      });

      setFormValues((prev) => ({
        ...prev,
        landesNumbers: newLandesNumbers,
        planNumbers: newPlanNumbers,
        streetNumbers: newStreetNumbers,
      }));
    }
  }, [selectedFeatures, selectedLandes]);

  useEffect(() => {
    return () => {
      const customEvent = new CustomEvent("showPrintBox", {
        detail: { show: false },
      });
      document.dispatchEvent(customEvent);
    };
  }, []);

  const validateSelectedFeatures = useCallback((features) => {
    return (
      features.landes.length > 0 ||
      features.plans.length > 0 ||
      features.streets.length > 0
    );
  }, []);

  const validateInputs = useCallback(() => {
    const errors = {
      landesNumbers: [],
      description: [],
      ticketTypeId: [],
      ticketTypeName: [],
      files: [],
      selectedFeatures: [],
    };

    if (!validateSelectedFeatures(selectedFeaturesState)) {
      errors.selectedFeatures.push(
        t("common:validation.select at least one feature")
      );
    }

    if (!formValues.ticketTypeId) {
      errors.ticketTypeId.push(t("common:validation.select ticket type"));
    }

    if (!formValues.description.trim()) {
      errors.description.push(t("common:validation.This field is required"));
    }

    if (!formValues.files || formValues.files.length === 0) {
      errors.files.push(t("common:validation.upload file"));
    }

    setFormErrors(errors);
    return errors;
  }, [formValues, selectedFeaturesState, t]);

  // Handlers
  const handleInputChange = (field, value) => {
    setFormValues((prev) => ({ ...prev, [field]: value }));
    setFormTouched((prev) => ({ ...prev, [field]: true }));
    setTimeout(() => validateInputs(), 0);
  };

  const handleSelect = (value) => {
    const selectedTicket = ticketTypes.find((type) => type.id === value);
    setFormValues((prev) => ({
      ...prev,
      ticketTypeId: value,
      ticketTypeName: selectedTicket?.name || "",
    }));
    setFormTouched((prev) => ({ ...prev, ticketTypeId: true }));
    setTimeout(() => validateInputs(), 0);
  };

  const setFile = async ({ file, fileList }) => {
    if (file.status === "done") {
      try {
        const formData = new FormData();
        formData.append(`file[${0}]`, file.originFileObj);

        const { data } = await axios.post(
          `${window.ApiUrl}/uploadMultifiles`,
          formData
        );
        const fileData = data[0];
        const fileObject = {
          path: fileData.data,
          fileName: fileData.PrevFileName || file.name,
        };

        setFormValues((prev) => ({
          ...prev,
          files: [...prev.files, fileObject],
        }));
        setFormTouched((prev) => ({ ...prev, files: true }));
        message.success(t("sidemenu:uploadSuccess"));
        setTimeout(() => validateInputs(), 0);

        // Handle GIS file processing for specific file types
        const fileExt = file.name.split(".").pop().toLowerCase();
        let params,
          processingToolUrl,
          fileType = "cad",
          outputName = "output_value";

        if (["kmz", "kml"].includes(fileExt)) {
          params = { KML_File_Name: fileData.data };
          processingToolUrl = window.kmlToJSONGPUrl;
          fileType = "kmz";
        } else if (fileExt === "dwg") {
          params = { CAD_File_Name: fileData.data };
          processingToolUrl = window.cadToJsonGPUrl;
        }

        if (params && processingToolUrl) {
          showLoading(true);
          const userObj = JSON.parse(localStorage.getItem("user") || "{}");
          executeGPTool(
            `${processingToolUrl}?token=${userObj?.esriToken || ""}`,
            params,
            (result) => {
              showLoading(false);
              showGeneralDataTable({
                type: "importGisFile",
                data: result,
                map,
                uploadFileType: fileType,
                show: true,
              });
            },
            (error) => {
              showLoading(false);
              message.error(t("sidemenu:addFileToMapError"));
            },
            outputName,
            "submitJob",
            userObj?.esriToken
          );
        }
      } catch (error) {
        message.error(t("sidemenu:uploadFilesError"));
      }
    } else if (file.status === "error") {
      message.error(t("sidemenu:uploadFilesError"));
    }
  };

  const submitUpdateForm = async (e) => {
    e.preventDefault();
    setFormTouched({
      landesNumbers: true,
      description: true,
      ticketTypeId: true,
      ticketTypeName: true,
      files: true,
      selectedFeatures: true,
    });

    const errors = validateInputs();
    if (Object.values(errors).every((errorList) => errorList.length === 0)) {
      setIsLoading(true);
      try {
        const body = {
          description: `${formValues.description} -- ${formValues.landesNumbers} -- ${formValues.planNumbers} -- ${formValues.streetNumbers}`,
          ticketTypeId: formValues.ticketTypeId,
          ticketTypeName: formValues.ticketTypeName,
          attachments:
            (formValues.files?.length &&
              formValues.additionalFiles?.length && [
                ...formValues.files?.split(", "),
                ...formValues.additionalFiles?.split(", "),
              ]) ||
            (formValues.files?.length && [...formValues.files?.split(", ")]) ||
            (formValues.additionalFiles?.length && [
              ...formValues.additionalFiles?.split(", "),
            ]),
          appId: appData[0]?.id,
          appName: appData[0]?.translate_ar_caption,
          supportingInfo: `
            Selected Features:
            Landes: [${selectedFeaturesState.landes.join(", ")}]
            Plans: [${selectedFeaturesState.plans.join(", ")}]
            Streets: [${selectedFeaturesState.streets.join(", ")}]
          `,
        };

        const { data } = await axios.post(`${window.ApiUrl}/ticket`, body);
        message.success(
          `${t("common:sendSuccessMessageForTicket")} ${t("common:order_no")} ${
            data.id
          }`,
          5
        );

        // Reset form
        setFormValues({
          landesNumbers: "",
          streetNumbers: "",
          planNumbers: "",
          description: "",
          ticketTypeId: "",
          ticketTypeName: "",
          files: [],
          additionalFiles: [],
        });
        setFormTouched({
          landesNumbers: false,
          streetNumbers: false,
          planNumbers: false,
          description: false,
          ticketTypeId: false,
          ticketTypeName: false,
          files: false,
          selectedFeatures: false,
        });
        setFormErrors({
          landesNumbers: [],
          streetNumbers: [],
          planNumbers: [],
          description: [],
          ticketTypeId: [],
          ticketTypeName: [],
          files: [],
          selectedFeatures: [],
        });
        setLandesNumbers("");
        setStreetNumbers("");
        setPlanNumbers("");
        setSelectedLandes([]);
        closeToolsData();
        Cleanup();
      } catch (error) {
        message.error(t("common:generalErrorMessage"), 5);
      } finally {
        setIsLoading(false);
      }
    } else {
      const firstError = Object.values(errors).find(
        (errorList) => errorList.length > 0
      );
      if (firstError) {
        message.error(firstError[0], 5);
      }
    }
  };

  return (
    <Fade left collapse>
      <div
        className="toolsMenu inquiryTool layersMenu"
        style={{
          position: "absolute",
          top: "50px",
          left: "15px",
          width: "400px",
        }}
      >
        {/* <span
          style={{
            width: "100%",
            float: "left",
            textAlign: "left",
            marginLeft: "5px",
          }}
        >
          <FontAwesomeIcon
            icon={faTimes}
            style={{
              marginTop: "5px",
              marginRight: "5px",
              cursor: "pointer",
              color: "#284587",
            }}
            onClick={() => {
              closeToolsData();
              Cleanup();
            }}
          />
        </span> */}
        <div
          className="Heading_tocComponent"
          style={{ flexDirection: "row-reverse" }}
        >
          <img
            onClick={() => {
              closeToolsData();
              Cleanup();
              onClose();
            }}
            src={closeIconFigma}
            alt=""
          />
          <label>{t("common:updateLand")}</label>
        </div>

        <section
          className="toc"
          style={{
            direction: i18n.language === "ar" ? "rtl" : "ltr",
            padding: "5px",
            width: "auto",
            height: "450px",
          }}
        >
          {/* <span
            style={{ fontSize: "20px", fontWeight: "bold", color: "#284587" }}
          >
            {t("common:updateLand")}
          </span>{" "} */}
          <span
            style={{ fontSize: "20px", fontWeight: "bold", color: "#284587" }}
          >
            {t("common:mainMunicipalities")}
          </span>
          <div style={{ marginTop: "5px", color: "#284587" }}>
            <div style={{ fontSize: "15px", fontWeight: "bold" }}>
              {selectedLandes[0]?.attributes.CITY_NAME ||
                selectedLandes[0]?.attributes.MUNICIPALITY_NAME}
            </div>
          </div>
          <div
            style={{
              backgroundColor: "#FFFFFF99",
              borderRadius: "12px",
              padding: "10px",
              marginBlock: "10px",
              boxShadow: "0px 0px 10px 0px #ddd",
            }}
          >
            {selectedLandes[0]?.attributes.DISTRICT_NAME && (
              <Grid
                container
                // spacing={2}
                className="attribute-row"
                style={{ padding: "8px 0", borderBottom: "1px solid #f1f1f1" }}
              >
                <Grid item xs={6}>
                  <div className={styles.attributeKey}>
                    {t("generalSearch:district")}
                  </div>
                </Grid>
                <Grid item xs={6} className="px-0">
                  <div className={styles.attributeValue}>
                    {selectedLandes[0]?.attributes.DISTRICT_NAME}
                  </div>
                </Grid>
              </Grid>
            )}

            {planNumbers.length > 0 && (
              <Grid
                container
                // spacing={2}
                className="attribute-row"
                style={{ padding: "8px 0", borderBottom: "1px solid #f1f1f1" }}
              >
                <Grid item xs={6}>
                  <div className={styles.attributeKey}>
                    {t("generalSearch:planNumber")}
                  </div>
                </Grid>
                <Grid item xs={6} className="px-0">
                  <div className={styles.attributeValue}>{planNumbers}</div>
                </Grid>
              </Grid>
            )}

            {streetNumbers.length > 0 && (
              <Grid
                container
                // spacing={2}
                className="attribute-row"
                style={{ padding: "8px 0", borderBottom: "1px solid #f1f1f1" }}
              >
                <Grid item xs={6}>
                  <div className={styles.attributeKey}>
                    {t("generalSearch:streetName")}
                  </div>
                </Grid>
                <Grid item xs={6} className="px-0">
                  <div className={styles.attributeValue}>{streetNumbers}</div>
                </Grid>
              </Grid>
            )}

            {landesNumbers && (
              <Grid
                container
                // spacing={2}
                className="attribute-row"
                style={{ padding: "8px 0", borderBottom: "1px solid #f1f1f1" }}
              >
                <Grid item xs={6}>
                  <div className={styles.attributeKey}>
                    {t("generalSearch:parcelNumber")}
                  </div>
                </Grid>
                <Grid item xs={6} className="px-0">
                  <div className={styles.attributeValue}>{landesNumbers}</div>
                </Grid>
              </Grid>
            )}
          </div>
          <Grid
            item
            xs={12}
            style={{
              paddingTop: "0",
              paddingBottom: "0",
              display: "flex",
              flexDirection: "column",
              gap: "10px",
            }}
          >
            {/* <Select
              virtual={false}
              className="searchInput englishFont"
              showSearch
              placeholder={t("common:selectTicketType")}
              style={{
                width: "100%",
                borderColor:
                  formErrors.ticketTypeId.length > 0 ? "red" : "black",
                borderRadius: "10px",
                overflow: "hidden",
                border: "1px solid",
                marginBottom: "10px",
              }}
              onChange={handleSelect}
              value={formValues.ticketTypeId}
              optionFilterProp="v"
              filterOption={(input, option) =>
                option.v.toLowerCase().includes(input.toLowerCase())
              }
            >
              <Option disabled value="">
                {t("common:selectTicketType")}
              </Option>
              {ticketTypes.map((m) => (
                <Option v={m.name} key={m.id} value={m.id}>
                  {m.name}
                </Option>
              ))}
            </Select> */}

            <div>
              <Form.Item
                label={t("layers:orderType")}
                className="select-cust"
                style={{ height: "auto", overflow: "hidden" }}
              >
                <Select
                  virtual={false}
                  // suffixIcon={<MdArrowDropDown size={24} color="#284587" />}
                  className="searchInput englishFont"
                  showSearch
                  placeholder={t("common:selectTicketType")}
                  style={{
                    width: "100%",
                    // borderColor:
                    //   formErrors.ticketTypeId.length > 0 ? "red" : "black",
                    // borderRadius: "10px",
                    // overflow: "hidden",
                    // border: "1px solid",
                    // marginBottom: "10px",
                  }}
                  onChange={handleSelect}
                  value={formValues.ticketTypeId}
                  optionFilterProp="v"
                  filterOption={(input, option) =>
                    option.v.toLowerCase().includes(input.toLowerCase())
                  }
                >
                  <Option disabled value="">
                    {t("common:selectTicketType")}
                  </Option>
                  {ticketTypes.map((m) => (
                    <Option v={m.name} key={m.id} value={m.id}>
                      {m.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
              {formErrors.ticketTypeId.length > 0 &&
                formTouched.ticketTypeId && (
                  <div
                    style={{
                      color: "red",
                      fontSize: "12px",
                      marginBottom: "10px",
                    }}
                  >
                    {formErrors.ticketTypeId[0]}
                  </div>
                )}
            </div>

            {/* <TextArea
              name="description"
              onChange={(e) => handleInputChange("description", e.target.value)}
              value={formValues.description}
              placeholder={t("common:description")}
              rows={4}
              style={{
                marginBottom: "10px",
                borderRadius: "10px",
                borderColor:
                  formErrors.description.length > 0 ? "red" : undefined,
              }}
            /> */}

            <div>
              <Form.Item
                label={"وصف الطلب"}
                className="select-cust"
                style={{ height: "auto" }}
              >
                <TextArea
                  onChange={(e) =>
                    handleInputChange("description", e.target.value)
                  }
                  value={formValues.description}
                  placeholder={t("common:description")}
                  style={{
                    backgroundColor: "transparent",
                    border: "none",
                    height: "100px",
                  }}
                />
              </Form.Item>
              {formErrors.description.length > 0 && formTouched.description && (
                <div
                  style={{
                    color: "red",
                    fontSize: "12px",
                    marginBottom: "10px",
                  }}
                >
                  {formErrors.description[0]}
                </div>
              )}
            </div>

            {/* <Upload
              name="fileUpload"
              multiple={true}
              onChange={setFile}
              accept="image/*,.kmz,.kml,.dwg"
              customRequest={({ file, onSuccess, onError }) => {
                setTimeout(() => onSuccess("ok"), 0);
              }}
              showUploadList={true}
            >
              <Button icon={<UploadOutlined />}>{t("sidemenu:upload")}</Button>
            </Upload> */}
            <Upload
              label={"إرفاق ملف"}
              name="fileUpload"
              fileType={"image/*,.pdf"}
              multiple={true}
              onInputChange={(value) => {
                handleInputChange("files", value);
                //message.success(t("sidemenu:uploadSuccess"));
              }}
              value={formValues.files}
              error={formTouched.files && formErrors.files.length > 0}
              // helperText={formTouched.files && formErrors.files[0]}
              // loginUI="loginUI"
            />
            {/* {formErrors.files.length > 0 && formTouched.files && (
              <div style={{ color: "red", fontSize: "12px", marginBottom: "10px" }}>
                {formErrors.files[0]}
              </div>
            )} */}

            {/* <Button
              type="primary"
              onClick={submitUpdateForm}
              loading={isLoading}
              block
              style={{ marginTop: "20px", borderRadius: "10px" }}
            >
              {t("common:submit")}
            </Button> */}
            <Button
              className="addMark mt-2 "
              type="primary"
              onClick={submitUpdateForm}
              loading={isLoading}
              // disabled={formValues.bookmark !== "" ? false : true}
            >
              إرسال
            </Button>
          </Grid>
        </section>
      </div>
    </Fade>
  );
};

export default withTranslation("common")(UpdateLand);
