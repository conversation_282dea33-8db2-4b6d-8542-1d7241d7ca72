import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import Generic<PERSON>hart from "../GenericApexCharts";
import TextCountSection from "../TextCountSection";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faExpandArrowsAlt } from "@fortawesome/free-solid-svg-icons";
import { Modal } from "antd";

function LeftSideChartsContainer({
  changeMapTablesShow,
  mapTablesShow,
  layersNames,
  selectedLayers, // Array of selected layer names
  map,
  queryData,
}) {
  const { t } = useTranslation("dashboard");
  const [isModalOpen, setIsModalOpen] = useState(null);
  const [chartsDataArr, setChartsDataArr] = useState([]);

  // Debug logging
  useEffect(() => {
    console.log('Selected Layers:', selectedLayers);
    console.log('All Layers:', layersNames);
  }, [selectedLayers, layersNames]);

  // Effect to update charts when selected layers change
  useEffect(() => {
    // Ensure selectedLayers is an array and not empty
    if (Array.isArray(selectedLayers) && selectedLayers.length > 0) {
      // Map selected layers to their chart data
      const chartDataArray = selectedLayers.map(selectedLayer => {
        // Find the layer in layersNames that matches the selected layer
        const layerDataWithCharts = layersNames.find(
          (lay) => lay?.englishName === selectedLayer
        );
        
        console.log(`Processing layer: ${selectedLayer}`, layerDataWithCharts);
        
        return {
          data: layerDataWithCharts?.dashboardCharts || [],
          layerName: selectedLayer
        };
      }).filter(layer => layer.data.length > 0); // Remove layers with no charts
      
      console.log('Processed Chart Data:', chartDataArray);
      
      setChartsDataArr(chartDataArray);
    } else {
      // Clear charts if no layers are selected
      setChartsDataArr([]);
    }
  }, [selectedLayers, layersNames]);

  // Render nothing if no charts
  if (!chartsDataArr.length) {
    console.log('No charts to render');
    return null;
  }

  return (
    <div className="left-side-chart-container">
      {chartsDataArr.map((layerCharts, layerIndex) => (
        <React.Fragment key={layerCharts.layerName}>
          {/* Top Charts Section */}
          <div
            className={
              layerCharts.data.filter((c) => c.position === "top").length <= 2
                ? "charts-layout-col"
                : "charts-layout-row"
            }
            style={{
              flexBasis: 
                layerCharts.data.length === 3 ? "60%" :
                layerCharts.data.length === 2 ? "50%" : "auto",
              flexGrow: layerCharts.data.filter((c) => c.position === "bottom").find((i) => i.dependentLayer) ? "0.8" : "1"
            }}
          >
            {layerCharts.data
              .filter((c) => c.position === "top")
              .map((item, index) => (
                <div
                  className={`${item?.relatedChildChart ? '' : 'normal-chart-item'} mapSquare`}
                  key={`${layerCharts.layerName}-chart-top-${index}`}
                >
                  {/* Expand Modal for Charts */}
                  {!item?.relatedChildChart && (
                    <>
                      <FontAwesomeIcon
                        onClick={() => setIsModalOpen(`${layerCharts.layerName}-chart-${index}`)}
                        className="faplusChart"
                        icon={faExpandArrowsAlt}
                      />
                      <Modal
                        cancelText="إغلاق"
                        visible={isModalOpen === `${layerCharts.layerName}-chart-${index}`}
                        onCancel={() => setIsModalOpen(null)}
                        footer={null}
                        width="50%"
                      >
                        <GenericChart
                          title={t(item.alias)}
                          onClickTitle={changeMapTablesShow}
                          sideTblTitle={mapTablesShow}
                          chartObj={item}
                          map={map}
                          selectedLayer={layerCharts.layerName}
                          layersNames={layersNames}
                          type={item.chartType}
                          queryData={queryData}
                        />
                      </Modal>
                    </>
                  )}

                  {/* Chart Rendering */}
                  <GenericChart
                    title={t(item.alias)}
                    onClickTitle={changeMapTablesShow}
                    sideTblTitle={mapTablesShow}
                    chartObj={item}
                    selectedLayer={layerCharts.layerName}
                    map={map}
                    layersNames={layersNames}
                    type={item.chartType}
                    queryData={queryData}
                  />
                </div>
              ))}
          </div>

          {/* Bottom Charts Section */}
          {layerCharts.data
            .filter((c) => c.position === "bottom")
            .map((item, index) => (
              <div 
                className="last-chart-item mapSquare text-count-info" 
                key={`${layerCharts.layerName}-chart-bottom-${index}`}
              >
                {item.dependentLayer ? (
                  <TextCountSection
                    title={t(item.alias)}
                    dependentLayer={item.dependentLayer}
                    chartObj={item}
                    layersNames={layersNames}
                    map={map}
                    selectedLayer={layerCharts.layerName}
                    type={item.chartType}
                    queryData={queryData}
                  />
                ) : (
                  <GenericChart
                    title={t(item.alias)}
                    onClickTitle={changeMapTablesShow}
                    sideTblTitle={mapTablesShow}
                    chartObj={item}
                    layersNames={layersNames}
                    map={map}
                    selectedLayer={layerCharts.layerName}
                    type={item.chartType}
                    queryData={queryData}
                  />
                )}
              </div>
            ))}
        </React.Fragment>
      ))}
    </div>
  );
}

export default LeftSideChartsContainer;