import top_bar_clear from "../../assets/images/interactive-map/topbar_clear.svg";
import top_bar_move from "../../assets/images/interactive-map/topbar_stopDraw.svg";
import top_bar_redo from "../../assets/images/interactive-map/topbar_redo.svg";
import top_bar_undo from "../../assets/images/interactive-map/topbar_undo.svg";
import delete_icon from "../../assets/images/sidemenu/delete.svg";
import i18n from "../../i18n";
import { useEffect, useState } from "react";
import eventBus from "../../helper/EventBus";
import { useRef } from "react";
import { useTranslation } from "react-i18next";
import { Button, Modal, Tooltip } from "antd";
import * as watchUtils from "@arcgis/core/core/watchUtils";

export default function InteractiveMapTopBar(props) {
  const { t } = useTranslation("common");

  const [isShow, setIsShow] = useState(false);
  const [deleteModalVisible, setdeleteModalVisible] = useState(false);
  const eventListeners = useRef();
  const graphicsHistory = useRef();
  const [map, setMap] = useState();
  const [undoCount, setUndoCount] = useState(0);
  const [redoCount, setRedoCount] = useState(0);

  useEffect(() => {
    eventBus.on("setShowInteractivEditBar", (data) => {
      console.log("setShowInteractivEditBar",data);
      
      setIsShow(data.message);
      eventListeners.current = data.eventListeners
        ? data.eventListeners.current
        : null;
      graphicsHistory.current = data.graphicsHistory
        ? data.graphicsHistory.current
        : null;
      let map = data.map ? data.map : null;
      setMap(map);
    });
  }, []);

  let watcher;

  useEffect(() => {
    if (map) {
      watcher = watchUtils.on(
        map.findLayerById("InteractiveMapGraphicLayer"),
        "graphics",
        "change",
        (event) => {
          if (graphicsHistory.current) {
            window.__undoStackLength = graphicsHistory.current.undoStack.filter(
              (item) =>
                typeof item == "object" && !Array.isArray(item)
                  ? !item.graphic.has_animation
                  : item
            ).length;
            setUndoCount(
              graphicsHistory.current.undoStack.filter((item) =>
                typeof item == "object" && !Array.isArray(item)
                  ? !item.graphic.has_animation
                  : item
              ).length
            );
            setRedoCount(
              graphicsHistory.current.redoStack.filter((item) =>
                typeof item == "object" && !Array.isArray(item)
                  ? !item.graphic.has_animation
                  : item
              ).length
            );
          }
        }
      );
    }

    return () => {
      if (watcher) {
        watcher.remove();
      }
      setUndoCount(0);
      setRedoCount(0);
    };
  }, [map]);

  useEffect(() => {
    return () => {
      eventBus.remove("setShowInteractivEditBar");
    };
  }, []);

  const tools = [
    {
      id: 1,
      icon: top_bar_undo,
      label: t("undo"),
      onClick: () => {
        handleUndo();
      },
    },
    {
      id: 2,
      icon: top_bar_redo,
      label: t("redo"),
      onClick: () => {
        handleRedo();
      },
    },
    {
      id: 3,
      icon: top_bar_move,
      label: t("pan"),
      onClick: () => {
        handleStopDrawing();
      },
    },
    {
      id: 4,
      icon: delete_icon,
      label: t("delete"),
      onClick: () => {
        handleDelete();
      },
    },
    {
      id: 5,
      icon: top_bar_clear,
      label: t("clearAll"),
      onClick: () => {
        showDeleteAll();
      },
    },
  ];

const handleDelete = () => {
  if (!eventListeners.current) {
    console.warn("eventListeners.current is null");
    return;
  }
  eventListeners.current.disableSelectedTool(null);
  eventListeners.current.disableGraphicState(undefined);
  eventListeners.current.sketch.current.handleSelectTool({
    name: "delete",
    src: delete_icon,
  });
};

const handleStopDrawing = () => {
  if (!eventListeners.current) {
    console.warn("eventListeners.current is null");
    return;
  }
  eventListeners.current.disableSelectedTool(null);
  eventListeners.current.disableGraphicState(undefined);
  eventListeners.current.sketch.current.cancelDraw();
  if (eventListeners.current.animation?.length > 0) {
    eventListeners.current.animation.forEach((element) => {
      element.current?.remove();
    });
  }
};

const submitDeleteAll = () => {
  if (!eventListeners.current || !graphicsHistory.current) {
    console.warn("eventListeners.current or graphicsHistory.current is null");
    return;
  }
  eventListeners.current.disableSelectedTool(null);
  eventListeners.current.disableGraphicState(undefined);
  eventListeners.current.sketch.current.cancelDraw();
  graphicsHistory.current.redoStack = [];
  graphicsHistory.current.undoStack = [];
  setUndoCount(0);
  setRedoCount(0);
  map.findLayerById("InteractiveMapGraphicLayer").graphics.removeAll();
  setdeleteModalVisible(false);
};

  const showDeleteAll = () => {
    setdeleteModalVisible(true);
  };

  const handleUndo = () => {
    if (!graphicsHistory.current || graphicsHistory.current.undoStack.length === 0) {
    console.warn("graphicsHistory.current is null or undoStack is empty");
    return;
  }
    let graphicLayer = map.findLayerById("InteractiveMapGraphicLayer");
    let stackLength = graphicsHistory.current.undoStack.length;
    let lastGraphic = graphicsHistory.current.undoStack[stackLength - 1];
    if (typeof lastGraphic == "object" && !Array.isArray(lastGraphic)) {
      if (lastGraphic.action == "add") {
        if (lastGraphic.graphic._animateId) {
          let lineGraphic = graphicsHistory.current.undoStack[stackLength - 2];
          graphicsHistory.current.redoStack.push(lineGraphic);
          graphicsHistory.current.redoStack.push(lastGraphic);
          graphicLayer.graphics.removeMany([
            lineGraphic.graphic,
            lastGraphic.graphic,
          ]);
          graphicsHistory.current.undoStack.pop();
          graphicsHistory.current.undoStack.pop();
        } else {
          graphicsHistory.current.redoStack.push(lastGraphic);
          graphicLayer.graphics.remove(lastGraphic.graphic);
          graphicsHistory.current.undoStack.pop();
        }
      } else {
        if (lastGraphic.graphic._animateId) {
          let lineGraphic = graphicsHistory.current.undoStack[stackLength - 2];
          graphicsHistory.current.redoStack.push(lineGraphic);
          graphicsHistory.current.redoStack.push(lastGraphic);
          graphicLayer.graphics.addMany([
            lineGraphic.graphic,
            lastGraphic.graphic,
          ]);
          graphicsHistory.current.undoStack.pop();
          graphicsHistory.current.undoStack.pop();
        } else {
          graphicsHistory.current.redoStack.push(lastGraphic);
          graphicLayer.graphics.add(lastGraphic.graphic);
          graphicsHistory.current.undoStack.pop();
        }
      }
    } else {
      graphicsHistory.current.redoStack.push(lastGraphic);
      graphicLayer.graphics.addMany(lastGraphic);
      graphicsHistory.current.undoStack.pop();
    }
  };

  const handleRedo = () => {
    if (!graphicsHistory.current || graphicsHistory.current.redoStack.length === 0) {
    console.warn("graphicsHistory.current is null or redoStack is empty");
    return;
  }
    let graphicLayer = map.findLayerById("InteractiveMapGraphicLayer");
    let stackLength = graphicsHistory.current?.redoStack?.length;
    let lastGraphic = graphicsHistory.current.redoStack[stackLength - 1];
    if (typeof lastGraphic == "object" && !Array.isArray(lastGraphic)) {
      if (lastGraphic.action == "add") {
        if (lastGraphic.graphic._animateId) {
          let lineGraphic = graphicsHistory.current.redoStack[stackLength - 2];
          graphicsHistory.current.undoStack.push(lineGraphic);
          graphicsHistory.current.undoStack.push(lastGraphic);
          graphicLayer.graphics.addMany([
            lineGraphic.graphic,
            lastGraphic.graphic,
          ]);
          graphicsHistory.current.redoStack.pop();
          graphicsHistory.current.redoStack.pop();
        } else {
          graphicsHistory.current.undoStack.push(lastGraphic);
          graphicLayer.graphics.add(lastGraphic.graphic);
          graphicsHistory.current.redoStack.pop();
        }
      } else {
        if (lastGraphic.graphic._animateId) {
          let lineGraphic = graphicsHistory.current.redoStack[stackLength - 2];
          graphicsHistory.current.undoStack.push(lineGraphic);
          graphicsHistory.current.undoStack.push(lastGraphic);
          graphicLayer.graphics.removeMany([
            lineGraphic.graphic,
            lastGraphic.graphic,
          ]);
          graphicsHistory.current.redoStack.pop();
          graphicsHistory.current.redoStack.pop();
        } else {
          graphicsHistory.current.undoStack.push(lastGraphic);
          graphicLayer.graphics.remove(lastGraphic.graphic);
          graphicsHistory.current.redoStack.pop();
        }
      }
    } else {
      graphicsHistory.current.undoStack.push(lastGraphic);
      graphicLayer.graphics.removeMany(lastGraphic);
      graphicsHistory.current.redoStack.pop();
    }
  };

  return (
    <>
      <div
        className="topBar"
        style={
          i18n.language === "ar"
            ? {
                right: props.openDrawer ? "56%" : "50%",
                transform: `translateX(${props.openDrawer ? "56%" : "50%"})`,
                display: isShow ? "block" : "none",
              }
            : {
                left: props.openDrawer ? "56%" : "50%",
                transform: `translateX(${props.openDrawer ? "-56%" : "-50%"})`,
                display: isShow ? "block" : "none",
              }
        }
      >
        {/* <div
          className="images"
          style={{ display: "flex", flexDirection: "row-reverse", gap: "20px", color: "black" }}
        >
          {tools.map((tool) => (
            <div
              key={tool.id}
              style={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
              }}
            >
              <Tooltip title={tool.label} placement="top">
                <div className="image" onClick={tool.onClick}>
                  {tool.id === 1 && undoCount > 0 && (
                    <div className="badge">{undoCount}</div>
                  )}
                  {tool.id === 2 && redoCount > 0 && (
                    <div className="badge">{redoCount}</div>
                  )}
                  <img src={tool.icon} alt="tool" />
                </div>
              </Tooltip>
            </div>
          ))}
        </div> */}
      </div>
      <Modal
        title={t("deleteGraphicsConfirmation")}
        centered
        visible={deleteModalVisible}
        //onOk={() => afterEditModal()}
        onCancel={() => setdeleteModalVisible(false)}
        okText={t("yes")}
        cancelText={t("no")}
      >
        <div
          style={{
            display: "flex",
            gap: "10px",
            marginTop: "10px",
            justifyContent: "end",
          }}
        >
          <Button
            type="primary"
            style={{
              backgroundColor: "#b45333",
            }}
            onClick={() => {
              submitDeleteAll();
            }}
          >
            {t("yes")}
          </Button>

          <Button
            type="primary"
            style={{
              backgroundColor: "#b45333",
            }}
            onClick={() => setdeleteModalVisible(false)}
          >
            {t("no")}
          </Button>
        </div>
      </Modal>
    </>
  );
}
