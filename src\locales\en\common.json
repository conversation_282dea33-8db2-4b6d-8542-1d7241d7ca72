{"SearchForService": "Search for a service", "outerActiveSearch": "To activate the search for the current geographical area", "uploadFilesError": "An error occur while uploading files", "availableServices": "Available services on this land", "clickOnMap": "Click on the required site to identify", "chooseLayer": "Choose layer", "notAvailable": "Not available", "constructionLicense": "Construction license", "storeLicense": "Store license", "municipality": "Municipality Name", "chooseMunicipality": "Choose municipality", "choosePlans": "Choose plan", "district": "District Name", "districtName": "Choose district name", "documentNo": "Enter plan document num.", "notAvailableData": "Sorry, no data available", "extractKML": "Extract kml file", "saveLocationToBookMark": "The current location is saved in the favorites list", "removed": "Removed", "edited": "Edited successfully", "bookmark": "Add Bookmark", "enterBookmark": "Enter a bookmark", "editBookmarkName": "Edit bookmark name", "bookmarkName": "Bookmark name", "edit": "Edit", "cancel": "Cancel", "enterText": "Please, enter your text", "stopPaint": "Stop Drawing", "clickToStopPaint": "Click to stop drawing on map", "deletePaintElemnt": "Remove a Graphic", "circle": "Circle", "text": "Text", "point": "Point", "searchLayer": "Search Layer", "searchLayerSelect": "Select search layer", "distance": "Distance (m2)", "distance2": "Distance", "without": "Without", "addFilterGroup": "Add filter group", "removeFilter": "Remove filter", "filter": "Filter", "confirm": "Confirm", "close": "Close", "removeRow": "Remove row", "value": "Value", "selectDate": "Click here to select date", "enterValue": "Please enter value", "enterSubAct": "Enter a sub-activity", "enteMainAct": "Enter main activity", "field": "Field", "chooseParam": "Please choose a parameter from the list", "param": "Parameter", "addNewRow": "Add new row", "removeGroup": "Remove row", "selectMenuField": "Please select field from menu", "search": "Search", "noLayer": "No layer selected", "chooseLayerToresult": "Choose layer to appear results", "noResults": "No results", "cancelSearch": "Cancel search", "filteredSucc": "Filtered successfully", "orderSucc": "Ordered successfully", "stat": "Statistics", "layerFilter": "Filter layers", "distanceRule": "The distance should be between 10 and 100,000.", "clickMapToSearch": "Please click on the map to start the search", "noDataAvail": "Sorry, no data available", "areaCalc": "Area Calculation", "measureDisLength": "Measuring Distances and Lengths", "locationCoord": "Location Coordinates", "longitude": "Longitude", "Latitude": "Latitude", "longitudeSelect": "Select longitude", "LatitudeSelect": "Select latitude", "x": "X", "y": "Y", "xCoor": " X coordinates", "yCoor": " Y coordinates", "clickMapXY": "Click on the map to find the latitude, longitude, x and y coordinates of the selected point", "decimalCoor": "Decimal coordinates", "degrees": "Degrees", "chooseDeg": "Choose degree", "minutes": "Minutes", "chooseMin": "Choose minute", "seconds": "Seconds", "chooseSec": " <PERSON><PERSON> second", "degMinSec": "Degrees-Minutes-Seconds", "decimalDeg": " Decimal degrees ", "east": "East", "north": "North", "dropSysSelect": "Choose drop system", "dropSys": "Drop system", "dropTypeSelect": "Choose drop type", "dropType": "Drop type", "geographic": "Geographic", "metric": "Metric", "menu": "<PERSON><PERSON>", "results": "Results", "geoSearch": "Geographical search", "retrievError": "An error occurred while retrieving data", "Transparency": "Transparency", "mainLayer": "Layers", "measuringUnit": "Measuring unit", "code": "Code", "indicator": "Indicator", "color": "Color", "addPointer": "Add indicator", "showres": "Show results", "chooseChartType": "Choose a chart type", "indicatorUnit": "Indicator unit of measure", "clickMapDrawPoint": "Click on the map in the desired place to draw a point", "clickWordText": "Click on the word text, then enter the text, then click on the map in the place where the text appears.", "enterText2": "Please, Enter Text", "freePolyg": "Free-hand Polygon", "clickDrawToDelete": "Click on the drawing element to be erased on the map.", "clickMapDrag": "Click on the map and start dragging the mouse to draw a polygon with free lines, then double-click the mouse to finish drawing", "polygon": "Polygon", "clickDrawPoly": "Click on the map to draw a polygon, then double-click the mouse to finish drawing.", "clickMapSolidLines": "Click on the map to draw solid lines, then double-click the mouse to finish drawing.", "clickMapRec": "Click on the map to draw a rectangle and you can control its size by dragging the mouse horizontally or vertically.", "rectangle": "Rectangle", "clickMapCirc": "Click on the map to draw a circle and you can control its size by dragging the mouse horizontally or vertically.", "solidLines": "Solid lines", "disConnectLines": "Disconnected lines", "indicatorAddedVefore": "This indicator has been added before.", "downloadApp": "To download map explorer app for mobile (Madinty)", "importantLinks": "Important links", "amanaWebsite": "Amana website", "ElecServices": "Electronic Services", "BaladPortal": "Balady portal", "MinistryofMunicipal": "Ministry of Municipal and Rural Affairs", "eGovYosr": "E-Government Program - Yosr", "extractExcelFile": "Extract excel file", "extractRoyalLandReport": " Export Royal Lands Report", "extractPrivateLandReport": " Export Private Lands Report", "royalLandReport": " Royal Lands Report", "salesLandReport": " Sales Lands Report", "privateLandReport": " Private Lands Report", "rightsReserv": "All rights reserved - Eastern Region", "chooseTimeContext": "Choose the time context", "yearly": "yearly", "monthly": "monthly", "daily": "daily", "chooseFromCalendar": "Choose from the calendar", "adminBoundary": " Adminstration Boundary", "mainMunicipalities": "Municipilities", "secMunicipalities": "Sub Municipilities", "plans": "Plans", "districts": "Districts", "searchWith": "Search With ", "notFoundValue": "No value", "noDataForSort": "No Data Available to sort", "NoDataAvailForStatistics": "No Data Available for Statistics", "allInLabel": "All", "sessionFinished": "Your login session is finished. Please relogin", "import_files": "Import Files", "my_files": "My Files", "planNoLandsStatistics": "Plan Num. Data", "count": "Count", "export": "Export", "enterReqField": "Please Enter Required Fields", "enterAnotherField": "Please Enter Aother Field with Municipility Name", "enterMun": "Please Municipality is required", "enterPlanNoOrParcelNo": "Please Enter Plan No. or Parcel No.", "noDataForExtract": "No Data availabe for export", "doesntReflectGDB": "This land doesn't reflect on geodatabase", "featureDoesntReflectGDB": "This feature does not reflect on geodatabase", "planLandsStatistics": "Statistics of plan lands", "districtLandsStatistics": "Statistics of district lands", "plannedLandNum": "Planned Lands Num.", "generalServicesLandsNum": "General Services Lands Num.", "assetsLandNum": "Assets Lands Num.", "investLandsNum": "Invest Lands Num.", "licensedLandsNum": "Licensed Lands Num.", "withElectricLandsNum": "Lands with electric Num.", "farzLandsNum": "Farz Lands Num.", "damgLandsNum": "Damg Lands Num.", "commercialLandsNum": "Commercial Lands Num.", "resedntialLandsNum": "Residenial Lands Num.", "allocatedLandsNum": "Allocated Lands Num.", "royalLandsNum": "Royal Lands Num.", "salesLandsNum": "Sales Lands Num.", "usedLandsNum": "Exploited Lands Num.", "nonUsedLandsNum": "Non-Exploited Lands Num.", "noImageAvailable": "No Images for preview", "download": "Download File", "view": "View File", "imageArchive": "Gallery Archive", "unplannedParcelArchive": "Unplanned Parcel Archive", "parcelNumber": "Pa<PERSON>el <PERSON>", "planNumber": "Plan Number", "streetName": "Street Name", "description": "Description", "selectTicketType": "Select Ticket Type", "sendSuccessMessageForTicket": "Ticket has been sent successfully", "order_no": "Order No.", "requests": "Requests", "Area": "Area", "drawingIsRequired": "Drawing is required.", "drawNameRequired": "Draw name is required.", "ConfirmSave": "Confirm Save", "NewNameRequired": "Drawing name is already existed ! please add a new name", "editDrawingName": "Edit drawing name", "DrawingName": "Drawing Name", "NewNameEmpty": "Please update drawing name", "drawingSavedSuccessfully": "Drawing saved successfully", "deletionConfirmation": "Are you sure you want to delete drawing : ", "bookMarDeletionConfirmation": "Are you sure you want to delete : ", "yes": "Yes", "no": "No", "drawingDeletedSuccessfully": "Drawing deleted successfully", "drawingNamedEditSuccessfully": "Drawing name updated successfully", "SameOldNewName": "New drawing name is same as old one", "ErrorRequest": "Error ! please try again later", "BookmarkNameDuplication": "Can't add more than a bookmark with same name , please add a new name", "EmptyBookmarkName": "Bookmark name is empty , please add a bookmark name", "MapNameRequired": "Map name is required", "MapUrlRequired": "Map URL is required", "MapNameDuplication": "Map name is already existed ! please add a new name", "showMore": "Show More", "showLess": "Show Less", "procedures": "Procedures", "emptyRegionName": "please add a region name", "newRegionNameRequired": "Region name is already existed ! please enter a new region name", "editRegionName": "Edit region name", "SameOldNewRegionName": "New region name is same as old one", "regionNamedEditSuccessfully": "Region name updated successfully", "regionNamedAddSuccessfully": "Region name added successfully", "regionDeletedSuccessfully": "Region deleted successfully", "deleteRegionConfirmation": "Are you sure you want to delete region : ", "regionMustBeSelected": "A region must be selected", "emptyZoneName": "Please add a zone name", "newZoneNameRequired": "Zone name is already existed ! please enter a new zone name", "zoneNamedEditSuccessfully": "Zone name edited successfully", "zoneNamedAddSuccessfully": "Zone name added successfully", "SameOldNewZoneName": "New zone name is same as old one", "editZoneName": "Edit zone name", "zoneDeletedSuccessfully": "Zone deleted successfully", "deleteZoneConfirmation": "Are you sure you want to delete zone : ", "mapDeletedSuccessfully": "Map deleted successfully", "deleteDrawingConfirmation": "Are you sure you want to delete map : ", "share": "Share", "search_select": "Search or Select", "public": "Public", "persons": "Persons", "departments": "Departments", "name": "Name", "department": "Department", "emptyMapName": "Please a map name", "mapSavedSuccessfully": "Map saved successfully", "enterMapName": "Enter map name", "confirmSave": "Confirm save", "NoShareChoice": "Please check sharing type !", "sharedSuccessfully": "Shared successfully", "CannotDeleteRegion": "Sorry , region can't be deleted as it contains a zone or more", "CannotDeleteZone": "Sorry , zone can't be deleted as it has an interactive map or more", "deleteGraphicsConfirmation": "Are you sure you want to clear all graphics ? graphics will not be retrieved again.", "mapEditedSuccessfully": "Map edited successfully", "undo": "Undo", "redo": "Redo", "pan": "Stop", "clearAll": "Clear-All", "alreadySelected": "Selected already !", "delete": "Delete", "useSameImageUrl": "Do you want to use another image ?", "invalidFileType": "Invalid file type !", "invalidFileSize": "Max file size is 2 MB", "straight_line": "straight line", "free_hand": "free draw", "line_with_arrows": "line with arrows", "half_spinner": "loading", "line_right_arrow": "line right arrow", "line_left_arrow": "line left arrow", "location": "location", "imageUploadSettings": "Please ensure that the image is at least 40x40 pixels and has a maximum size of 160x160 pixels (aspect ratio 1:1)", "fileUploadSettings": "Add files to a specific location and when clicked they open in a separate screen", "point_text": "draw text", "emptyGraphics": "No graphics to save", "drawnGraphicsWarning": "Drawn graphics on map will be lost and can't be retrieved again !!", "continue": "Continue", "fish": "fish", "dolphin": "dolphin", "turtle": "turtle", "deer": "deer", "scorpion": "scorpion", "bird": "bird", "frog": "frog", "tree": "tree", "seaweed": "seaweed", "mangrove": "mangrove", "gecko": "gecko", "coralreefs": "coral-reefs", "region": "region", "zone": "zone", "explorer": "explorer", "drone": "drone", "car": "car", "boat": "boat", "hunt": "hunt", "cut_wood": "cut wood", "garbage": "garbage", "camel": "camel", "babon": "babon", "dead_bird": "dead bird", "building": "building", "tent": "tent", "flag": "flag", "chair": "chair", "castle": "castle", "passenger": "passenger", "ruler": "ruler", "rocks": "rocks", "NoDataWarning": "No data to show", "useSameFileUrl": "Do you want to use another file ?", "image_gallery": "Gallery", "no-results": "No results", "from": "From", "to": "To"}