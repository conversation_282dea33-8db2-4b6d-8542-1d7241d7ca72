import React, { useEffect, useState, forwardRef } from "react";
import styles from "../sidemenu-Components/Deals.module.css";
import trackingStyles from "../sidemenu-Components/Tracking.module.css";
import FloatingLabelTextarea from "../mapComponents/tools/more/FloatingLabelTextarea";
import FloatingLabelInput from "../mapComponents/tools/more/FloatingLabelInput";
import Axios from "axios";
import closeImg from "../../src/assets/icons/close.svg";
import { Box, Grid, Dialog, Slide } from "@mui/material";
import { useTranslation } from "react-i18next";
import { ExpandLess, ExpandMore } from "@mui/icons-material";
import { getDistrictNameById } from "../helper/common_func";
import { isNaN } from "lodash";
import { Button, Form, Input } from "antd";
import Upload from "../components/Upload/Upload";

const { TextArea } = Input;

const UpdateDataLandView = ({ setIsOpen, ticketData, features, map }) => {
  const { t, i18n } = useTranslation(["common"]);
  const [districtName, setDistrictName] = useState(
    features[0]?.attributes.DISTRICT_NAME
  );
  const [formValues, setFormValues] = useState({
    landesNumbers: ticketData.description.split("--")?.[1]?.trim() || "",
    planNumbers: ticketData.description.split("--")?.[2]?.trim() || "",
    streetNumbers: ticketData.description.split("--")?.[3]?.trim() || "",
    description: ticketData.description.split("--")?.[0]?.trim(),
    ticketTypeId: ticketData.ticketTypeId,
  });
  const [isVisible, setIsVisible] = useState(true);
  useState(() => {
    Axios.get(`${window.ApiUrl}/ticket-type`, {
      params: { isMadinaty: true },
    }).then(({ data }) => {
      if (data.length !== 0) {
        setFormValues({
          ...formValues,
          ticketTypeName: data.find((r) => r.id == formValues.ticketTypeId)
            .name,
        });
      }
    });
  }, [formValues.selectedLandes]);
  const [isOpenContent, setIsOpenContent] = useState(false);

  const toggleContent = () => {
    setIsOpenContent(!isOpenContent);
  };

  useEffect(() => {
    if (!isNaN(+districtName)) {
      getDistrictNameById(map, features[0]?.attributes.DISTRICT_NAME).then(
        (res) => setDistrictName(res)
      );
    }
  }, []);

  const handleClose = () => {
    setIsOpen(false);
    map.graphics?.clear();
    map?.findLayerById("highLightGraphicLayer")?.removeAll();
    map.findLayerById("SketchLayer")?.removeAll();
  };

  return (
    <>
      {/* <div className={styles.SearchLandTitle} style={{ marginBottom: "10px" }}>
        <h3 className={styles.title}>
          <img src={closeImg} alt="" onClick={handleClose} />
          <span>{t("common:applyTicket")}</span>
        </h3>
        <div style={{ display: "flex", alignItems: "center" }}>
          <span
            class={
              ticketData.isClosed
                ? trackingStyles.finished
                : trackingStyles.running
            }
          >
            {t("common:ticket_no")} : {ticketData.title.match(/(\d+\/?)+/g)[0]}
          </span>
        </div>
      </div> */}

      <Grid container spacing={1}>
        <Grid item xs={12}>
          <div className={`${styles.attributeContainer}`}>
            {/* <div
              className={`${styles.attributeHeader} px-2 py-1`}
              style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                cursor: "pointer",
              }}
              onClick={toggleContent}
            >
              {!isOpenContent ? <ExpandLess /> : <ExpandMore />}
            </div> */}
            <div
              className={`${styles.attributeContent} ${
                !isOpenContent ? styles.contentOpen : styles.contentClosed
              } mb-2 px-2 py-1`}
              style={{
                textAlign: i18n.language === "en" ? "left" : "right",
              }}
            >
              <Grid
                container
                spacing={2}
                className="attribute-row"
                style={{
                  padding: "8px 0",
                  borderBottom: "1px solid #f1f1f1",
                }}
              >
                <Grid item xs={6}>
                  <div className={styles.attributeKey}>
                    {t("common:municipality")}
                  </div>
                </Grid>
                <Grid item xs={6} className="px-0">
                  <div className={styles.attributeValue}>
                    {features[0]?.attributes.CITY_NAME ||
                      features[0]?.attributes.MUNICIPALITY_NAME}
                  </div>
                </Grid>
              </Grid>
              {districtName && (
                <Grid
                  container
                  spacing={2}
                  className="attribute-row"
                  style={{
                    padding: "8px 0",
                    borderBottom: "1px solid #f1f1f1",
                  }}
                >
                  <>
                    <Grid item xs={6}>
                      <div className={styles.attributeKey}>
                        {t("generalSearch:district")}
                      </div>
                    </Grid>
                    <Grid item xs={6} className="px-0">
                      <div className={styles.attributeValue}>
                        {districtName}
                      </div>
                    </Grid>
                  </>
                </Grid>
              )}
              {formValues.planNumbers.length > 0 && (
                <Grid
                  container
                  spacing={2}
                  className="attribute-row"
                  style={{
                    padding: "8px 0",
                    borderBottom: "1px solid #f1f1f1",
                  }}
                >
                  <>
                    <Grid item xs={6}>
                      <div className={styles.attributeKey}>
                        {t("generalSearch:planNumber")}
                      </div>
                    </Grid>
                    <Grid item xs={6} className="px-0">
                      <div className={styles.attributeValue}>
                        {formValues.planNumbers
                          .split(",")
                          .map((r) => r.trim())
                          .join(", ")}
                      </div>
                    </Grid>
                  </>
                </Grid>
              )}
              {formValues.streetNumbers.length > 0 && (
                <Grid
                  container
                  spacing={2}
                  className="attribute-row"
                  style={{
                    padding: "8px 0",
                    borderBottom: "1px solid #f1f1f1",
                  }}
                >
                  <>
                    <Grid item xs={6}>
                      <div className={styles.attributeKey}>
                        {t("generalSearch:streetName")}
                      </div>
                    </Grid>
                    <Grid item xs={6} className="px-0">
                      <div className={styles.attributeValue}>
                        {formValues.streetNumbers
                          .split(",")
                          .map((r) => r.trim())
                          .join(", ")}
                      </div>
                    </Grid>
                  </>
                </Grid>
              )}
              {formValues.landesNumbers.length > 0 && (
                <Grid
                  container
                  spacing={2}
                  className="attribute-row"
                  style={{
                    padding: "8px 0",
                    borderBottom: "1px solid #f1f1f1",
                  }}
                >
                  <>
                    <Grid item xs={6}>
                      <div className={styles.attributeKey}>
                        {t("generalSearch:parcelNumber")}
                      </div>
                    </Grid>
                    <Grid item xs={6} className="px-0">
                      <div className={styles.attributeValue}>
                        {formValues.landesNumbers
                          .split(",")
                          .map((r) => r.trim())
                          .join(", ")}
                      </div>
                    </Grid>
                  </>
                </Grid>
              )}
            </div>
          </div>
        </Grid>

        <div
          className={`${styles.attributeHeader} px-2 py-1`}
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            cursor: "pointer",
            margin: "auto",
          }}
          onClick={toggleContent}
        >
          {!isOpenContent ? <ExpandLess /> : <ExpandMore />}
        </div>

        <Grid
          item
          xs={12}
          style={{
            paddingTop: "0",
            paddingBottom: "0",
            marginTop: "1vh",
            marginBottom: "1vh",
          }}
        >
          {/* <FloatingLabelInput
            label={t("layers:orderType")}
            placeholder={t("layers:selectOrderType")}
            value={formValues.ticketTypeName}
            disabled={true}
          /> */}

          <Form
            className="GeneralForm"
            layout="vertical"
            name="validate_other"
            style={{
              width: "100%",
              padding: "0 10px",
              display: "flex",
              flexDirection: "column",
              gap: "10px",
            }}
            // onFinish={addBookMark}
          >
            <Form.Item
              label={t("layers:orderType")}
              rules={[
                {
                  message: "ابحث برقم الطلب",
                  required: true,
                },
              ]}
              className="select-cust"
            >
              <Input
                type="search"
                name="bookmark"
                value={formValues.ticketTypeName}
                disabled={true}
                placeholder={t("layers:selectOrderType")}
              />
            </Form.Item>

            <Form.Item
              label={"وصف الطلب"}
              rules={[
                {
                  message: "ابحث برقم الطلب",
                  required: true,
                },
              ]}
              className="select-cust"
              style={{ height: "auto" }}
            >
              <TextArea
                placeholder={t("layers:selectOrderDesc")}
                value={formValues.description}
                disabled={true}
                style={{
                  backgroundColor: "transparent",
                  border: "none",
                }}
              />
            </Form.Item>

            <Form.Item>
              <Upload
                label={"إضافة صور/ملفات اخري"}
                name="attachment"
                fileType={"image/*,.kmz,.kml,.dwg,.pdf"}
                multiple={true}
                onInputChange={(value) => {
                  setFormValues({ ...formValues, attachment: value });
                  //message.success(t("sidemenu:uploadSuccess"));
                }}
                // value={
                //   (!editModalVisible && formValues.attachment?.split(", ")) ||
                //   []
                // }
                disabled={true}
              />
            </Form.Item>

            <Button
              className="addMark mt-2 "
              size="large"
              htmlType="submit"
              // disabled={formValues.bookmark !== "" ? false : true}
            >
              إرسال
            </Button>
          </Form>
        </Grid>
        {/* <Grid item xs={12} style={{ paddingTop: "0", paddingBottom: "0" }}>
          <FloatingLabelTextarea
            label={t("layers:orderDesc")}
            placeholder={t("layers:selectOrderDesc")}
            // helperText={
            //   formTouched.description && formErrors.description[0]
            // }
            value={formValues.description}
            disabled={true}
          />
        </Grid> */}
      </Grid>
    </>
  );
};

export default UpdateDataLandView;
