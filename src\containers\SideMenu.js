import React, { useEffect, useState } from "react";
import { Button, Upload, message, Steps } from "antd";
import axios from "axios";
import { useTranslation } from "react-i18next";
import { Fade } from "react-reveal";
import { useLocation } from "react-router-dom";
import { Link } from "react-router-dom";
import ImportFile from "../sidemenu-Components/ImportFile";
import { UploadOutlined } from "@ant-design/icons";
import { faChevronCircleRight } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import BarChartIcon from "@mui/icons-material/BarChart";
import { Tooltip } from "@mui/material";
import Divider from "@mui/material/Divider";
import MuiDrawer from "@mui/material/Drawer";
import IconButton from "@mui/material/IconButton";
import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import ListItemIcon from "@mui/material/ListItemIcon";
import ListItemText from "@mui/material/ListItemText";
import { styled } from "@mui/material/styles";
import arabicIcon from "../assets/images/sidemenu/arabicIcon.svg";
import bookmark from "../assets/images/sidemenu/bookmark.svg";
import setting from "../assets/images/services/setting.svg";
import cooSearch from "../assets/images/sidemenu/cooSearch.svg";
import desSearch from "../assets/images/sidemenu/desSearch.svg";
import englishIcon from "../assets/images/sidemenu/englishIcon.svg";
import general from "../assets/images/sidemenu/general.svg";
import google from "../assets/images/sidemenu/google.svg";
import importFile from "../assets/images/sidemenu/importFile.svg";
import Marsed from "../assets/images/sidemenu/marsed.svg";
import measurTool from "../assets/images/sidemenu/measurTool.svg";
import incidentsIcon from "../assets/images/sidemenu/<EMAIL>";
import Paint from "../assets/images/sidemenu/Paint.svg";
import Phone from "../assets/images/sidemenu/Phone.svg";
import print from "../assets/images/sidemenu/print.svg";
import LayersMenu from "../sidemenu-Components/LayersMenu";
import layers_menu from "../assets/images/sidemenu/layers_menu.svg";
import menu_open from "../assets/images/menu_open.svg";
import searchIcon from "../assets/icons/search.svg";
import menuIcon from "../assets/icons/menu.svg";
import resultIcon from "../assets/icons/results.svg";

import {
  SearchOutlined,
  MenuFoldOutlined,
  UnorderedListOutlined,
} from "@ant-design/icons";
import {
  showGeneralDataTable,
  executeGPTool,
  showLoading,
} from "../helper/common_func";
import { modulesIDs } from "../helper/constants";
import BookMark from "../sidemenu-Components/BookMark";
//===========
import Setting from "../sidemenu-Components/Setting";
import CoordinatesSearch from "../sidemenu-Components/CoordinatesSearch";
import GeneralSearch from "../sidemenu-Components/GeneralSearch";
import GeoRange from "../sidemenu-Components/GeoRange";
import KmlGoogle from "../sidemenu-Components/KmlGoogle";
import MarsedComponent from "../sidemenu-Components/MarsedComponent";
import MeasureTool from "../sidemenu-Components/MeasureTool";
import UpdatingRequests from "../sidemenu-Components/updatingRequests";
import InteravtiveMap from "../sidemenu-Components/interavtiveMap/interavtiveMap";
import NearestServiceSearch from "../sidemenu-Components/NearestServiceSearch";
import OuterSearchMainPage from "../sidemenu-Components/outerSearchComponents/OuterSearchMainPage";
import Painting from "../sidemenu-Components/Painting";
import PrintComponent from "../sidemenu-Components/PrintComponent";
import { MdKeyboardDoubleArrowLeft } from "react-icons/md";
import { RiArrowRightDoubleLine } from "react-icons/ri";

import { CiSearch } from "react-icons/ci";
import { IoLayersOutline } from "react-icons/io5";
import { BsPrinter } from "react-icons/bs";
import { TbCurrentLocation } from "react-icons/tb";
import { RxRulerSquare } from "react-icons/rx";
import { MdOutlinePalette } from "react-icons/md";
import { BiBarChartAlt2 } from "react-icons/bi";
import { LuBookmarkMinus } from "react-icons/lu";
import { CgNotes } from "react-icons/cg";
import { TbArrowDownToArc } from "react-icons/tb";
import { SiGoogle } from "react-icons/si";
import { FaRegUser } from "react-icons/fa";
import { FaRegCircleQuestion } from "react-icons/fa6";
import { RiGlobalLine } from "react-icons/ri";
import { IoCallOutline } from "react-icons/io5";
import { CgSearchLoading } from "react-icons/cg";
import { useNavigate } from "react-router-dom";

export default function SideMenu(props) {
  const navigate = useNavigate();
  const handleClick = () => {
    navigate("/");
  };

  const { t, i18n } = useTranslation("sidemenu", "common");
  const location = useLocation();
  const resultDetailsData = React.useRef({
    detailsData: null,
    landBaseParcelData: null,
  });
  // const landBaseParcelDataRef = React.useRef(null);
  // const [landBaseParcelData, setLandBaseParcelData] = React.useState();

  // const DrawerHeader = styled("div")(({ theme }) => ({
  //   display: "flex",
  //   alignItems: "center",
  //   justifyContent: "flex-end",
  //   padding: theme.spacing(0, 1),
  //   ...theme.mixins.toolbar,
  // }));

  const Drawer = styled(MuiDrawer, {
    shouldForwardProp: (prop) => prop !== "open",
  })(({ theme, open }) => ({
    width:
      location.pathname.substring(location.pathname.lastIndexOf("/") + 1) ===
        "generalSearch" ||
        location.pathname.substring(location.pathname.lastIndexOf("/") + 1) ===
        "search" ||
        location.pathname.substring(location.pathname.lastIndexOf("/") + 1) ===
        "coordinateSearch" ||
        location.pathname.substring(location.pathname.lastIndexOf("/") + 1) ===
        "marsed"
        ? 450
        : location.pathname.substring(location.pathname.lastIndexOf("/") + 1) ==
          ""
          ? 316
          : location.pathname == process.env.PUBLIC_URL
            ? 316
            : location.pathname.substring(
              location.pathname.lastIndexOf("/") + 1
            ) === "metaDataSearch"
              ? 316
              : 370,
    flexShrink: 0,
    whiteSpace: "nowrap",
    boxSizing: "border-box",
    ...(open && {
      ...openedMixin(theme),
      "& .MuiDrawer-paper": openedMixin(theme),
    }),
    ...(!open && {
      ...closedMixin(theme),
      "& .MuiDrawer-paper": closedMixin(theme),
    }),
  }));
  const openedMixin = (theme) => ({
    width:
      location.pathname.substring(location.pathname.lastIndexOf("/") + 1) ===
        "generalSearch" ||
        location.pathname.substring(location.pathname.lastIndexOf("/") + 1) ===
        "search" ||
        location.pathname.substring(location.pathname.lastIndexOf("/") + 1) ===
        "coordinateSearch" ||
        location.pathname.substring(location.pathname.lastIndexOf("/") + 1) ===
        "marsed"
        ? 450
        : location.pathname.substring(location.pathname.lastIndexOf("/") + 1) ==
          ""
          ? 250
          : location.pathname == process.env.PUBLIC_URL
            ? 250
            : location.pathname.substring(
              location.pathname.lastIndexOf("/") + 1
            ) === "metaDataSearch"
              ? 250
              : 370,
    transition: theme.transitions.create("width", {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.enteringScreen,
    }),
    overflowX: "hidden",
  });

  const closedMixin = (theme) => ({
    transition: theme.transitions.create("width", {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.leavingScreen,
    }),
    overflowX: "hidden",
    width: `calc(${theme.spacing(7)} + 1px)`,
    [theme.breakpoints.up("sm")]: {
      width: `calc(${theme.spacing(9)} + 1px)`,
    },
  });
  const [sideLinks, setSideLinks] = useState([
    {
      id: 1,
      name: "sideLinks.generalSearch",
      icon: CiSearch,
      to: "generalSearch",
    },
    // { id: 4, name: "sideLinks.print", icon: print, to: "print" },
    {
      id: 5,
      name: "sideLinks.coordinateSearch",
      icon: TbCurrentLocation,
      to: "coordinateSearch",
    },

    {
      id: 7,
      name: "sideLinks.measurementTools",
      icon: RxRulerSquare,
      to: "measurement",
    },
    { id: 8, name: "sideLinks.paint", icon: MdOutlinePalette, to: "painting" },
    {
      id: 10,
      name: "sideLinks.bookmark",
      icon: LuBookmarkMinus,
      to: "bookmark",
    }, //======================================================
    {
      id: 12,
      name: "sideLinks.setting",
      icon: TbCurrentLocation,
      to: "setting",
    },

    { id: 16, name: "sideLinks.contact", icon: IoCallOutline, to: "contact" },
    {
      id: 17,
      name: "sideLinks.updatingRequests",
      icon: RxRulerSquare,
      to: "updatingRequests",
    },
    {
      id: 18,
      name: "sideLinks.interactiveMap",
      icon: RxRulerSquare,
      to: "interactiveMap",
    },
  ]);
  useEffect(
    () => {
      console.log("side menu", { location });
      if (props?.mainData) {
        let mainFunctions = props?.mainData?.mainFunctions || [];
        let reqMainFuncIDs = [];
        if (mainFunctions.length) {
          for (let index = 0; index < mainFunctions.length; index++) {
            let item = mainFunctions[index];
            let moduleIds = item.groups_permissions.map((i) => i.module_id);
            reqMainFuncIDs = [...reqMainFuncIDs, ...moduleIds];
          }
        }
        let sideLinksOfMainFunc = [
          {
            id: 2,
            name: "sideLinks.layersMenu",
            icon: layers_menu,
            to: "layersMenu",
            module_id: modulesIDs.mapLayersView,
          },
          {
            id: 14,
            name: "sideLinks.importFiles",
            module_id: modulesIDs.exportFilesModule,
            icon: TbArrowDownToArc,
            to: "import",
          },
          {
            id: 9,
            name: "sideLinks.marsad",
            module_id: modulesIDs.marsadModule,
            className: "marsadImg",
            icon: Marsed,
            to: "marsed",
          },
          {
            id: 12,
            name: "sideLinks.incidentsDashboard",
            module_id: modulesIDs.incidentsModule,
            icon: incidentsIcon,
            dashboardType: "incidentsDashboard",
            to: "",
          },
          {
            id: 13,
            name: "sideLinks.landsDashboard",
            module_id: modulesIDs.landsKPIsDashboard,
            icon: incidentsIcon,
            dashboardType: "landsDashboard",
            to: "",
          },
          {
            id: 6,
            module_id: modulesIDs.searchByAttrModule,
            name: "sideLinks.MetaSearch",
            icon: CgSearchLoading,
            to: "metaDataSearch",
          },
          {
            id: 15,
            name: "sideLinks.exportGoogle",
            module_id: modulesIDs.importGoogle,
            icon: SiGoogle,
            to: "kml",
          },
          // {
          //   id: 2,
          //   name: "sideLinks.layersMap",
          //   module_id: modulesIDs.layersMap,
          //   icon: google,
          //   to: "",
          // },
          // {
          //   id: 3,
          //   name: "sideLinks.interactiveMap",
          //   module_id: modulesIDs.interactiveMap,
          //   icon: google,
          //   to: "",
          // },
          // {
          //   id: 17,
          //   name: "sideLinks.updatingRequests",
          //   module_id: modulesIDs.updatingRequests,
          //   icon: CgNotes,
          //   to: "",
          // },
        ];

        let reqMainFuncSideLinks = sideLinksOfMainFunc.filter((sL) =>
          reqMainFuncIDs.includes(sL.module_id)
        );
        let reqSideLinksToSet = [...reqMainFuncSideLinks, ...sideLinks].sort(
          (a, b) => (a.id < b.id ? -1 : 1)
        );
        setSideLinks(reqSideLinksToSet);
      }
      return () => {
        console.log("unmount SideMenu.js");
      };
    },
    [
      /*props.mainData*/
    ]
  );
  useEffect(() => {
    console.log("mount side menu", { location });
    if (
      location.pathname.substring(location.pathname.lastIndexOf("/") + 1) !==
      "generalSearch" &&
      resultDetailsData.current.detailsData
    ) {
      resultDetailsData.current.detailsData = null;
      resultDetailsData.current.landBaseParcelData = null;
    }
  }, [location.pathname]);
  // const openImportTable = (e) => {
  //   props.handleDrawerOpen();
  //   props.openImportTable();
  // };
  const openSearchTable = (e) => {
    props.openSearchTable();
    props.handleDrawerCloseGeneral();
  };

  const generalOpenSearchInput = (e) => {
    setOpenResultMenu(false);
    setOpenSearchInputs(true);
    setOpenResultDetails(false);
    props.handleDrawerOpen();
  };
  /*General Search Functions*/
  const [generalSearchInputsShown, setOpenSearchInputs] = useState(true);
  const [generalResultMenuShown, setOpenResultMenu] = useState(false);
  const [generalResultDetailsShown, setOpenResultDetails] = useState(false);
  const [activeStep, setActiveStep] = useState(1);

  const generalOpenResultMenu = (e) => {
    setOpenResultMenu(true);
    setOpenSearchInputs(false);
    setOpenResultDetails(false);
    resultDetailsData.current.detailsData = null;
    resultDetailsData.current.landBaseParcelData = null;
    //props.showResultCardsHelp();
    setActiveStep(2);
  };
  const generalOpenSearchInputs = (e) => {
    setOpenResultMenu(false);
    setOpenSearchInputs(true);
    setOpenResultDetails(false);
    resultDetailsData.current.detailsData = null;
    resultDetailsData.current.landBaseParcelData = null;
    setActiveStep(1);
  };
  const generalOpenResultdetails = (e) => {
    setOpenResultMenu(false);
    setOpenSearchInputs(false);
    setOpenResultDetails(true);
    props.showCardDetailsHelp();
    setActiveStep(3);
  };

  const setFile = (e) => {
    const status = e.file.status;
    if (status !== "uploading") {
      console.log(e.file, e.fileList);
    }
    if (status === "done") {
      const formData = new FormData();
      formData.append(
        `file[${0}]`,
        e.fileList[e.fileList.length - 1].originFileObj
      );

      axios
        .post(window.ApiUrl + "uploadMultifiles", formData)
        .then((res) => {
          let fileExt = e.fileList[e.fileList.length - 1].name.split(".");
          fileExt = fileExt[fileExt.length - 1];

          let params;
          let processingToolUrl;
          let fileType = "cad";
          let outputName = "output_value";

          if (fileExt == "kmz" || fileExt == "kml") {
            params = {
              KML_File_Name: res.data[0].data,
            };
            processingToolUrl = window.kmlToJSONGPUrl;
            outputName = "output_value";
            fileType = "kmz";
          } else if (fileExt == "dwg") {
            params = {
              CAD_File_Name: res.data[0].data,
            };
            processingToolUrl = window.cadToJsonGPUrl;
            outputName = "output_value";
          }

          showLoading(true);
          let userObj = localStorage.getItem("user");
          if (userObj) userObj = JSON.parse(userObj);
          executeGPTool(
            `${processingToolUrl}?token=${userObj ? userObj.esriToken : ""}`,
            params,
            (result) => {
              showLoading(false);

              showGeneralDataTable({
                type: "importGisFile",
                data: result,
                map: props.map,
                uploadFileType: fileType,
                show: true,
              });
            },
            (error) => {
              showLoading(false);
              message.error(t("sidemenu:addFileToMapError"));
            },
            outputName,
            "submitJob",
            userObj?.esriToken
          );
        })
        .catch((err) => {
          message.error(t("sidemenu:uploadFilesError"));
        });
    }
  };

  const { Step } = Steps;

  return (
    <Drawer
      variant="permanent"
      open={props.open}
      anchor="right"
      className="SideMenu elden"
      id={
        location.pathname.substring(location.pathname.lastIndexOf("/") + 1) ===
          "search" ||
          location.pathname.substring(location.pathname.lastIndexOf("/") + 1) ===
          "generalSearch" ||
          location.pathname.substring(location.pathname.lastIndexOf("/") + 1) ===
          "coordinateSearch"
          ? "SearchSidemenu"
          : ""
      }
    >
      {props.open &&
        (location.pathname === process.env.PUBLIC_URL ||
          location.pathname === `${process.env.PUBLIC_URL}/`) ? (
        <div className="drawerHeder">
          <p className="sideMenuTitle">{t("sidemenu:mainTitle")}</p>
          <p className="TitleEdition">
            {t("sidemenu:titleEdition")} <span className="editionNo">2.0</span>
          </p>

          <Link to="/">
            <IconButton
              onClick={props.handleDrawerCloseButton}
              className="closeMenuIcon openSideMenuHelp"
              color="inherit"
              aria-label="open drawer"
              edge="start"
            >
              <FontAwesomeIcon
                icon={faChevronCircleRight}
                style={{
                  cursor: "pointer",
                }}
              />
            </IconButton>
          </Link>
        </div>
      ) : props.open && location.pathname !== "/" ? (
        <div
          className="backBtn"
          style={{
            padding: "12px 20px",
            display: "block",
          }}
        >
          {/* <Link to="/" className="backBar"f>
            <BarChartIcon />
          </Link> */}
          {location.pathname.substring(
            location.pathname.lastIndexOf("/") + 1
          ) !== "metaDataSearch" ? (
            <div>
              <h3
                // className="mb-3"
                className="HeadingSideMenu"
                style={{ direction: "rtl" }}
                id={
                  location.pathname.substring(
                    location.pathname.lastIndexOf("/") + 1
                  ) === "search" ||
                    location.pathname.substring(
                      location.pathname.lastIndexOf("/") + 1
                    ) === "generalSearch"
                    ? "h3SideSearch"
                    : ""
                }
              >
                <div>
                  <img src={menu_open} alt="" onClick={handleClick} />
                  {window.location.pathname.includes("generalSearch") && (
                    <span>البحث</span>
                  )}
                  {location.pathname.substring(
                    location.pathname.lastIndexOf("/") + 1
                  ) !== "generalSearch" &&
                    location.pathname.substring(
                      location.pathname.lastIndexOf("/") + 1
                    ) !== "search" ? (
                    t(
                      sideLinks.filter(
                        (x) =>
                          location.pathname.substring(
                            location.pathname.lastIndexOf("/") + 1
                          ) === x.to
                      )[0]?.name
                    )
                  ) : (
                    <div className="searchStepsWizard">
                      {location.pathname.substring(
                        location.pathname.lastIndexOf("/") + 1
                      ) === "generalSearch" ? (
                        ""
                      ) : (
                        <nav class="breadcrumbs">
                          <li
                            onClick={props.outerOpenResultMenu}
                            className={
                              props.outerResultMenuShown
                                ? "breadcrumbs__item breadcrumbs__itemActive first"
                                : "breadcrumbs__item first"
                            }
                          >
                            <p> {t("common:menu")}</p>
                          </li>
                          {props.outerResultDetailsShown ? (
                            <li
                              onClick={props.outerOpenResultdetails}
                              className={
                                props.outerResultDetailsShown
                                  ? "breadcrumbs__item breadcrumbs__itemActive second"
                                  : "breadcrumbs__item second"
                              }
                            >
                              <p> {t("common:results")}</p>
                            </li>
                          ) : null}
                        </nav>
                      )}
                    </div>
                  )}
                </div>
              </h3>
              {window.location.pathname.includes("generalSearch") && (
                <Steps current={activeStep} labelPlacement="vertical">
                  <Step
                    icon={
                      <SearchOutlined
                        style={{
                          background: "#0a8eb9",
                          borderRadius: "50%",
                          padding: "10px",
                          color: "#fff",
                          fontSize: "15px",
                        }}
                      />
                    }
                    title={"بحث"}
                    onClick={generalOpenSearchInputs}
                  />
                  <Step
                    icon={
                      <MenuFoldOutlined
                        style={{
                          background:
                            activeStep == 2 || activeStep == 3
                              ? "#0a8eb9"
                              : "#fff",
                          borderRadius: "50%",
                          padding: "10px",
                          color:
                            activeStep == 2 || activeStep == 3
                              ? "#fff"
                              : "#000",
                          fontSize: "15px",
                        }}
                      />
                    }
                    onClick={generalOpenResultMenu}
                    title={"القائمة"}
                  />
                  <Step
                    icon={
                      <UnorderedListOutlined
                        style={{
                          background: activeStep == 3 ? "#0a8eb9" : "#fff",
                          borderRadius: "50%",
                          padding: "10px",
                          color: activeStep == 3 ? "#fff" : "#000",
                          fontSize: "15px"
                        }} />}
                    title={"النتايج"} onClick={generalResultDetailsShown} />
                </Steps>)}
            </div>
          ) : (
            <p className="sideMenuTitle">{t("sidemenu:mainTitle")}</p>
          )
          }
          <Link
            to={
              location.pathname.substring(
                location.pathname.lastIndexOf("/") + 1
              ) === "metaDataSearch"
                ? ""
                : "/"
            }
            className=""
          >
            <IconButton
              onClick={props.handleDrawerCloseButton}
              className="closeMenuIcon openSideHelpCopy"
            >
              <RiArrowRightDoubleLine className="MdKeyboardDoubleArrowLeft" />
            </IconButton>
          </Link>
        </div>
      ) : (
        <Link
          to={
            location.pathname.substring(
              location.pathname.lastIndexOf("/") + 1
            ) === "metaDataSearch"
              ? ""
              : "/"
          }
          className=""
        >
          <IconButton
            onClick={props.handleDrawerCloseButton}
            className="closeMenuIcon openSideHelpCopy"
            style={{ display: !props.open ? "none" : "block" }}
          >
            <RiArrowRightDoubleLine className="MdKeyboardDoubleArrowLeft" />
          </IconButton>
        </Link>
      )}
      {/* <Divider /> */}
      {location.pathname === "/" ||
        location.pathname === `${process.env.PUBLIC_URL}/` ||
        location.pathname === process.env.PUBLIC_URL ||
        location.pathname.substring(location.pathname.lastIndexOf("/") + 1) ===
        "metaDataSearch" ? (
        <Fade right>
          <div className="Side-menu-mainContent">
            <List style={{ paddingTop: "0", padding: "10px" }}>
              {sideLinks.map((text, index) => (
                <Tooltip
                  title={props.open ? "" : t(text.name)}
                  placement="left"
                  key={index}
                >
                  <div className="sideLinkDiv sideLinkDivCopy">
                    <Button
                      variant="contained"
                      component="label"
                      onClick={
                        text.to === "metaDataSearch"
                          ? openSearchTable
                          : text.to === "generalSearch"
                            ? generalOpenSearchInput
                            : text.dashboardType === "incidentsDashboard"
                              ? () => {
                                window.open(
                                  `${window.location.origin}/eexplorer/incidentdashboard`,
                                  "_blank"
                                );
                              }
                              : text.dashboardType === "landsDashboard"
                                ? () => {
                                  window.open(
                                    `${window.landsKPIsDashboardURL}`,
                                    "_blank"
                                  );
                                }
                                : props.handleDrawerOpen
                      }
                      style={{
                        border: "none",
                        outline: "none",
                        boxShadow: "none",
                        backgroundColor: "transparent",
                        width: "100%",
                        height: "100%",
                        padding: "0",
                        margin: "0",
                      }}
                    >
                      {text.to === "import" ? (
                        <input type="file" hidden />
                      ) : null}
                      <Link
                        to={"/" + text.to}
                        style={{ whiteSpace: "break-spaces" }}
                      >
                        <ListItem button key={text.id} className="hashmap">
                          <div style={{ textAlign: "center" }}>
                            {/* <img
                            className={
                              text.to === "marsed" ? text.className : ""
                            }
                            src={text.icon}
                            alt="sidemenuIcon"
                            style={{
                              width: [
                                modulesIDs.incidentsModule,
                                modulesIDs.landsKPIsDashboard,
                              ].includes(text.module_id)
                                ? "30px"
                                : "20px",
                            }}
                          /> */}
                            <div style={{ textAlign: "center" }}>
                              {typeof text.icon === "function" ? (
                                <text.icon
                                  className="hashTestSideMenu"
                                  id={text.id}
                                />
                              ) : typeof text.icon === "string" ? (
                                <img
                                  src={text.icon}
                                  alt={text.name}
                                  className="hashTest"
                                  id={text.id}
                                />
                              ) : null}
                            </div>
                            {/* {text.icon ? (
                            <text.icon
                              className="hashTest"
                              id={text.id}
                            />
                          ) : null} */}
                          </div>
                          {props.open ? (
                            <ListItemText
                              className="side_ListItemText"
                              style={{
                                flex: "none",
                                marginRight: "10px",
                                color: "#284587",
                                fontSize: "16px",
                              }}
                              primary={t(text.name)}
                            />
                          ) : null}
                        </ListItem>
                      </Link>
                    </Button>
                  </div>
                </Tooltip>
              ))}
              {/* <Tooltip
              title={
                props.open ? "" : t("sidemenu:sideLinks.changeLangTooltip")
              }
              placement="left"
            >
              <div className="sideLinkDiv languageSideDiv">
                <Button
                  variant="contained"
                  component="label"
                  onClick={props.toggleCurrentLang}
                  style={{
                    border: "none",
                    outline: "none",
                    boxShadow: "none",
                    backgroundColor: "transparent",
                    width: "100%",
                    height: "100%",
                    padding: "0",
                    margin: "0",
                    cursor: "pointer",
                  }}
                >
                  <ListItem button>
                    <div
                      style={{ textAlign: "center" }}
                      className="changeLanguageAREN"
                    >
                      {i18n.language === "en" ? (
                        <img
                          className="changeLangIcon"
                          src={arabicIcon}
                          alt="sidemenuIcon"
                          style={{ width: "40px", marginRight: "10px" }}
                        />
                      ) : (
                        <p>EN</p>
                      )}
                    </div>

                    {props.open ? (
                      <ListItemText
                        primary={t("sidemenu:sideLinks.language")}
                      />
                    ) : null}
                  </ListItem>
                </Button>
              </div>
            </Tooltip> */}
            </List>
          </div>
          <div className="Side-menu-icons">
            {props.open ? (
              <p className="sideMenuFooter pt-2">{t("sidemenu:sideMsg")}</p>
            ) : null}
            <div
              className="footerIcons"
              style={{ display: !props.open ? "none" : "flex" }}
            >
              <RiGlobalLine />
              <FaRegCircleQuestion />
              <FaRegUser />
            </div>
          </div>
        </Fade>
      ) : location.pathname.substring(
        location.pathname.lastIndexOf("/") + 1
      ) === "coordinateSearch" ? (
        <CoordinatesSearch map={props.map} setActiveStep={setActiveStep} activeStep={activeStep} />
      ) : location.pathname.substring(
        location.pathname.lastIndexOf("/") + 1
      ) === "contact" ? (
        (window.open(`${window.hostURL}/home/<USER>
          window.open("/", "_self"))
      ) : location.pathname.substring(
        location.pathname.lastIndexOf("/") + 1
      ) === "generalSearch" ? (
        <GeneralSearch
          mainData={props.mainData}
          languageState={props.languageState}
          map={props.map}
          outerSearchResult={props.outerSearchResult}
          setOuterSearchResult={props.setOuterSearchResult}
          generalOpenResultdetails={generalOpenResultdetails}
          generalOpenResultMenu={generalOpenResultMenu}
          generalOpenSearchInputs={generalOpenSearchInputs}
          generalResultMenuShown={generalResultMenuShown}
          generalResultDetailsShown={generalResultDetailsShown}
          generalSearchInputsShown={generalSearchInputsShown}
          resultDetailsDataRef={resultDetailsData}
        // datailData={resultDetailsData.current.detailsData}
        // landBaseParcelData={resultDetailsData.current.landBaseParcelData}
        // setLandBaseParcelData={setLandBaseParcelData}
        />
      ) : //  ) : location.pathname.substring(
        //                 location.pathname.lastIndexOf("/") + 1
        //               ) === "layersMenu" ? (
        //               <LayersMenu
        //                 mainData={props.mainData}
        //                 languageState={props.languageState}
        //                 map={props.map}
        //               />
        location.pathname.substring(location.pathname.lastIndexOf("/") + 1) ===
          "nearestService" ? (
          <NearestServiceSearch
            map={props.map}
            mainData={props.mainData}
            setOuterSearchResult={props.setOuterSearchResult}
            outerOpenResultdetails={props.outerOpenResultdetails}
            outerOpenResultMenu={props.outerOpenResultMenu}
            outerResultMenuShown={props.outerResultMenuShown}
            outerResultDetailsShown={props.outerResultDetailsShown}
          />
        ) : location.pathname.substring(
          location.pathname.lastIndexOf("/") + 1
        ) === "measurement" ? (
          <MeasureTool map={props.map} />
        ) : location.pathname.substring(
          location.pathname.lastIndexOf("/") + 1
        ) === "geoRange" ? (
          <GeoRange />
        ) : location.pathname.substring(
          location.pathname.lastIndexOf("/") + 1
        ) === "painting" ? (
          <Painting map={props.map} />
        ) : location.pathname.substring(
          location.pathname.lastIndexOf("/") + 1
        ) === "marsed" ? (
          <MarsedComponent map={props.map} mainData={props.mainData} />
        ) : location.pathname.substring(
          location.pathname.lastIndexOf("/") + 1
        ) === "print" ? (
          <PrintComponent map={props.map} />
        ) : location.pathname.substring(
          location.pathname.lastIndexOf("/") + 1
        ) === "kml" ? (
          <KmlGoogle map={props.map} mainData={props.mainData} />
        ) : location.pathname.substring(
          location.pathname.lastIndexOf("/") + 1
        ) === "bookmark" ? (
          <BookMark map={props.map} />
        ) : location.pathname.substring(
          location.pathname.lastIndexOf("/") + 1
        ) === "setting" ? ( ////========================================================
          <Setting map={props.map} />
        ) : location.pathname.substring(
          location.pathname.lastIndexOf("/") + 1
        ) === "search" ? (
          <OuterSearchMainPage
            mainData={props.mainData}
            map={props.map}
            outerSearchResult={props.outerSearchResult}
            outerOpenResultdetails={props.outerOpenResultdetails}
            outerOpenResultMenu={props.outerOpenResultMenu}
            outerResultMenuShown={props.outerResultMenuShown}
            outerResultDetailsShown={props.outerResultDetailsShown}
            resultDetailsDataRef={resultDetailsData}
          // landBaseParcelData={resultDetailsData.current.landBaseParcelData}
          // setLandBaseParcelData={setLandBaseParcelData}
          />
        ) : location.pathname.substring(
          location.pathname.lastIndexOf("/") + 1
        ) === "import" ? (
          <ImportFile
            mainData={props.mainData}
            map={props.map}
            outerSearchResult={props.outerSearchResult}
            outerOpenResultdetails={props.outerOpenResultdetails}
            outerOpenResultMenu={props.outerOpenResultMenu}
            outerResultMenuShown={props.outerResultMenuShown}
            outerResultDetailsShown={props.outerResultDetailsShown}
            resultDetailsDataRef={resultDetailsData}
          />
        ) : location.pathname.substring(
          location.pathname.lastIndexOf("/") + 1
        ) === "updatingRequests" ? (
          <UpdatingRequests map={props.map} mainData={props.mainData} />
        ) : // <div style={{ textAlign: "center" }}>
          location.pathname.substring(location.pathname.lastIndexOf("/") + 1) ===
            "interactiveMap" ? (
            <InteravtiveMap map={props.map} mainData={props.mainData} />
          ) : // <div style={{ textAlign: "center" }}>
            //   <Upload
            //     name="fileUpload"
            //     multiple={true}
            //     onChange={setFile}
            //     accept=".kmz, .kml, .dwg"
            //     type="file"
            //     action={window.ApiUrl + "uploadMultifiles"}
            //   >
            //     <button
            //       className="SearchBtn mt-3 "
            //       size="large"
            //       htmlType="submit"
            //       block
            //     >
            //       {t("sidemenu:upload")} <UploadOutlined />
            //     </button>
            //   </Upload>
            // </div>
            null}
    </Drawer>
  );
}
