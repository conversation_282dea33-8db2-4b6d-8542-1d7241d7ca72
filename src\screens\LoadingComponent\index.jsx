
import { useEffect, useState,useContext } from "react";
import Loader from "../../containers/Loader";
import GeneralDataTable from "../../components/generalDataTable"
import { LoaderContext } from "../../Contexts/LoaderContext";

export default function LoadingComponent() {

    const [printBoxStyle, setPrintBox] = useState(null);
    const [showLoading, setShowLoading] = useState(false);
    const [showDataTable, setShowDataTable] = useState(false);
    const [showSwipLabels, setShowSwipLabels] = useState(false);
    const { isLoading} = useContext(LoaderContext);

    useEffect(() => {
        document.addEventListener('showLoading', (event) => {

            setShowLoading(event.detail);
        });
        document.addEventListener('showPrintBox', (event) => {
            setPrintBox(event.detail);
        });
        document.addEventListener('showGeneralDataTable', (event) => {
            setShowDataTable(event.detail);
        });
        document.addEventListener("showSwipLabels", (event) => {
            setShowSwipLabels(event.detail);
          });
    }, []);


    return (<div>
        {(showLoading || isLoading) &&
            <Loader />}
              {showSwipLabels && showSwipLabels.show && (
        <div>
          <div id="leftSwipeLabel" class="swipelabelStyle"></div>
          <div id="rightSwipeLabel" class="swipelabelStyle"></div>
        </div>
      )}
        {printBoxStyle && printBoxStyle.show &&
            <div class="print-box hidden-print" style={printBoxStyle.style} ></div>}
        {showDataTable && showDataTable.show && <GeneralDataTable handlePrint={showDataTable.handlePrint} data={showDataTable}/>}
    </div>)

}