import React, { useEffect, useRef, useState } from "react";
import { Container } from "react-bootstrap";
import { useTranslation } from "react-i18next";
import axios from "axios";
import { Box, Grid, Dialog, Slide } from "@mui/material";
import debounce from "lodash.debounce";
import styles from "../../sidemenu-Components/Tracking.module.css";
import {
  convertToEnglish,
  getFeatureDomainName,
  getLayerId,
  highlightFeature,
  queryTask,
} from "../../helper/common_func";
import { Form, Input } from "antd";
import InteractiveMap from "./InteractiveMap";
const host = window.ApiUrl; // Replace with actual API host

const InteravtiveMap = (props) => {
  const { t } = useTranslation("common");
  const [isLoading, setIsLoading] = useState(false);
  const [tickets, setTickets] = useState([]);
  const [totalPages, setTotalPages] = useState(0);
  const [currentPageNo, setCurrentPageNo] = useState(0);
  const [searchValue, setSearchValue] = useState("");
  const [inputTimer, setInputTimer] = useState(null);
  const containerRef = useRef(null);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState(null);
  const [features, setFeatures] = useState([]);



  return (
    <div className="MeasureTool" style={{ direction: "rtl" }}>
      {!isOpen && (
        <div className="coordinates measurePage">
          <div
            // style={{
            //   display: "flex",
            //   flexDirection: "column",
            //   justifyContent: "center",
            //   alignItems: "center",
            //   gap: "10px",
            // }}
          >
            <div
              style={{
                // display: "flex",
                // justifyContent: "space-between",
                // alignItems: "center",
                // marginBottom: "15px",
              }}
            >
        <InteractiveMap map={props.map} />
        </div>
        </div>
        </div>
      )}
     
    </div>
  );
};

export default InteravtiveMap;
