import React from "react";
import { useTranslation } from "react-i18next";
import { CloseCircleOutlined } from '@ant-design/icons'
export default function SideTable(props) {
  const { t } = useTranslation("dashboard");

  let hasArea = Object.values(props.data).length ?
    typeof Object.values(props.data)[0] === "object" : '';
  if (props.isChildChart) {
    return (
      <>
        <h5 className="dashTableTitle text-center"> ({props.title})</h5>
        <span onClick={props.closeSideTblHandler} style={{ position: 'relative', cursor: 'pointer', left: '5%', top: '-2rem' }} title={t("closeSideTbl")}>
          <CloseCircleOutlined style={{ fontSize: '1.3rem', color: '#08c' }} />
        </span>
        {props?.data?.labels?.map((label, labelIdx) => {
          return <React.Fragment key={labelIdx + label}>
            <h6 className="text-center">
              {label}
            </h6>
            <table className="table table-bordered dashboardMapTable1">
              <thead>
                <tr style={{ fontWeight: 'bolder' }}>
                  <td>{t('name')}</td>
                  <td key={label + "asd"}>{t('count')}</td>
                </tr>
              </thead>
              {(props?.data?.data || []).map((item, index) => {
  if(props?.data?.chartOriginalData&& props?.data?.chartOriginalData[labelIdx]?.data.map(i=>i.value).includes(item['name']))
                return (
                  <React.Fragment key={index + label}>

                    <tbody>
                      <tr style={!index ? { fontWeight: 'bolder' } : {}}>
                        <td>{item['name']}</td>
                        <td>{item?.data[labelIdx]}</td>
                      </tr>
                    </tbody>
                  </React.Fragment>
                )
              })}
            </table>
          </React.Fragment>
        })}
      </>
    )
  } else if (!props.isChildChart && !props?.data?.length)
    return (
      <>
        <p className="dashTableTitle"> {props.title}</p>
        <span onClick={props.closeSideTblHandler} style={{ position: 'relative', cursor: 'pointer', left: '5%', top: '-2rem' }} title={t("closeSideTbl")}>
          <CloseCircleOutlined style={{ fontSize: '1.3rem', color: '#08c' }} />
        </span>
        <table className="table table-bordered dashboardMapTable1">
          <tbody>
            {Object.entries(props.data).map((item, index) => {
              if (hasArea)
                return (
                  <tr key={index + "asd"} style={!index ? { fontWeight: 'bolder' } : {}}>
                    <td>{item[0]}</td>
                    <td>{item[1][0]}</td>
                    <td>{item[1][1]}</td>
                  </tr>
                );
              else
                return (
                  <tr key={index + "asd"} style={!index ? { fontWeight: 'bolder' } : {}}>
                    <td>{item[0]}</td>
                    <td>{item[1]}</td>
                  </tr>
                );
            })}
          </tbody>
        </table>
      </>
    );
  else return null
}
