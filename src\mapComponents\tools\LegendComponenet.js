import React, { useEffect, useState } from "react";
import Fade from "react-reveal/Fade";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTimes } from "@fortawesome/free-solid-svg-icons";
import TocComponent from "./TocComponent";

import ArcGISLegend from "@arcgis/core/widgets/Legend";
export default function LegendComponent(props) {
  useEffect(() => {
    const legend = new ArcGISLegend({
      view: props.map.view,
      style: "classic",
      container: "legendContainer",
    });
  }, [props.map]);
  return (
    <Fade left collapse>
      <div
        className="toolsMenu inquiryTool"
        style={{
          borderRadius: "16px",
          position: "absolute",
          bottom: 0,
          top: "auto",
          maxHeight: "300px",
        }}
      >
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            // position: "sticky",
            // top: 0,
            // left: 0,
            // right: 0,
            // zIndex: 9999,
            // backgroundColor: "white",
            // padding: "10px",
            // borderRadius: "10px",
          }}
        >
          <FontAwesomeIcon
            icon={faTimes}
            style={{
              marginTop: "5px",
              marginRight: "5px",
              cursor: "pointer",
              color: "rgba(40, 69, 135, 1)",
            }}
            onClick={() => {
              props.closeToolsData();
              props.Cleanup();
            }}
          />

          <div
            style={{
              fontSize: "16px",
              fontWeight: "400",
              color: "rgba(40, 69, 135, 1)",
              fontFamily: "Droid Arabic Kufi",
            }}
          >
            مفتاح الخريطة
          </div>
        </div>
        <div id="legendContainer" className="legend"></div>
      </div>
    </Fade>
  );
}
