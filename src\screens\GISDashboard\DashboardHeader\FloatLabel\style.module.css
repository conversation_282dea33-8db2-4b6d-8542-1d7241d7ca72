.floatCustomLabel {
    position: relative;
  }
  
  .customLabel {
    font-weight: normal;
    position: absolute;
    pointer-events: none;
    /* left: 12px;
    top: 11px; */
    transition: 0.2s ease all;
    
  }
  
  .asPlaceholder {
    color: black;
    float: right;
  }
  
  .asCustomLabel {
    top: -8px;
    font-size: 12px !important;
    background: white;
    color: black;
    padding: 0 5px;
    /* margin-right: -4px; */
    z-index: 2;
    right: 0;
  }
  