import React, { Component } from "react";
import { useEffect } from "react";
import { useState } from "react";
import ReactApex<PERSON>hart from "react-apexcharts";
import { useTranslation } from "react-i18next";

function PolarAreaChart(props) {
  const { t,i18n } = useTranslation("dashboard");
  const [series, setSeries] = useState([]);
  const [labels, setLabels] = useState([]);
  const [options, setOptions] = useState({});

  useEffect(() => {
    let { preparedChartData, title, shownDataTypes } = props;
if(preparedChartData?.length){
    setOptions({
      chart: {
        defaultLocale: i18n.language==='ar'?'ar':'en',
        locales: [{
          name: i18n.language==='ar'?'ar':'en',
          options: {
             toolbar: {
              exportToSVG:t('exportToSVG'),
              exportToPNG:t('exportToPNG'),
              exportToCSV:t('exportToCSV'),
              menu:t('menu'),
              selection: t('selection'),
              selectionZoom: t('selectionZoom'),
              download: t('downloadSVG'),
              zoomIn: t('zoomIn'),
              zoomOut: t('zoomOut'),
              pan: t('pan'),
              reset: t('reset'),
            }
          }
        }],
        width: "100%",
        type: "polarArea",
        toolbar: {
          show: false,
        },
        zoom: {
          enabled: true,
        },

        stroke: {
          colors: ["#fff"],
        },
        fill: {
          opacity: 0.8,
        },
      },
      // title: {
      //   text: title,
      //   align: 'center',
      //   style: {
      //       fontSize:  '14',
      //       fontWeight:  'bold',
      //       fontFamily:  "NeoSansArabic",

      //     },
      // },
      legend: { show: false },
      labels: preparedChartData.map((i) => i.label),
      responsive: [
        {
          breakpoint: 150,
          options: {
            chart: {
              width: 150,
            },
            legend: {
              show: false,
              // position: 'bottom'
            },
          },
        },
      ],
    });
    setSeries(preparedChartData.map((i) => i.count));
    setLabels(preparedChartData.map((i) => i.label));
  }else{
    setSeries([]);
     setOptions({});
     setLabels([]);
  }
    return () => {
      setSeries();
      setOptions();
      setLabels();
    };
  }, [props.preparedChartData]);
  useEffect(()=>{
    if(props.sideTblTitle===props.title && series){
      props.onClickTitle({
        data: series,
        title: props.title,
        labels,
      })
    }
    
  },[series]);

  return (
    <div className="ape-chart">
      <div className="col text-center">

      <h6
        title={t("ClickHereForDetails")}
        onClick={() =>
          {if((options && series) && (Object.keys(options).length &&series.length)) props.onClickTitle({ data: series, title: props.title, labels })
              //todo: show a toast there is no data to show in else
        
        }
        }
        style={{
          fontFamily: "NeoSansArabic",
          textAlign: "center",
          fontWeight: "bold",
          cursor: "pointer",
        }}
      >
        {props.title}
      </h6>
      {/* <img src={props.mapLogoSrc} className="map-pointer" alt="map logo" onClick={handleHeatMap} /> */}
          </div>

          {(options && series) && (!Object.keys(options).length || !series.length) ?
      <h4 className="text-center"> لا يوجد بيانات للعرض</h4>
      :(options && series) && (Object.keys(options).length &&series.length)?  <ReactApexChart
        options={props.options || options}
        series={props.series || series}
        type="polarArea"
        // width={props.width||250}
        // height={props.height||250}
      />:null}
    </div>
  );
}

export default PolarAreaChart;
