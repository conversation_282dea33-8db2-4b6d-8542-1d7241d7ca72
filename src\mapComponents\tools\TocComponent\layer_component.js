import { faCaretSquareLeft } from "@fortawesome/free-regular-svg-icons";
import {
  faCaretDown,
  faCaretLeft,
  faCaretRight,
  faCaretSquareDown,
  faCartPlus,
  faPlus,
  faPlusSquare,
  faSearchPlus,
} from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import React from "react";
import i18n from "../../../i18n";
// import { layersSetting } from "../../../helper/layers";
import { useTranslation } from "react-i18next";

import cursorGreen from "../../../assets/icons/cursorGreen.svg";
import arrow_drop_down from "../../../assets/icons/arrow_drop_down.svg";
import arrow_drop_up from "../../../assets/icons/arrow_drop_up.svg";

export const LayerComponent = ({
  layer,
  zoomToLayer,
  expand,
  changeLayer,
  mainData,
  languageState,
}) => {
  const { t } = useTranslation("map", "layers");

  return (
    <section className={` ${layer.disable}`}>
      <div className="toc-gallery">
        <div className="toc-gallery-content">
          <div
            onClick={expand}
            style={{ cursor: "pointer", display: "flex", alignItems: "center" }}
          >
            {layer.show ? (
              <img src={arrow_drop_up} alt="" />
            ) : (
              <img src={arrow_drop_down} alt="" />
            )}
          </div>
          <input
            type="checkbox"
            // style={{ marginTop: "-10px" }}
            checked={layer.visible}
            onChange={changeLayer}
            className="toc-gallery-content_checkbox"
          />
          <label
            style={{
              fontSize: "13px",
              fontWeight: "normal",
            }}
          >
            {languageState === "ar" && mainData.layers[layer.layerName]
              ? mainData.layers[layer.layerName].arabicName
              : layer.layerName}
          </label>
        </div>
        <div style={{ cursor: "pointer" }} onClick={zoomToLayer}>
          <img src={cursorGreen} alt="" />
        </div>
      </div>
      {layer.show &&
        layer.legend.map((legend, key) => {
          return (
            <ul
              key={key}
              style={{
                display: "flex",
                alignItems: "center",
                paddingRight: "30px",
                marginTop: "8px",
                marginBottom: "5px",
              }}
            >
              <img src={"data:image/jpeg;base64," + legend.imageData} />
              <div
                style={{
                  fontSize: "13px",
                  marginRight:"5px"
                }}
              >
                {legend.label}
              </div>
            </ul>
          );
        })}
    </section>
  );
};
