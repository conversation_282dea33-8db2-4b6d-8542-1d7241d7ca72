import * as React from "react";
import { styled } from "@mui/joy/styles";
import Input from "@mui/joy/Input";
import { useTranslation } from "react-i18next";

// Simplified Input styling for mobile compatibility
const StyledInput = styled("input")(({ error }) => ({
  border: "none",
  borderRadius: "6px",
  minWidth: 0,
  outline: 0,
  padding: 0,
  paddingLeft: "14px",
  paddingRight: "14px",
  paddingTop: "23px",
  paddingBottom: "4px",
  flex: 1,
  color: error ? "red" : "inherit",
  // textAlign: "right",

  backgroundColor: "transparent",
  fontFamily: "inherit",
  fontSize: "inherit",
  fontStyle: "inherit",
  fontWeight: "inherit",
  lineHeight: "inherit",
  appearance: "none", // Remove number spinner arrows
  "&::placeholder": {
    color: "#A8A8A8", // Fallback color for placeholder
  },
  "&:focus ~ label, &:not(:placeholder-shown) ~ label, &:-webkit-autofill ~ label":
    {
      top: "0.5rem",
      fontSize: "0.75rem",
    },
  "&:focus ~ label": {
    color: error ? "red" : "#A8A8A8",
  },
}));

const StyledLabel = styled("label")({
  position: "absolute",
  lineHeight: 1,
  top: "calc(50% - 8px)",
  color: "#A8A8A8",
  fontWeight: 700,
  fontSize: "12px",
  transition: "all 250ms ease-out", // Faster, simpler transition
  textAlign: "right",
  padding: "0px 15px",
  // position: "absolute",
  // lineHeight: 1,
  // top: "calc((var(--Input-minHeight) - .5em) / 2)",
  // color: "#A8A8A8",
  // fontWeight: 700,
  // fontSize: "12px",
  // transition: "all 350ms cubic-bezier(0.4, 0, 0.2, 1)",
});

const InnerInput = (props) => {
  const {
    label,
    placeholder,
    value,
    onChange,
    error,
    maxLength,
    type,
    ...other
  } = props;
  const id = props.name || "" //React.useId();

  return (
    <>
      <StyledInput
        style={{
          background: "#fff",
          border: "1px solid #999",
          // padding: "12px",
          // paddingTop: "22px",
          borderRadius: "16px",
          boxShadow: "none",
          minHeight: "50px"
        }}
        {...other}
        id={id}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        error={error}
        maxLength={maxLength} // Set the maxLength here for controlling digits
        type={type === "number" || type === "float" ? "text" : type} // Render as text for numbers and float to remove spinner
        inputMode={type === "number" || type === "float" ? "decimal" : "text"} // Use decimal input mode for floats
        pattern={
          type === "number" || type === "float"
            ? "[0-9]*([,.][0-9]*)?"
            : undefined
        } // Allow numbers and one decimal point if type is number or float
      />
      <StyledLabel htmlFor={id}>{label}</StyledLabel>
    </>
  );
};

export default function FloatingLabelInput({
  label = "Label",
  placeholder = "Enter text...",
  onInputChange,
  error = false,
  helperText = "",
  setSearchValue,
  loginUI,
  value,
  type = "text", // Default type is text
  maxDigits = 50, // New prop to control max digits for numbers
  ...rest
}) {
  const [inputValue, setInputValue] = React.useState(value || "");
  const { t, i18n } = useTranslation(["common"]);

  const handleChange = (event) => {
    const newValue = event.target.value;
    

    // Handle input for number, float, and text
    if (type === "number" || type === "float") {
      // Allow digits and a single decimal point for float
      if (/^\d*\.?\d*$/.test(newValue) && newValue.length <= maxDigits) {
        if (setSearchValue && typeof setSearchValue === "function") {
          setSearchValue(newValue); // Use setSearchValue if it's a function
        } else {
          setInputValue(newValue); // Fallback to setInputValue
        }

        if (onInputChange) {
          onInputChange(newValue); // Call the parent's function with the new value
        }
      }
    } else {
      // For text input, update directly
      if (setSearchValue && typeof setSearchValue === "function") {
        setSearchValue(newValue); // Use setSearchValue if it's a function
      } else {
        setInputValue(newValue); // Fallback to setInputValue
      }

      if (onInputChange) {
        onInputChange(newValue);
      }
    }
  };

  return (
    <>
      <div className="input-wrapper">
        <Input
          slots={{ input: InnerInput }}
          slotProps={{
            input: {
              label,
              placeholder,
              value: value,
              onChange: handleChange,
              type,
              error,
              maxLength: maxDigits, // Set maxLength for InnerInput
              ...rest,
            },
          }}
          // sx={{
          //   "--Input-minHeight": "56px",
          //   "--Input-radius": "6px",
          //   // "--Input-boxShadow": "none", // Remove box shadow
          // }}
          sx={{
            padding: "0",
            borderRadius: "16px",
            boxShadow: "none !important",
            outline: "none",
            border: "none",
            "::before": {
              display: "none",
              content: "none",
            },
            textAlign: i18n.language === "en" ? "left" : "right",
          }}
        />
      </div>
      {helperText && (
        <div
          style={{
            color: "red",
            fontSize: "12px",
            textAlign: "center",
            marginTop: `${loginUI ? "0px" : "-9px"}`,
          }}
        >
          {helperText}
        </div>
      )}
    </>
  );
}
