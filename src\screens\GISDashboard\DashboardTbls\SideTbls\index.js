import React, { useEffect, useState, useMemo } from "react";

import { useTranslation } from "react-i18next";
import { Spin } from "antd";
import isDeepEqual from 'fast-deep-equal/react'
import SideTable from "./SideTable";

function SideTbls(props) {
  const { t } = useTranslation("dashboard");
  const [data, setData] = useState({});
  const dateDataRef = React.useRef();
  const titleRef=React.useRef();
  if (!isDeepEqual(dateDataRef.current, props.data.data)) {
    dateDataRef.current = props.data.data
  };

useEffect(()=>{
  if(props.data.title) titleRef.current = props.data.title;
  else if(titleRef.current) titleRef.current = null;
},[props.data.title])
  useEffect(() => {
    let reqData = {};
    if(props?.data?.isChildChart){
      setData(props.data)   //note this
    }
    else{
    if (props.data.title) {
      let countInK = props.data.labels.find(i=>i.countInK);
      let notDirectSeries = Object.keys(props.data.data.find(i=>i)).find(i=>i==='name');
      if (props.data.hasArea) {
        reqData[t('name')]=[
            countInK?t('count') +" * 1000":t('count'),
            t('areaKm2')
        ]
        props.data.labels.map(i=>i.value).forEach((elem, index) => {
          reqData[elem] = [
            props.data?.data[0]?.data[index],
            props.data?.data[1]?.data[index],
          ];
        });
      }else {
        reqData[t('name')]=countInK?t('count') +"*1000":t('count');
        let lbels=[]; let dataSeries = [];
        if(notDirectSeries) {
          dataSeries = props.data.data.map(i=>i.data).flat();
          lbels= props.data.labels.map(i=>i.value);
        }
        else {
          dataSeries = props.data.data;
          lbels =  props.data.labels;
        }
        lbels.forEach((elem, index) => {
          reqData[elem] = dataSeries[index];
        });
    }
    setData(reqData);
    } 
  }
  }, [dateDataRef.current, props.data.title]);

//   if (Object.keys(data).length) return <Loader />;
//   else
    return (
      <div className="tbl-beside-map">
        <div className="dashTable2 mt-2">
          {/**Details tbl */}
         
         { titleRef.current === props.data.title? <SideTable 
         closeSideTblHandler={props.closeSideTblHandler}
          title={props.data.title} data={data} 
          isChildChart={props?.data?.isChildChart} />:
         <div style={{width:'100%', height:'5rem', display:'flex', justifyContent:'center'}}>
         <Spin /> 
         </div>
         
         }
        </div>
      </div>
    );
}

export default SideTbls;
