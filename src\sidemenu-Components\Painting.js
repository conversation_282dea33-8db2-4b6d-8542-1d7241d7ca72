import React, { useEffect, useRef, useState } from "react";
import { Container } from "react-bootstrap";
import { Form, Input, message } from "antd";
import { Tooltip } from "@mui/material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faArrowsAltH,
  faBan,
  faChartLine,
  faCircle,
  faEraser,
  faInfo,
  faSquare,
  faTextWidth,
} from "@fortawesome/free-solid-svg-icons";

import Sketch from "@arcgis/core/widgets/Sketch";
import { drawText } from "../helper/common_func";
import { useTranslation } from "react-i18next";

import { MdTextFields } from "react-icons/md";
import { FaExclamation } from "react-icons/fa6";
import { FaArrowsAltH } from "react-icons/fa";
import { LuPenLine } from "react-icons/lu";
import { MdCropSquare } from "react-icons/md";
import { FaRegCircle } from "react-icons/fa6";
import { BsPentagon } from "react-icons/bs";
import { LuHexagon } from "react-icons/lu";
import { RiDeleteBin6Line } from "react-icons/ri";

export default function Painting(props) {
  const { t } = useTranslation("common");

  const componentRef = useRef({});
  const { current: sketch } = componentRef;
  const activeSketchName = useRef({});

  const [formValues, setFormValues] = useState({
    text: "",
  });

  useEffect(() => {
    sketch.current = new Sketch({
      layer: props.map.findLayerById("drawingGraphicLayer"),
      view: props.map.view,
    });

    sketch.current.on("create", (event) => {
      if (
        event.state === "complete" &&
        activeSketchName.current == "pointText"
      ) {
        props.map.findLayerById("drawingGraphicLayer").remove(event.graphic);

        if (
          typeof window.streetName === "string" ||
          window.streetName instanceof String
        ) {
          drawText(
            event.graphic,
            window.streetName,
            props.map,
            "drawingGraphicLayer"
          );
        } else {
          message.warning(t("enterText"));
        }
      }
    });

    return () => {
      // componentwillunmount in functional component.
      cancelDraw();
    };
  }, []);

  const handleChangeInput = (e) => {
    setFormValues({ ...formValues, [e.target.name]: e.target.value });

    if (e.target.name == "streetName") {
      window.streetName = e.target.value;
    }
  };

  const drawPolygon = () => {
    activeSketchName.current = "polygon";
    sketch.current.create("polygon");
  };

  const drawPolygonFreeHand = () => {
    activeSketchName.current = "polygon";
    sketch.current.create("polygon", { mode: "freehand" });
  };

  const drawPolyLineFreeHand = () => {
    activeSketchName.current = "polyline";
    sketch.current.create("polyline", { mode: "freehand" });
  };

  const drawPolyLine = () => {
    activeSketchName.current = "polyline";
    sketch.current.create("polyline");
  };

  const drawRectangle = () => {
    activeSketchName.current = "rectangle";
    sketch.current.create("rectangle");
  };

  const drawCircle = () => {
    activeSketchName.current = "circle";
    sketch.current.create("circle");
  };

  const drawPoint = () => {
    activeSketchName.current = "point";
    sketch.current.create("point");
  };

  const cancelDraw = () => {
    sketch.current.cancel();
  };

  const deleteFeature = () => {
    sketch.current.cancel();
  };

  const drawTextHandle = () => {
    sketch.current.create("point");

    activeSketchName.current = "pointText";
  };

  return (
    <div className="coordinates painting">
      <Container>
        <ul>
          <Tooltip placement="top" title={t("clickWordText")}>
            <div className="mb-3">
              <li onClick={drawTextHandle} className="m-0">
                {/* <FontAwesomeIcon icon={faTextWidth} className="ml-2" /> */}
                <MdTextFields className="paintingIcons" />

                <span className="paintingText">{t("text")}</span>
              </li>
              <Form.Item name="streetName" className="">
                <Input
                  name="streetName"
                  onChange={handleChangeInput}
                  value={formValues.streetName}
                  placeholder={t("enterText2")}
                  className="paintingTextInput"
                />
              </Form.Item>
            </div>
          </Tooltip>
          <Tooltip placement="top" title={t("clickMapDrawPoint")}>
            <li onClick={drawPoint}>
              {/* <FontAwesomeIcon icon={faInfo} className="ml-2" /> */}
              <FaExclamation className="paintingIcons" style={{fontSize:"27px"}}/>

              <span className="paintingText">{t("point")}</span>
            </li>
          </Tooltip>

          <Tooltip placement="top" title={t("clickMapSolidLines")}>
            <li onClick={drawPolyLine}>
              {/* <FontAwesomeIcon icon={faArrowsAltH} className="ml-2" /> */}
              <FaArrowsAltH className="paintingIcons"/>

              <span className="paintingText">{t("solidLines")}</span>
            </li>
          </Tooltip>
          <Tooltip placement="top" title={t("clickDrawPoly")}>
            <li onClick={drawPolyLineFreeHand}>
              {/* <FontAwesomeIcon icon={faChartLine} className="ml-2" /> */}
              <LuPenLine className="paintingIcons"/>
              <span className="paintingText">{t("disConnectLines")}</span>
            </li>
          </Tooltip>

          <Tooltip placement="top" title={t("clickMapRec")}>
            <li onClick={drawRectangle}>
              {/* <FontAwesomeIcon icon={faSquare} className="ml-2" /> */}
              <MdCropSquare className="paintingIcons"/>
              <span className="paintingText">{t("rectangle")}</span>
            </li>
          </Tooltip>
          <Tooltip placement="top" title={t("clickMapCirc")}>
            <li onClick={drawCircle}>
              {/* <FontAwesomeIcon icon={faCircle} className="ml-2" /> */}
              <FaRegCircle className="paintingIcons"/>
              <span className="paintingText">{t("circle")}</span>
            </li>
          </Tooltip>

          <Tooltip placement="top" title={t("clickDrawPoly")}>
            <li onClick={drawPolygon}>
              {/* <FontAwesomeIcon icon={faSquare} className="ml-2" /> */}
              <BsPentagon className="paintingIcons"/>

              <span className="paintingText">{t("polygon")}</span>
            </li>
          </Tooltip>
          <Tooltip placement="top" title={t("clickMapDrag")}>
            <li onClick={drawPolygonFreeHand}>
              {/* <FontAwesomeIcon icon={faSquare} className="ml-2" /> */}
              <LuHexagon className="paintingIcons"/>
              <span className="paintingText">{t("freePolyg")}</span>
            </li>
          </Tooltip>
          <Tooltip
            placement="top"
            title={t("clickDrawToDelete")}
            // {t("enteronElementToDelete")}
          >
            <li onClick={deleteFeature}>
              {/* <FontAwesomeIcon icon={faEraser} className="ml-2" /> */}
              <RiDeleteBin6Line className="paintingIcons"/>
              <span className="paintingText">{t("deletePaintElemnt")}</span>
            </li>
          </Tooltip>
          <Tooltip placement="top" title={t("clickToStopPaint")}>
            <li onClick={cancelDraw}>
              <FontAwesomeIcon icon={faBan} className="ml-2" />
              <span className="paintingText">{t("stopPaint")}</span>
            </li>
          </Tooltip>
        </ul>
      </Container>
    </div>
  );
}
