import React, { useEffect, useState, useRef } from "react";
import ReactApex<PERSON><PERSON> from "react-apexcharts";
import { useTranslation } from "react-i18next";
import moment from "moment-hijri";
import isDeepEqual from 'fast-deep-equal/react'
//utils
import { getMinMax } from "../../../../helper/utilsFunc.js";
import Loader from "../../../../containers/Loader.js";
function CustomLineChartComp({title, chartData}) {
  const { t, i18n } = useTranslation("dashboard");
  const [series, setSeries] = useState([]);
  const [options, setOptions] = useState({});
  const chartDataRef = useRef(chartData);
  if (!isDeepEqual(chartDataRef.current, chartData.data)) {
    chartDataRef.current = chartData.data
  }
  useEffect(() => {
    
    if( chartData?.data){
    let { data, type,timePeriod, } =  chartData;
      
      let series = data?.length? data.map((item) => item["COUNT"]):[0];
      let labels = data?.length? data.map((item,index) => {
        //logic to conveert from Milady to Hijri
        let startDate = timePeriod && timePeriod[0];
        let endDate = timePeriod?(timePeriod?.length)>1?timePeriod[1]:timePeriod[0]:'';
        let startDayDate= ['monthly','daily'].includes(type)?startDate.split("-")[2]:'';
        let endDayDate=['monthly','daily'].includes(type)? endDate.split("-")[2]:'';
        switch (type) {
          case 'yearly':
            return moment(String(item["EXPR1"]),"YYYY").format("YYYY")
          case 'monthly':
            if(chartData?.default) return moment(String(item["EXPR1"]),"YYYY").format("YYYY");
            if(index===0)
            return moment(`${item["EXPR2"]}/${item["EXPR1"]}/${startDayDate}`,"YYYY/M/D").format("YYYY/MM")
            else if(index===(data.length-1))
            return moment(`${item["EXPR2"]}/${item["EXPR1"]}/${endDayDate}`,"YYYY/M/D").format("YYYY/MM")
            else
            return moment(`${item["EXPR2"]}/${item["EXPR1"]}/01`,"YYYY/M/D").format("YYYY/MM")
          default:
            if(chartData?.default) return moment(String(item["EXPR1"]),"YYYY").format("YYYY");
            return moment(`${item["EXPR3"]}/${item["EXPR2"]}/${item["EXPR1"]}`,"YYYY/M/D").format("YYYY/MM/DD")
            
        }
      }):[t("notFound")];
      setSeries([
        {
          name:(chartData?.default||
            type === "yearly")
              ? t("countPerYear")
              : type === "monthly"
              ? t("countPerMonth")
              : t("countPerDay"),
          data: series,
          type: "column",
        },
        {
          name:
          (chartData?.default||
            type === "yearly")
              ? t("countPerYear")
              : type === "monthly"
              ? t("countPerMonth")
              : t("countPerDay"),
          data: series,
          type: "line",
        },
      ]);
      setOptions({
        chart: {
          defaultLocale: i18n.language==='ar'?'ar':'en',
          locales: [{
            name: i18n.language==='ar'?'ar':'en',
            options: {
               toolbar: {
                exportToSVG:t('exportToSVG'),
                exportToPNG:t('exportToPNG'),
                exportToCSV:t('exportToCSV'),
                menu:t('menu'),
                selection: t('selection'),
                selectionZoom: t('selectionZoom'),
                download: t('downloadSVG'),
                zoomIn: t('zoomIn'),
                zoomOut: t('zoomOut'),
                pan: t('pan'),
                reset: t('reset'),
              }
            }
          }],
          width: "100%",
      
    //       sparkline: {
    //   enabled: true
    // },
          height: labels.length>5?'320':"100%",
          selection:{
            enabled:false
          },
          type: "line",
          dropShadow: {
            enabled: true,
            color: "#000",
            top: 18,
            left: 7,
            blur: 10,
            opacity: 0.2,
          },
          toolbar: {
            show: true,
            tools:{
              reset:true,
              download: true,
              pan: false,
              selection: false,
              zoom: false,
              zoomin: false,
              zoomout: false,
            },
            export: {
              csv: {
                filename: title,
                columnDelimiter: ',',
                headerCategory:  (chartData?.default||
                  type === "yearly")
                ? t("year")
                : type === "monthly"
                ? t("month")
                : t("day"),
                headerValue: 'value',
                dateFormatter(timestamp) {
                  return new Date(timestamp).toDateString()
                }
              },
              svg: {
                filename: title,
              },
              png: {
                filename: title,
              }
            },
          },
          colors: ["#77B6EA", "#545454"],
          zoom: {
            enabled: true,
          },
        
        },
        stroke: {
          width: [0, 4],
        },
        //   stroke: {
        //     curve: 'smooth'
        //   },

        title: {
          // text:  title,
          align: "center",
          style: {
            fontSize: "18px",
            fontWeight: "bold",
            fontFamily: "NeoSansArabic",
          },
        },
        grid: {
          borderColor: "#e7e7e7",
          row: {
            colors: ["rgb(201, 247, 245)", "transparent"], // takes an array which will be repeated on columns
            opacity: 0.5,
          },
        },
        markers: {
          size: 1,
        },
        xaxis: {
          categories: labels,
          title: {
            text:
            (chartData?.default||
              type === "yearly")
                ? t("year")
                : type === "monthly"
                ? t("month")
                : t("day"),
          },
          labels: {
            rotate: -45,
            show: true,
            trim: true,
            align: 'center',
            minWidth: 0,
            maxWidth: 12,
            style: {
                colors: [],
                fontSize: '10px',
                fontFamily: 'Helvetica, Arial, sans-serif',
                fontWeight: 200,
                cssClass: 'apexcharts-yaxis-label',
                transform: 'rotate(270deg)'
            },
            // offsetX: 0,
            offsetY: 20, 
            rotateAlways: true,
            formatter: (value) => { return value },
        },
        },
        yaxis: [{
          title: {
            text: t("count"),
          },
          labels: {
            rotate: 0,
            show: true,
            offsetX: -15,
            formatter: (val) => { 
            if( chartData.type==='daily') return val;
            else {
              let maxVal = getMinMax(series).max;
              return maxVal<1000?val :val/1000 +"K"
            } 
            },
        },
        },{
               show: false,
        }],
        legend: {
          show: false,
        },
        dataLabels: {
          enabled: true,
          enabledOnSeries: [1],
        },
        tooltip: {
          enabled: true,
          enabledOnSeries: [0],
          y: {
            formatter: function(value, { series, seriesIndex, dataPointIndex, w }) {
              return value
            }
          }
        }
      });
    }
  }, [ chartDataRef.current]);
useEffect(()=>{
  return ()=>{
    setSeries();
    setOptions()
}
},[])

 return (
    <div
      className="Columnchart1"
      style={{
        maxWidth: "37vh",
        margin: "auto",
      }}
    >
    {chartData?.data ?
         (options && series) && (!Object.keys(options).length || !series.length)?
      <h4 className="text-center"> لا يوجد بيانات للعرض</h4>
      :(options && series) && (Object.keys(options).length &&series.length)?<ReactApexChart
        options={options}
        series={series}
        type="line"
        // height={200}
      /> : null
    :<Loader /> }
    </div>
  );
}

export default CustomLineChartComp;
