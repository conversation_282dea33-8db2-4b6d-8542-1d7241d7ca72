import React from "react";
import { useState } from "react";
import { useEffect } from "react";
import isDeepEqual from 'fast-deep-equal/react'
import { Spin } from 'antd';

import { useTranslation } from "react-i18next";

import { getLayerId } from "../../../../helper/common_func";
import { getFeaturesCountOnly,  getTimePeriod } from "../../helpers/helperFunc";
import { getDateInFormatYYYYMMDD } from "../../../../helper/utilsFunc";
function TextCountSection({
  chartObj,
  selectedLayer,
  title,
  map,
  queryData,
  dependentLayer
}) {
  const { t } = useTranslation("dashboard");
const dateDataRef = React.useRef();
  const [totalCount, setTotalCount] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  if (!isDeepEqual(dateDataRef.current, queryData?.selectedTimeContext?.dateData)) {
    dateDataRef.current = queryData?.selectedTimeContext?.dateData
  }
  useEffect(() => {
    let layerID = getLayerId(map.__mapInfo, dependentLayer);
    // let statisticsObj = chartObj;
    // let fieldName = chartObj.name;
    let timeContextType = queryData.selectedTimeContext.type;
    let whereClause = '';
    if (queryData?.selectedBoundaryType?.value && queryData?.selectedBoundaryType?.selectedBoundary) {
      whereClause += `${queryData?.selectedBoundaryType?.value} = ${queryData?.selectedBoundaryType?.selectedBoundary}`;
    }
    if (queryData.selectedTimeContext.dateData?.length) {
      let timePeriod = getTimePeriod(queryData);
      let extractedPeriod =
        timeContextType === "yearly"
          ? "YEAR"
          : timeContextType === "monthly"
            ? "MONTH"
            : "DAY";
      let extractedStatment =
        timeContextType === "yearly"
          ? `EXTRACT (${extractedPeriod} FROM ${chartObj?.filterDateField||"CREATED_DATE"})`
          : timeContextType === "monthly"
            ? `EXTRACT (${extractedPeriod} FROM ${chartObj?.filterDateField||"CREATED_DATE"}), EXTRACT (YEAR FROM ${chartObj?.filterDateField||"CREATED_DATE"})`
            : `EXTRACT (${extractedPeriod} FROM ${chartObj?.filterDateField||"CREATED_DATE"}),EXTRACT (MONTH FROM ${chartObj?.filterDateField||"CREATED_DATE"}), EXTRACT (YEAR FROM ${chartObj?.filterDateField||"CREATED_DATE"})`;
      let whereClauseDueToTimeContext =
        timeContextType === "yearly"
          ? `${extractedStatment} IN (${timePeriod.join(",")})`
          : timePeriod.length > 1 && ["monthly", "daily"].includes(timeContextType)
            ? "" +
            `${chartObj?.filterDateField||"CREATED_DATE"}` +
            " BETWEEN DATE '" +
            timePeriod.join("' AND DATE '") +
            "'"
            : `${chartObj?.filterDateField||"CREATED_DATE"} BETWEEN TIMESTAMP '${timePeriod.join(" ")} 00:00:00' AND TIMESTAMP '${timePeriod.join(" ")} 23:59:59'`;
            whereClause = whereClause? whereClause + " AND " +whereClauseDueToTimeContext: whereClauseDueToTimeContext;
    }
    if(chartObj?.restrictionDateWhereClause) whereClause=whereClause? whereClause+` AND ${chartObj?.restrictionDateWhereClause} '${getDateInFormatYYYYMMDD(new Date())}'`:
    `${chartObj?.restrictionDateWhereClause} '${getDateInFormatYYYYMMDD(new Date())}'`;
    if(chartObj?.restrictionWhereClause) whereClause=whereClause? whereClause+` AND ${chartObj?.restrictionWhereClause} `:
    `${chartObj?.restrictionWhereClause} `;
    
    console.log({whereClause, chartObj});
    setIsLoading(true)
    getFeaturesCountOnly(
      layerID,
      (data) => {
        
        console.log(data);
        setTotalCount(data ||0);
        setIsLoading(false);
        
      },
      (err) => {
        console.log(err);
        setIsLoading(false);
      }, whereClause    //where clause
    );
  }, [selectedLayer,dateDataRef.current,
    queryData?.selectedBoundaryType?.selectedBoundary,
  ]);
  if (isLoading) return <Spin />;

  else return<> <h2> 
  {title}
  </h2>
    <h4 className="text-center">
    <strong>{totalCount} </strong>
  </h4>

  </>
  ;

}

export default TextCountSection;
