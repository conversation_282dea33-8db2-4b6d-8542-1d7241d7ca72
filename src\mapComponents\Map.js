import React, { Component } from 'react';
import {  getMapInfo, showLoading } from '../helper/common_func';
import GraphicsLayer from "@arcgis/core/layers/GraphicsLayer";
import Home from "@arcgis/core/widgets/Home";
import Zoom from "@arcgis/core/widgets/Zoom";
import MapView from "@arcgis/core/views/MapView";
import Map from "@arcgis/core/Map";
import MapImageLayer from "@arcgis/core/layers/MapImageLayer"
// import FeatureLayer from "@arcgis/core/layers/FeatureLayer"
import * as intl from "@arcgis/core/intl";
// import identifyIcon from '../../src/assets/images/identify.gif'
import SceneView from "@arcgis/core/views/SceneView";
import IdentityManager from "@arcgis/core/identity/IdentityManager";

// import * as urlUtils from "@arcgis/core/core/urlUtils";
import { notificationMessage } from "../helper/utilsFunc";
import { withTranslation } from 'react-i18next';
import {withRouter} from '../HOC/WithRouter'
import Extent from '@arcgis/core/geometry/Extent';
import Setting from '../sidemenu-Components/Setting';

class MapComponent extends Component {


    constructor(props) {
        super(props);
        this.state = {
            map:undefined,
            mapService:undefined,
            incidentMapService:undefined
        };
    }

    componentDidMount() {

        let mapServiceUrl = window.mapUrl;
        //1- get map info
        //2- query task
        //3- projection
        //4- get domain
        //5- highlight and zoom
        //6- add layers
        //7- map events
        //8- identify


        const graphicLayersIds = ["ZoomGraphicLayer", "SelectGraphicLayer",
            "printGraphicLayer", "identifyGraphicLayer", "ThematicGraphicLayer", "MarsedNoGraphicLayer", "locationGraphicLayer", "drawingGraphicLayer",
            "highlightGraphicLayer","InteractiveMapGraphicLayer"];

        let map;

        let view;

        //esri proxy
        /*urlUtils.addProxyRule({
            proxyUrl: "https://maps.mrda.gov.sa/Proxy/proxy.ashx",
            urlPrefix: "webmap.mrda.gov.sa:6443"
        });*/
        window.__intl__ = intl;
        intl.setLocale("ar");
        if (this.props.type == "3d") {

            mapServiceUrl = window.dashboardMapUrl;

            const params = new URLSearchParams(window.location.search);

            map = new Map({
                basemap: params.get('maptype') == "dark" ? "streets-night-vector" :
                    "streets-navigation-vector"
            });



            view = new SceneView({
                camera: window.initialCameraPosition,
                container: "mapDiv",
                map: map,
                popup: {
                    dockOptions: {
                        buttonEnabled: false
                    }
                },
                extent: window.fullExtent,
            });
            // creates a new instance of the NavigationToggle widget


            map.view = view;
            window.__view = view;

            view.on('pointer-move', (evt) => {
                view.hitTest(evt).then((response) => {
                    if (response.results.length) {

                        var graphic = response.results.filter((result) => {
                            // check if the graphic belongs to the layer of interest 
                            return result.graphic.layer.id.indexOf("barLayer_") > -1;
                        });

                        if (graphic.length) {
                            graphic = graphic[0].graphic;
                            view.popup.open({
                                location: graphic.geometry.centroid,
                                features: [graphic]
                            });
                        }
                        else { view.popup.close(); }

                    } else {
                        view.popup.close();
                    }
                })
            });

        }
        else {
            map = new Map({
                basemap: "satellite"
            });
            view = new MapView({
                container: "mapDiv",
                map: map,
                constraints: {
                    minZoom: 2,
                    snapToZoom: false,
                    rotationEnabled: true,
                },
                ui: {
                    components: ["attribution"]
                },
                extent: new Extent(this.props.isDashboard?{
                    xmin:4785834.7959298305,
                    ymin:2933732.235282638,
                    xmax: 5944008.648506764,
                    ymax: 3353830.1427379455,
                    spatialReference: {
                      wkid: 102100,
                      latestWkid: 3857, 
                    },
                }:window.fullExtent),
                /*padding: {
                    right: mapPadding // Same value as the #sidebar width in CSS
                  }*/
            });


            map.view = view;
        }
 
          

        window.__map = map;
        if(this.props.isDashboard){

            let homeBtn = new Home({
                view: view
            });
            let zoom = new Zoom({
                view: view
              });
              view.ui.add(zoom, "top-left");
              view.ui.add(homeBtn, "top-left");

        }
        //hide esri logo
        view.ui._removeComponents(["attribution"]);


        map.view = view;

        view.when(() => {
            let layersSetting = this.props.mainData.layers;
            getMapInfo(mapServiceUrl, layersSetting).then((response) => {

                map.__mapInfo = response;

                //showLoading(true);
                if (this.props.mapload) {
                    this.props.mapload(map);
                    var dynamicMapServiceLayer = new MapImageLayer({
                        url: this.props.mapUrl || mapServiceUrl,
                        id: 'baseMap',
                    });
                    if (!this.props.mainData.logged && !this.props.mainData.user)
                        dynamicMapServiceLayer.on('layerview-create', (e) => {
                            console.log("layerview-create event", { e });
                            dynamicMapServiceLayer.allSublayers.items.forEach(item => {
                                console.log({id:item.id, title:item.title, visible:item.visible});
                                let layer = this.props.mainData.layers[item.title];
                                if (layer && !layer.isHidden) item.visible = true;
                                else item.visible = false;
                            });
                            // dynamicMapServiceLayer.refresh();
                        })
                    map.add(dynamicMapServiceLayer);
                    graphicLayersIds.forEach((graphicLayerId) => {
                        var graphicLayer = new GraphicsLayer({
                            id: graphicLayerId,
                            opacity: (graphicLayerId == "ThematicGraphicLayer") ? 0.8 : 1
                        });
                        map.layers.add(graphicLayer);
                    });
                    this.setState({mapService:dynamicMapServiceLayer, map:map})

                }

            }).catch(err => {
                console.log(err);
                notificationMessage(this.props.t("retrievError"))
                showLoading(false);
                this.props.navigate("/serverErr")
            });
        }, function (error) {
            // Use the errback function to handle when the view doesn't load properly
            console.log("The view's resources failed to load: ", error);
        });


    }
    componentDidUpdate(){
        if(this.state.map){
        if(this.props.currentLayer ==='incidents940'){
            IdentityManager.registerToken({
                token: this.props.mainData.user?.esriToken,
                server: window.mapServerUrl
            });
            if(!this.state.incidentMapService){

                let incidentMapServiceLayer = new MapImageLayer({
                url: window.incidentMapServiceUrl || window.mapUrl ,
                id: 'incidentBaseMap',
            });
            this.state.map.add(incidentMapServiceLayer);
            this.setState({incidentMapService:incidentMapServiceLayer})
            if(this.state?.mapService) this.state.mapService.set({visible: false});
        }else{
            this.state.incidentMapService.set({visible:true});
            if(this.state?.mapService) this.state.mapService.set({visible: false});
        }
        }else if(this.props.currentLayer){
            if(this.state?.mapService && !this.state.mapService.visible) this.state.mapService.set({visible :true});
            if(this.state.incidentMapService && this.state.incidentMapService.visible) this.state.incidentMapService.set({visible :false});
        }
    }
    }
    render() {
        return (
            <div id='mapDiv' style={{
                height: "100%",
                marginTop: this.props.openDrawer && this.props.mapExp && localStorage.user
                    ? "57px" : "",
                marginRight:
                    this.props.openDrawer &&
                        this.props.routeName !== "generalSearch" &&
                        this.props.routeName !== "outerSearch"
                        ? "300px"
                        : this.props.openDrawer &&
                            (this.props.routeName === "generalSearch" ||
                                this.props.routeName === "outerSearch")
                            ? "400px"
                            : "unset",
            }}>
                
            </div>
        )
    }
}

export default withRouter(withTranslation('common')(MapComponent));
