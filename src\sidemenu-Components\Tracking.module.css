.optionsList {
  background-color: white;
  z-index: 9999;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 16px;
  padding-top: 0;
  display: flex;
  flex-direction: column;
  gap: 0px;
  border-radius: 28px 28px 0px 0px;
  animation: slideUp 0.5s ease-out forwards;
}

.borderCotainer {
  display: flex;
  cursor: pointer;
  z-index: 2;
}

.border {
  width: 32px;
  height: 4px;
  border-radius: 100px;
  display: block;
  background-color: #a8a8a8;
  margin: auto;
  margin-bottom: 15px;
  margin-top: 15px;
}

.infoHeading {
  display: flex;
  align-items: center;
  width: 100%;
  align-items: center;
  /* margin-bottom: -7px; */
  /* margin-top: 15px; */
  /* margin-bottom: 10px; */
  margin-top: -10px;
  cursor: pointer;
}

.infoHeading h1 {
  font-size: 16px;
  font-weight: 700;
  line-height: 24px;
  color: #2b2b2b;
  margin: 0;
  /* margin-right: 8px; */
  padding: 0px 5px;
}

.infoHeading img {
  position: relative;
  top: 1px;
  margin: 0px 8px;
  cursor: pointer;
}

.QueryDataContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.QueryData {
  display: flex;
  align-items: center;
  width: 100%;
  height: 56px;
}

.selectedOption {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #ffffff;
  width: 100%;
  /* Adjust as needed */
  padding: 8px 12px;
  cursor: pointer;
  border: 1px solid #a8a8a8;
  border-radius: 16px;
  transition: background-color 0.3s;
  font-size: 12px;
  padding: 16px;
  font-weight: 700;
  color: #a8a8a8;
  height: 56px;
}

.arrow {
  margin-right: 8px;
  font-size: 12px;
  transition: transform 0.3s ease;
  color: #a8a8a8;
}

.rotate {
  transform: rotate(180deg);
}

.card {
  border-radius: 16px;
  background-color: #FFFFFF99;
  /* box-shadow: none; */
  /* border: 1px solid #efefef; */
  box-shadow: 0px 0px 10px 0px #ddd;
  padding: 10px 12px;
  /* margin-top: 10px; */
}

.favoriteCardContent {
  /* display: flex;
  align-items: center;
  justify-content: space-between; */
  /* padding: 17px 8px; */
}

.favoriteInfo {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.favoriteTitle,
.favoriteAddress {
  padding: 0;
  margin: 0;
  font-family: Droid Arabic Kufi !important;
  text-align: right;
}

.favoriteTitle {
  font-size: 14px;
  font-weight: 700;
  line-height: 20px;
  color: #2b2b2b;
}

.favoriteAddress {
  font-size: 11px;
  font-weight: 700;
  color: #a8a8a8;
}

.icon {
  width: 112px;
  background-color: #1fbc3826;
  padding: 4px;
  border-radius: 32px;
  text-align: center;
}

.iconSpan {
  font-size: 11px;
  font-weight: 700;
  line-height: 12px;
  color: #1fbc38;
  letter-spacing: 0.4px;
}

.optionsDetails {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.optionsDetails span:first-of-type {
  font-size: 12px;
  font-weight: 700;
  color: #a8a8a8;
}

.optionsDetails span:last-of-type {
  font-size: 16px;
  font-weight: 400;
  text-align: right;
  color: #2b2b2b;
}

@keyframes slideUp {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }

  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.finished,
.running {
  /* background-color: #1fbc3826; */
  /* color: #1fbc38; */
  padding: 7px 0px;
  display: block;
  width: 112px;
  border-radius: 32px;
  text-align: center;
  /* color: #67d4ea; */
  font-size: 11px;
  font-weight: 700;
}

.finished {
  background-color: #FFE2E0 !important;
  color: #ff3b30 !important;
}

.running {
  background: #c7e1ff !important;
  color: #007AFF !important;
  padding: 7px 0px;
  display: block;
  width: 112px;
  border-radius: 32px;
  text-align: center;
  color: #67d4ea;
  font-size: 11px;
  font-weight: 700;
}

.active {
  border-color: #338c9a !important;
  border-top-width: 0.3em !important;
}
