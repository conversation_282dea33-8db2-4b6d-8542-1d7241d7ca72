{"sideLinks": {"coordinateSearch": "Search By Coordinates", "generalSearch": "General Search", "MetaSearch": "Search By Attributes", "measurementTools": "Measurement Tools", "contact": "Contact Us", "paint": "Drawing", "language": "العربية", "changeLangTooltip": "Change Language", "print": "Print", "bookmark": "Add Bookmark", "importFiles": "Import Files", "exportGoogle": "Export To Google (K-M-L)", "marsad": "Marsad", "incidentsDashboard": "Incidents Dashboard ", "layersMap": "Layers Map", "interactiveMap": "Interactive Map", "updatingRequests": "Updating Requests", "setting": "Setting"}, "import": "Import", "file": "File", "link": "Link", "the_link": "Link", "enter_link": "Enter Link", "enter_token": "<PERSON>ter <PERSON>", "add_token": "Add <PERSON>", "mainBaseMap": "Base Map", "mapName": "Map Name", "enter_mapName": "Enter Map Name", "mainTitle": "GIS Map Explorer", "titleEdition": "Version No. ", "upload": "Upload", "sideMsg": "We would like to point out that the measurements, areas and uses of plots of land and plans are indicative and there may be some differences in the data, which are taken into account with the periodic update, and you can contact via e-mail <EMAIL>", "addFileToMapError": "Error during adding file to map. Please try again", "uploadFilesError": "Error occured during upload the file. Please try again", "entre-name": "Enter Name", "bookmark": "BookMark", "area": "Area", "tools": "Tools", "drawingShape": "Drawing shapes", "my_maps": "My Maps", "farasan_islands_map": "Farasan Islands map", "removeAll": "Remove All", "people_vehicles": "People and Vehicles", "violations": "Violations", "general": "General", "save": "Save", "special": "Special", "shared": "Shared", "study_areas": "Study Areas", "choose_study_area": "Choose Study Area", "areas": "Areas", "study_area_name": "Study Area Name", "area_name": "Area Name", "clearAll": "Clear All", "logoutMessage": "Do you want to logout ?", "confirm": "Confirm", "cancel": "Cancel", "showFileToMapError": "Error during showing file to map. Please try again"}