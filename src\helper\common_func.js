import Query from "@arcgis/core/tasks/support/Query";
import IdentityManager from "@arcgis/core/identity/IdentityManager";
import QueryTask from "@arcgis/core/tasks/QueryTask";
import esriRequest from "@arcgis/core/request";
import StatisticDefinition from "@arcgis/core/rest/support/StatisticDefinition";
import Graphic from "@arcgis/core/Graphic";
import moment from "moment-hijri";
import * as geometryEngine from "@arcgis/core/geometry/geometryEngine";
import PictureMarkerSymbol from "@arcgis/core/symbols/PictureMarkerSymbol";
import SimpleLineSymbol from "@arcgis/core/symbols/SimpleLineSymbol";
import SimpleMarkerSymbol from "@arcgis/core/symbols/SimpleMarkerSymbol";
import SimpleFillSymbol from "@arcgis/core/symbols/SimpleFillSymbol";
import * as projection from "@arcgis/core/geometry/projection";
import SpatialReference from "@arcgis/core/geometry/SpatialReference";
import Point from "@arcgis/core/geometry/Point";
import Polyline from "@arcgis/core/geometry/Polyline";
import Polygon from "@arcgis/core/geometry/Polygon";
import IdentifyParameters from "@arcgis/core/rest/support/IdentifyParameters";
import * as identify from "@arcgis/core/rest/identify";
import * as geoprocessor from "@arcgis/core/rest/geoprocessor";
import Extent from "@arcgis/core/geometry/Extent";
import BaseTileLayer from "@arcgis/core/layers/BaseTileLayer";
import Color from "@arcgis/core/Color";
import locationIcon from "../assets/images/location.gif";
// import { layersSetting } from './layers'

import { message } from "antd";

export const CustomTileLayer = BaseTileLayer.createSubclass({
  properties: {
    urlTemplate: null,
    id: null,
    tint: {
      value: null,
      type: Color,
    },
  },

  // generate the tile url for a given level, row and column
  getTileUrl: function (level, row, col) {
    return this.urlTemplate
      .replace("{z}", level)
      .replace("{x}", col)
      .replace("{y}", row);
  },

  // This method fetches tiles for the specified level and size.
  // Override this method to process the data returned from the server.
  fetchTile: function (level, row, col, options) {
    // call getTileUrl() method to construct the URL to tiles
    // for a given level, row and col provided by the LayerView
    const url = this.getTileUrl(level, row, col);

    // request for tiles based on the generated url
    // the signal option ensures that obsolete requests are aborted
    return esriRequest(url, {
      responseType: "image",
      signal: options && options.signal,
    }).then(
      function (response) {
        // when esri request resolves successfully
        // get the image from the response
        const image = response.data;
        const width = this.tileInfo.size[0];
        const height = this.tileInfo.size[0];

        // create a canvas with 2D rendering context
        const canvas = document.createElement("canvas");
        const context = canvas.getContext("2d");
        canvas.width = width;
        canvas.height = height;

        // Apply the tint color provided by
        // by the application to the canvas
        if (this.tint) {
          // Get a CSS color string in rgba form
          // representing the tint Color instance.
          context.fillStyle = this.tint.toCss();
          context.fillRect(0, 0, width, height);

          // Applies "difference" blending operation between canvas
          // and steman tiles. Difference blending operation subtracts
          // the bottom layer (canvas) from the top layer (tiles) or the
          // other way round to always get a positive value.
          context.globalCompositeOperation = "difference";
        }

        // Draw the blended image onto the canvas.
        context.drawImage(image, 0, 0, width, height);

        return canvas;
      }.bind(this)
    );
  },
});

export const project = (features, outSR, callback) => {
  if (features && features.length > 0) {
    var isSameWkid = false;
    if (
      (features[0].geometry &&
        features[0].geometry.spatialReference.wkid == outSR) ||
      (features[0].spatialReference &&
        features[0].spatialReference.wkid == outSR)
    ) {
      isSameWkid = true;
      callback(features);
    }
    if (!isSameWkid) {
      projection.load().then(() => {
        let outSpatialReference = new SpatialReference({
          wkid: outSR,
        });
        let returnFeatures = [];
        features.forEach((graphic) => {
          if (graphic.geometry)
            graphic.geometry = projection.project(
              graphic.geometry,
              outSpatialReference
            );
          else {
            graphic = projection.project(graphic, outSpatialReference);
          }
          returnFeatures.push(graphic);
        });
        callback(returnFeatures);
      });
    }
  } else {
    callback(features);
  }
};

export const queryTask = function (settings) {
  console.log({settings});
  if (!settings.notShowLoading && !settings.returnExecuteObject)
    showLoading(true);

  var query = new Query();
  query.returnGeometry = settings.returnGeometry || false;
  if (settings.geometry) query.geometry = settings.geometry;

  query.returnIdsOnly = settings.returnIdsOnly || false;
  //query.returnCountOnly = settings.returnCountOnly || false
  query.outFields = settings.outFields || ["*"];
  query.returnDistinctValues = settings.returnDistinctValues || false;

  if (query.returnDistinctValues) {
    query.returnGeometry = false;
  }

  if (settings.num && !settings.statistics) {
    query.start = settings.start;
    query.num = settings.num;
  }

  if (settings.statistics) {
    query.outStatistics = [];
    var statisticDefinition = {};
    settings.statistics.forEach((val) => {
      statisticDefinition = new StatisticDefinition();
      statisticDefinition.statisticType = val.type;
      statisticDefinition.onStatisticField = val.field;
      statisticDefinition.outStatisticFieldName = val.name;
      query.outStatistics.push(statisticDefinition);
    });
  }

  query.groupByFieldsForStatistics = settings.groupByFields;
  // query.returnCountOnly = settings.returnCountOnly || false
  if (settings.preQuery) {
    settings.preQuery(query, Query);
  }
  //for pagination --> use num, start
  if (settings.start && settings.num) {
    query.num = settings.num;
    query.start = settings.start;
  }
  if (settings.orderByFields) {
    query.orderByFields = settings.orderByFields;
  }

  if (settings.queryWithGemoerty) {
    query.geometry = settings.geometry;
    if (settings.where) {
      query.where = settings.where;
    }
    if (settings.distance) {
      query.distance = settings.distance || 5;
    }
    query.units = "meters"; //"kilometers";
  } else {
    query.where = settings.where || "1=1";
  }

  /*if (query.where) {
          query.where = "(" + query.where + ")";
      }*/
  var token = "";
  if (window.esriToken) token = "?token=" + window.esriToken;
  // var hasPermission = $rootScope.getPermissions('splitandmerge.MAPEXPLORER', 'modules.INVESTMENTLAYERS')
  // if (hasPermission) {
  // token = '?token=' + $rootScope.User.esriToken
  // }
  if (settings.url.indexOf("?token=") > -1) {
    token = "";
  }

  var queryTask = new QueryTask(settings.url + token); // + "?token=" + $rootScope.User.esriToken + "&username='d'")

  function callback(data) {
    // store.dispatch({type:'Show_Loading_new',loading: false})
    if (!settings.notShowLoading && !settings.returnExecuteObject)
      showLoading(false);
    settings.callbackResult(data);
  }

  function callbError(data) {
    message.error("حدث خطأ اثناء استرجاع البيانات");
    //window.notifySystem('warning', 'حدث خطأ اثناء استرجاع البيانات')
    // store.dispatch({type:'Show_Loading_new',loading: false})
    showLoading(false);
    if (settings.callbackError) {
      settings.callbackError(data);
    }
  }

  if (settings.returnCountOnly) {
    queryTask.executeForCount(query).then(callback, callbError);
  } else if (settings.returnExecuteObject) {
    !settings.notShowLoading && showLoading(false);
    return queryTask.execute(query);
  } else {
    queryTask.execute(query).then(callback, callbError);
  }
};
// export const queryChartData = function (settings) {
//   return new Promise((resolve, reject) => {
//     if (!settings.notShowLoading && !settings.returnExecuteObject)
//       showLoading(true);

//     var query = new Query();
//     query.returnGeometry = settings.returnGeometry || false;
//     query.outFields = settings.outFields || ["*"];
//     query.returnDistinctValues = settings.returnDistinctValues || false;

//     // Handle different chart types and statistics
//     if (settings.chartType === 'statistics') {
//       query.outStatistics = [];
//       settings.statistics.forEach((val) => {
//         var statisticDefinition = new StatisticDefinition();
//         statisticDefinition.statisticType = val.type;
//         statisticDefinition.onStatisticField = val.field;
//         statisticDefinition.outStatisticFieldName = val.name;
//         query.outStatistics.push(statisticDefinition);
//       });
//       if (settings.groupByFields) {
//         query.groupByFieldsForStatistics = settings.groupByFields;
//       }
//     } else if (settings.chartType === 'sum') {
//       // Handle sum type by creating a statistic definition
//       const sumField = settings.dependentFields?.length > 0 
//         ? settings.dependentFields[0] 
//         : settings.outFields[0];
//       const statisticDefinition = new StatisticDefinition();
//       statisticDefinition.statisticType = 'SUM';
//       statisticDefinition.onStatisticField = sumField;
//       statisticDefinition.outStatisticFieldName = `SUM_${sumField}`;
//       query.outStatistics = [statisticDefinition];

//     }

//     query.where = settings.where || "1=1";
//     if (settings.geometry) query.geometry = settings.geometry;
//     if (settings.start && settings.num) {
//       query.start = settings.start;
//       query.num = settings.num;
//     }
//     if (settings.orderByFields) query.orderByFields = settings.orderByFields;

//     var token = window.esriToken ? `?token=${window.esriToken}` : '';
//     if (settings.url.includes("?token=")) token = '';

//     var queryTask = new QueryTask(settings.url + token);

//     function callback(data) {
//       let processedData;
//       switch (settings.chartType) {
//         case "count":
//           processedData = data;
//           break;
//         case "sum":
//           if (data.features?.length > 0) {
//             const sumField = settings.dependentFields?.length > 0 
//               ? settings.dependentFields[0] 
//               : settings.outFields[0];
//             processedData = data.features[0].attributes[`SUM_${sumField}`] || 0;
//           } else {
//             processedData = 0;
//           }
//           break;
//         case "bar":
//         case "statistics":
//           processedData = data.features.map(f => f.attributes);
//           break;
//         default:
//           processedData = data;
//       }
//       resolve(processedData);
//     }

//     function callbackError(error) {
//       message.error("Error fetching data");
//       showLoading(false);
//       reject(error);
//     }

//     if (settings.chartType === 'count') {
//       queryTask.executeForCount(query).then(callback, callbackError);
//     } else {
//       queryTask.execute(query).then(callback, callbackError);
//     }
//   });
// };

export const queryChartData = function (settings) {
  return new Promise((resolve, reject) => {
    // Optional loading indicator
    if (!settings.notShowLoading && !settings.returnExecuteObject)
      showLoading(true);

    // Create a new Query object
    var query = new Query();
    
    // Configure query parameters
    query.returnGeometry = settings.returnGeometry || false;
    query.outFields = settings.outFields || ["*"];
    query.returnDistinctValues = settings.returnDistinctValues || false;

    // Handle different chart types and statistics
    if (settings.chartType === 'statistics') {
      query.outStatistics = [];
      settings.statistics.forEach((val) => {
        var statisticDefinition = new StatisticDefinition();
        statisticDefinition.statisticType = val.type;
        statisticDefinition.onStatisticField = val.field;
        statisticDefinition.outStatisticFieldName = val.name;
        query.outStatistics.push(statisticDefinition);
      });
      
      // Optional group by fields for statistics
      if (settings.groupByFields) {
        query.groupByFieldsForStatistics = settings.groupByFields;
      }
    } else if (settings.chartType === 'sum') {
      // Handle sum type by creating a statistic definition
      const sumField = settings.dependentFields?.length > 0
        ? settings.dependentFields[0]
        : settings.outFields[0];
      
      const statisticDefinition = new StatisticDefinition();
      statisticDefinition.statisticType = 'SUM';
      statisticDefinition.onStatisticField = sumField;
      statisticDefinition.outStatisticFieldName = `SUM_${settings.dependentFields[0]}`;
      
      query.outStatistics = [statisticDefinition];
    }

    // Set additional query parameters
    query.where = settings.where || "1=1";
    if (settings.geometry) query.geometry = settings.geometry;
    
    // Pagination support
    if (settings.start && settings.num) {
      query.start = settings.start;
      query.num = settings.num;
    }

    // Sorting support
    if (settings.orderByFields) query.orderByFields = settings.orderByFields;

    // Handle authentication token
    var token = window.esriToken ? `?token=${window.esriToken}` : '';
    if (settings.url.includes("?token=")) token = '';

    // Create QueryTask
    var queryTask = new QueryTask(settings.url + token);

    // Callback to process query results
    function callback(data) {
      let processedData;
      switch (settings.chartType) {
        case "count":
          processedData = data;
          break;
        case "sum":
          if (data.features?.length > 0) {
            const sumField = settings.dependentFields?.length > 0
              ? settings.dependentFields[0]
              : settings.outFields[0];
            processedData = data.features[0].attributes[`SUM_${settings.dependentFields[0]}`] || 0;
          } else {
            processedData = 0;
          }
          break;
        case "bar":
        case "statistics":
          processedData = data.features.map(f => f.attributes);
          break;
        default:
          processedData = data;
      }
      resolve(processedData);
    }

    // Error handling callback
    function callbackError(error) {
      message.error("Error fetching data");
      showLoading(false);
      reject(error);
    }

    // Execute query based on chart type
    if (settings.chartType === 'count') {
      queryTask.executeForCount(query).then(callback, callbackError);
    } else {
      queryTask.execute(query).then(callback, callbackError);
    }
  });
};
export const drawLine = function (settings) {
  var feature = settings.feature;
  var map = settings.map;
  var $event = settings.event;

  var canvas = document.getElementById("myCanvas");
  var context = canvas.getContext("2d");

  context.canvas.width = window.innerWidth;
  context.canvas.height = window.innerHeight;
  context.clearRect(0, 0, canvas.width, canvas.height);

  var point;

  if (feature.geometry) {
    if (feature.geometry.type == "point") {
      point = map.view.toScreen(feature.geometry);
    } else if (feature.geometry.type == "polygon") {
      point = map.view.toScreen(feature.geometry.centroid);
    } else {
      point = map.view.toScreen(feature.geometry.extent.center);
    }

    if (
      !(
        point.x < 0 ||
        point.y < 0 ||
        point.x > window.innerWidth - (settings.hideFromWidth || 450) ||
        point.y > window.innerHeight - (settings.hideFromHeight || 0)
      )
    ) {
      context.moveTo(point.x, point.y);

      context.lineTo($event.clientX, $event.clientY);
      context.lineWidth = 1.5;

      var color = "#212121";

      context.strokeStyle = color;
      context.stroke();

      context.beginPath();
      context.strokeStyle = color;
      context.arc(point.x, point.y, 3, 0, 2 * Math.PI, true);
      context.fill();
    }
  }
};

export const navigateToGoogle = (lat, long) => {
  window.open(`https://maps.google.com/maps?q=${lat},${long}`, "_blank");
};

export const zoomToFeatureDefault = (feature, map, is3d) => {
  highlightFeature(feature, map, {
    layerName: "ZoomGraphicLayer",
    isZoom: true,
    zoomDuration: 1000,
    isDashStyle: true,
  });
};
export const zoomToFeatures = (features, map) => {
  let fullExtent = null;
  for (let i = 0; i < features.length; i++) {
    if (features[i].geometry && features[i].geometry.extent) {
      if (!fullExtent) fullExtent = features[i].geometry.extent.clone();
      else fullExtent.union(features[i].geometry.extent);
    }
  }
  let cloneExt = fullExtent.clone();

  map.view.goTo(
    {
      target: features,
      extent: cloneExt,
    },
    {
      duration: 1000, // Duration of animation will be 1 seconds
    }
  );
};
export const zoomToFeature = function (feature, map, zoomFactor, callback) {
  
    var myFeatureExtent;
    //
    try {
      myFeatureExtent = feature.geometry.extent.clone();
    } catch (e) {
      if (feature.length) {
        feature.forEach(function (f) {
          if (f.geometry) {
            if (f.geometry.type == "polygon") {
              if (!f.geometry.spatialReference)
                f.geometry.spatialReference = {
                  wkid: map.spatialReference.wkid,
                };
              f.geometry = new Polygon(f.geometry);
            }
          } else if (f.type) {
            if (f.type == "point") {
              f.geometry = new Point(f);
            }
          }
        });
      } else {
        if (feature.geometry.type == "polygon") {
          feature.geometry = new Polygon(feature.geometry);
        }
        feature = [feature];
      }
      try {
      myFeatureExtent = feature.geometry.extent.clone();
      } catch (e) {
        // $rootScope.$apply()
      }
    }

    if (!feature.length) {
      if (feature.geometry.type == "point") {
        let extent = new Extent(
          myFeatureExtent.xmin - zoomFactor,
          myFeatureExtent.ymin - zoomFactor,
          myFeatureExtent.xmax + zoomFactor,
          myFeatureExtent.ymax + zoomFactor,
          map.spatialReference
        );

        map.setExtent(extent.expand(5)).then(callback);
      } else {
        if (zoomFactor) {
          myFeatureExtent.xmin = myFeatureExtent.xmin - zoomFactor;
          myFeatureExtent.ymin = myFeatureExtent.ymin - zoomFactor;
          myFeatureExtent.xmax = myFeatureExtent.xmax + zoomFactor;
          myFeatureExtent.ymax = myFeatureExtent.ymax + zoomFactor;
        }

        map.setExtent(myFeatureExtent.expand(2)).then(callback);
      }
    } else {
      if (feature[0].geometry) {
        if (feature[0].geometry.type == "point") {
          var extent = new Extent(
            myFeatureExtent.xmin - zoomFactor,
            myFeatureExtent.ymin - zoomFactor,
            myFeatureExtent.xmax + zoomFactor,
            myFeatureExtent.ymax + zoomFactor,
            map.spatialReference
          );

          map.setExtent(extent.expand(5)).then(callback);
        } else {
          if (zoomFactor) {
            myFeatureExtent.xmin = myFeatureExtent.xmin - zoomFactor;
            myFeatureExtent.ymin = myFeatureExtent.ymin - zoomFactor;
            myFeatureExtent.xmax = myFeatureExtent.xmax + zoomFactor;
            myFeatureExtent.ymax = myFeatureExtent.ymax + zoomFactor;
          }

          map.setExtent(myFeatureExtent.expand(2)).then(callback);
        }
      } else if (feature[0].type == "point") {
        extent = new Extent(
          myFeatureExtent.xmin - zoomFactor,
          myFeatureExtent.ymin - zoomFactor,
          myFeatureExtent.xmax + zoomFactor,
          myFeatureExtent.ymax + zoomFactor,
          map.spatialReference
        );

        map.setExtent(extent.expand(5)).then(callback);
      } else {
        if (zoomFactor) {
          myFeatureExtent.xmin = myFeatureExtent.xmin - zoomFactor;
          myFeatureExtent.ymin = myFeatureExtent.ymin - zoomFactor;
          myFeatureExtent.xmax = myFeatureExtent.xmax + zoomFactor;
          myFeatureExtent.ymax = myFeatureExtent.ymax + zoomFactor;
        }

        map.setExtent(myFeatureExtent.expand(2)).then(callback);
      }
    }
};

export const getDistrictNameById = (map, districtId) => {
  return new Promise((resolve, reject) => {
    if (!isNaN(Number(districtId))) {
      esriRequest(
        `${window.mapUrl}/${getLayerId(map.__mapInfo, "District_Boundary")}`
      ).then((res) => {
        let district = res?.fields?.[2]?.domain?.codedValues.find(
          (district) => district?.code == districtId
        );

        return resolve(
          (district?.name && convertToArabic(district?.name)) || ""
        );
      });
    } else {
      return resolve("");
    }
  });
};



export const groupBy = function (xs, key) {
  return xs.reduce((rv, x) => {
    (rv[x.attributes[key]] = rv[x.attributes[key]] || []).push(x);
    return rv;
  }, {});
};

export const DrawCirclePoints = (points, radius, center, pointIndex) => {
  let slice = (2 * Math.PI) / points;
  //for (let i = 0; i < points; i++)
  //{
  let angle = slice * pointIndex;
  let newX = center.x + radius * Math.cos(angle);
  let newY = center.y + radius * Math.sin(angle);
  return { x: newX, y: newY };
  //}
};

export const isInitialExtent = (view) => {
  return (
    view.camera.position.x == window.initialCameraPosition.position.x &&
    view.camera.position.y == window.initialCameraPosition.position.y &&
    view.camera.position.z == window.initialCameraPosition.position.z
  );
};

export const drawText = (
  point,
  text,
  map,
  layerName,
  fontSize,
  offsetX,
  offsetY
) => {
  let textSymbol = {
    type: "text", // autocasts as new TextSymbol()
    color: "white",
    haloColor: "black",
    haloSize: "1px",
    text: text,
    xoffset: offsetX || 3,
    yoffset: offsetY || 3,
    font: {
      // autocasts as new Font()
      size: fontSize || 20,
      weight: "bold",
    },
  };

  let graphicLayer = map.findLayerById(layerName);

  var graphic = new Graphic({
    geometry: point.geometry || point,
    symbol: textSymbol,
  });

  graphicLayer.add(graphic);
};

export const zoomToFeatureByObjectId = (
  attributes,
  map,
  returnGeometryAndNotZoom,
  callback
) => {
  if (attributes) {
    if (attributes.geometry) {
      if (!returnGeometryAndNotZoom) {
        highlightFeature(attributes, map, {
          layerName: "ZoomGraphicLayer",
          isZoom: true,
          zoomDuration: 1000,
          isDashStyle: true,
        });
      }
      if (callback) callback(attributes);
    } else {
      let layerdId = getLayerId(map.__mapInfo, attributes.layerName);

      queryTask({
        url: window.mapUrl + "/" + layerdId,
        where: "OBJECTID = " + attributes.id || attributes.OBJECTID,
        outFields: ["OBJECTID"],
        returnGeometry: true,
        callbackResult: ({ features }) => {
          if (!returnGeometryAndNotZoom && features.length) {
            highlightFeature(features[0], map, {
              layerName: "ZoomGraphicLayer",
              isZoom: true,
              zoomDuration: 1000,
              isDashStyle: true,
            });
          }
          if (callback) callback(features[0]);
        },
        callbackError(error) {},
      });
    }
  }
};
export const zoomToFeatureBySpatialID = (
  attributes,
  layerName,
  map,
  returnGeometryAndNotZoom,
  callback,
   callBackErr,
   getAttrData=false
) => {
  if (attributes) {
    if (attributes.geometry && !getAttrData) {
      if (!returnGeometryAndNotZoom) {
        highlightFeature(attributes, map, {
          layerName: "ZoomGraphicLayer",
          isZoom: true,
          zoomDuration: 1000,
          isDashStyle: true,
        });
      }
      if (callback) callback(attributes);
    } else {
      let layerdId = getLayerId(map.__mapInfo, layerName);

      queryTask({
        url: window.mapUrl + "/" + layerdId,
        where: "PARCEL_SPATIAL_ID = " + attributes.id,
        outFields: ["*"],
        returnGeometry: true,
        callbackResult: ({ features }) => {
          if(features.length)
          getFeatureDomainName(features, layerdId).then((reqFeats) => {

            if (!returnGeometryAndNotZoom) {
              highlightFeature(reqFeats[0], map, {
                layerName: "ZoomGraphicLayer",
                isZoom: true,
                zoomDuration: 1000,
              isDashStyle: true,
            });
          }
          if (callback) callback(reqFeats[0]);
        })
        else{
          callback(undefined);
        }
        },
        callbackError(error) {
          if(callBackErr) callBackErr(error);
        },
      });
    }
  }
};

export const zoomToFeatureByFilter = (
  where,
  layerName,
  map,
  returnGeometryAndNotZoom,
  callback,
  outfields
) => {
  let layerdId = getLayerId(map.__mapInfo, layerName);

  queryTask({
    url: window.mapUrl + "/" + layerdId,
    where: where,
    outFields: [outfields||"OBJECTID"],
    returnGeometry: true,
    callbackResult: ({ features }) => {
      if (features.length > 0) {
        if (!returnGeometryAndNotZoom) {
          highlightFeature(features[0], map, {
            layerName: "ZoomGraphicLayer",
            isZoom: true,
            isHighlighPolygonBorder: true,
            zoomDuration: 1000,
            notExpandLevel: true,

          });
        }
        if (callback){
          getFeatureDomainName(features, layerdId).then((reqFeats) => {
          
          callback(reqFeats[0]);
          })  
        }
      }else{
        let graphicLayer = map.findLayerById("ZoomGraphicLayer");
        if (graphicLayer) graphicLayer.removeAll();
      }
    },
    callbackError(error) {
      let graphicLayer = map.findLayerById("ZoomGraphicLayer");
        if (graphicLayer) graphicLayer.removeAll();
        console.log({error});
    },
  });
};

export const getFromEsriRequest = function (url) {
  //store.dispatch({ type: 'Show_Loading_new', loading: true })

  var requestHandler = esriRequest(url, {
    responseType: "json",
  });

  return requestHandler.then(
    ({ data }) => {
      //store.dispatch({ type: 'Show_Loading_new', loading: false })
      return data;
    },
    (error) => {
      //store.dispatch({ type: 'Show_Loading_new', loading: false })
      throw "error";
    }
  );
};
// let mapInfo

export const getMapInfo = function (url, layersSetting = {}) {
  return new Promise(async (resolve, reject) => {
    let out = { layersSetting };
    var token = "";
    if (window.esriToken) token = "&token=" + window.esriToken;
    try {
      let mapInfo = await getFromEsriRequest(url + "?f=pjson" + token);
      out.info = {};
      out.info.mapInfo = mapInfo;
      let legendInfo = await getFromEsriRequest(
        url + "/legend" + "?f=pjson" + token
      );
      out.info.$legends = legendInfo.layers;
      let layerInfo = await getFromEsriRequest(
        url + "/layers" + "?f=pjson" + token
      );
      out.info.$layers = layerInfo;

      out.info.$layers.layers = out.info.$layers.layers.map((layer, key) => {
        if (
          out.layersSetting[layer.name] &&
          out.layersSetting[layer.name].order
        )
          layer.viewerOrder = out.layersSetting[layer.name].order;
        layer.alias = out.info.$layers.layers[key].name;
        return layer;
      });

      let visibiles = [];
      out.info.$legends = out.info.$legends.map((layer, key) => {
        layer.visible = out.info.$layers.layers[key]?.defaultVisibility;
        if (
          out.layersSetting[layer.name] &&
          out.layersSetting[layer.layerName]?.order
        )
          layer.viewerOrder = out.layersSetting[layer.layerName].order;
        // //

        if (layer.visible) {
          visibiles.push(layer.layerId);
        }

        layer.isHidden =
          out.layersSetting[layer.layerName] &&
          out.layersSetting[layer.layerName]?.isHidden;
        layer.alias = out.info.$layers.layers[key].name;
        return layer;
      });

      out.mapVisibleLayerIDs = visibiles;
      mapInfo = out;
      resolve(out);
    } catch (err) {
      reject(err);
    }
  });
};

export const addPictureSymbol = function (
  point,
  icon,
  layerName,
  map,
  width,
  height
) {
  var symbol = new PictureMarkerSymbol({
    url: icon,
    height: height || 48,
    width: width || 48,
    yoffset: (height || 48) / 2 - 2,
  });
  var graphic = new Graphic({
    geometry: point,
    symbol: symbol,
  });

  map.findLayerById(layerName || "identifyGraphicLayer").removeAll();
  map.findLayerById(layerName || "identifyGraphicLayer").add(graphic);
};

export const clearCanvasLine = function () {
  var canvas = document.getElementById("myCanvas");
  var context = canvas.getContext("2d");
  context.clearRect(0, 0, window.innerWidth, window.innerHeight);
};

export const highlightFeature = function (features, map, settings) {
  var symbol;
  let fillColor = settings.fillColor || [0, 0, 0, 0.3];
  let strokeColor = settings.strokeColor || "black";
  let highlighColor = settings.highlighColor || [0, 255, 255, 0.5];

  let graphicLayer = map.findLayerById(settings.layerName);

  if (!settings.noclear) graphicLayer.removeAll();

  features = features?.length ? features : [features];

  features.forEach((feature) => {
    if (settings.isHiglightSymbol) {
      strokeColor = highlighColor;
      fillColor = settings.fillColor || highlighColor;
    }

    if (feature.type === "point" || feature.geometry.type === "point") {
      if (settings.isAnimatedLocation) {
        symbol = new PictureMarkerSymbol({
          url: locationIcon,
          height: 48,
          width: 48,
          yoffset: 48 / 2 - 2,
        });
      } else {
        symbol = new SimpleMarkerSymbol({
          style: "circle",
          color: settings.isHiglightSymbol ? highlighColor : fillColor,
          size: "20px", // pixels
          outline: {
            color: "black",
            width: 1, // points
          },
        });
      }
    } else if (
      feature.type === "polyline" ||
      feature.geometry.type === "polyline" ||
      feature.geometry.paths
    ) {
      symbol = new SimpleLineSymbol({
        color: highlighColor,
        width: "10px",
        style: "solid",
      });
    } else {
      symbol = GetSymbol(
        settings,
        settings.fillColor || fillColor,
        strokeColor
      );
    }

    if (!settings.isZoomOnly) {
      var graphic = new Graphic({
        geometry: feature.geometry || feature,
        symbol: symbol,
        attributes: settings.attr,
      });

      graphicLayer.add(graphic);
    }
  });

  if (!settings.listOfFeatures && settings.isZoom) {
    var fullExtent = null;
    for (var i = 0; i < features.length; i++) {
      if (features[i].geometry && features[i].geometry.extent) {
        if (!fullExtent) fullExtent = features[i].geometry.extent.clone();
        else fullExtent.union(features[i].geometry.extent);
      }
    }

    //for polygon , polyline
    if (fullExtent) {
      if (map.view.type == "3d") {
        if (settings.isZoomToCenter && features.length == 1) {
          map.view.goTo(
            {
              target: features[0].geometry.centroid,
              zoom: settings.zoomLevel || 9,
            },
            { duration: 1000 }
          );
        } else {
          map.view
            .goTo({
              target: features,
              heading: 0,
              tilt: window.is3dZoomEnabled ? 45 : 0,
            })
            .then(() => {
              if (settings.isZoomMoreLevels) {
                map.view.goTo({
                  zoom: map.view.zoom + 1.5,
                });
              }
            });
        }
      } else {
        var cloneExt = fullExtent.clone();

        map.view.goTo(
          {
            target: features,
            extent: settings.notExpandLevel ? cloneExt : cloneExt.expand(settings?.zoomFactor ? settings?.zoomFactor : 2),
          },
          {
            duration: settings.zoomDuration || 1000, // Duration of animation will be 1 seconds
          }
        );
      }
    } else {
      // array of points
      if (features.length > 1) {
        const [convexHull] = geometryEngine.convexHull(
          features.map((f) => {
            return f.geometry;
          }),
          true
        );

        map.view.goTo(
          { target: convexHull },
          { duration: settings.zoomDuration || 1000 }
        );
      } else {
        map.view.goTo(
          { target: features, zoom: 18 },
          { duration: settings.zoomDuration || 1000 }
        );
      }
    }
  }
  if(settings?.callback) settings.callback();
};

const GetSymbol = function (settings, fillColor, strokeColor) {
  let symbol;
  let highlightWidth = settings.highlightWidth || 2;
  let highlighColor = settings.highlighColor || [0, 255, 255, 1];

  let symbolOption = {
    style: "solid",
    color: fillColor,
    outline: {
      // autocasts as new SimpleLineSymbol()
      color: strokeColor,
      width: highlightWidth + "px",
    },
  };

  if (settings.isHiglightSymbol) {
    symbolOption = {
      style: "solid",
      color: highlighColor,
      outline: {
        // autocasts as new SimpleLineSymbol()
        color: highlighColor,
        width: highlightWidth + "px",
      },
    };
    symbol = new SimpleFillSymbol(symbolOption);
  } else if (settings.isDashStyle) {
    symbolOption = {
      style: "forward-diagonal",
      color: strokeColor,
      outline: {
        // autocasts as new SimpleLineSymbol()
        color: strokeColor,
        width: highlightWidth + "px",
      },
    };
    symbol = new SimpleFillSymbol(symbolOption);
  } else if (settings.isHighlighPolygonBorder) {
    symbolOption = {
      style: "none",
      color: highlighColor,
      outline: {
        // autocasts as new SimpleLineSymbol()
        color: strokeColor,
        width: highlightWidth + "px",
      },
    };
    symbol = new SimpleFillSymbol(symbolOption);
  } else {
    symbolOption = {
      style: "solid",
      color: highlighColor,
      outline: {
        // autocasts as new SimpleLineSymbol()
        color: strokeColor,
        width: highlightWidth + "px",
      },
    };
    symbol = new SimpleFillSymbol(symbolOption);
  }

  return symbol;
};

// maping field with domain
export const getFeatureDomainName = function (
  features,
  layerId,
  notReturnCode,
  domainMapUrl,
  isIdentifyResults
) {
  return getDomain(layerId, {}, domainMapUrl).then(
    function (data) {
      var codedValue = {};
      features.forEach(function (feature) {
        Object.keys(feature.attributes).forEach(function (attr) {
          let result = data.find((d) => d.name == attr);
          if (result && result.domain) {
            codedValue = result.domain.codedValues.find((d) => {
              //these next 3 lines to add coded values into attributes in case of identify
              if (
                isIdentifyResults &&
                feature.attributes[attr] &&
                !parseInt(feature.attributes[attr])
              ) {
                if (d.name === feature.attributes[attr])
                  feature.attributes[attr] = d.code;
              }
              return d.code == feature.attributes[attr];
            });
            if (!codedValue) {
              if (!isNaN(feature.attributes[attr])) {
                codedValue = result.domain.codedValues.find(
                  (d) => d.code == +feature.attributes[attr]
                );
              }
            }
            if (codedValue && codedValue.name) {
              if (!notReturnCode)
                feature.attributes[attr + "_Code"] = feature.attributes[attr];
              feature.attributes[attr] = codedValue.name;
            }
          }
        });
      });
      return features;
    },
    function (error) {
      return;
    }
  );
};

let mapViewerTempObj = {};

const getDomain = function (layerId, settings, domainMapUrl) {
  return new Promise((resolve, reject) => {
    let serv = mapViewerTempObj;
    let loadings = [];
    var returnedDomain;

    if (serv.Domains && serv.Domains[layerId]) {
      const domain = serv.Domains[layerId];
      if (!settings.fieldName && !settings.code) {
        domain.fields.forEach(function (val) {
          if (!val.domain) {
            settings.fieldName = val.name;
            settings.isSubType = true;
            if (domain.types) {
              returnedDomain = getSubTypes(domain, settings);

              if (returnedDomain) {
                if (settings.isfilterOpened) val.domain = returnedDomain;
                else val.domain = { codedValues: returnedDomain };
              } else val.domain = null;
            }
          }
        });
        returnedDomain = domain.fields;
      } else if (settings.isSubType && settings.fieldName) {
        returnedDomain = getSubTypes(domain, settings);
      } else {
        domain.fields.forEach(function (field) {
          if (field.name == settings.fieldName && field.domain) {
            returnedDomain = field.domain.codedValues;
          }
        });
      }
    }

    if (returnedDomain) {
      resolve(returnedDomain);
      return;
    } else {
      var url = (domainMapUrl || window.mapUrl) + "/" + layerId;
      let token = "";
      if (window.esriToken) {
        token = "&token=" + window.esriToken;
      }
      if (loadings.indexOf(url) == -1) {
        loadings.push(url);
        getFromEsriRequest(url + "?f=pjson" + token).then(
          (res) => {
            serv.Domains = serv.Domains || [];
            mapViewerTempObj.Domains[layerId] = {
              fields: res.fields,
              types: res.types,
            };
            loadings.pop(url);
            getDomain(layerId, settings, domainMapUrl).then(
              (data) => {
                resolve(data);
                return;
              },
              function () {}
            );
          },
          function () {
            loadings.pop(url);
          }
        );
      } else {
        return reject();
      }
    }
  });
};

const getSubTypes = function (domain, settings) {
  var returnedDomain = [];
  if (domain.types) {
    domain.types.forEach(function (subType) {
      if (settings.isSubType && !settings.code) {
        if (!returnedDomain) returnedDomain = [];

        if (subType.domains[settings.fieldName]) {
          if (settings.isfilterOpened)
            returnedDomain.push({
              id: subType.id,
              name: subType.name,
              isSubType: true,
            });
          else
            returnedDomain.push.apply(
              returnedDomain,
              subType.domains[settings.fieldName].codedValues
            );
        }
      } else {
        if (
          subType.id == settings.code &&
          subType.domains[settings.fieldName]
        ) {
          returnedDomain = subType.domains[settings.fieldName].codedValues;
        }
      }
    });
  }

  return returnedDomain.length == 0 ? null : returnedDomain;
};

export const makeIdentify = function (
  mapView,
  mapPoint,
  layerIds,
  tolerance,
  layerOption
) {
  // Set the parameters for the identify
  let params = new IdentifyParameters();
  params.tolerance = tolerance || 3;
  params.layerIds = layerIds || [];
  params.sublayers = layerIds ? [{ id: layerIds[0] }] : []; //added line for fix esri layerIds bug
  params.layerOption = layerOption || "visible"; //"top"|"visible"|"all"
  params.width = mapView.width;
  params.height = mapView.height;
  params.geometry = mapPoint;
  params.mapExtent = mapView.extent;
  params.returnFieldName = true;
  params.returnGeometry = true;

  return identify.identify(window.mapUrl, params);
};

export const getLayerId = function (mapInfo, layerName) {
  let findLayer = mapInfo.info.$layers.layers.find((x) => x.name == layerName);
  if (!findLayer) {
    findLayer = mapInfo.info.$layers.tables.find((x) => x.name == layerName);
  }
  return findLayer && findLayer.id;
};
export const getLayerInfo = function (mapInfo, layerName) {
  let findLayer = mapInfo.info.$layers.layers.find((x) => x.name == layerName);
  if (!findLayer) {
    findLayer = mapInfo.info.$layers.tables.find((x) => x.name == layerName);
  }
  return findLayer;
};



export const isLayerExist = function (mapInfo, layerName) {
  let findLayer = mapInfo.info.$layers.layers.find((x) => x.name == layerName);
  if (!findLayer) {
    findLayer = mapInfo.info.$layers.tables.find((x) => x.name == layerName);
  }
  return findLayer;
};

export const convertHirjiDateToTimeSpan = function (time, isEndDate) {
  //CREATED_DATE >= TIMESTAMP '2021-08-09 00:00:00' and CREATED_DATE <= TIMESTAMP '2021-11-05 23:59:59'
  let timeStamp = moment(time + " 00:00", "iYYYY/iM/iD HH:mm").format(
    "YYYY-M-D HH:mm:ss"
  );

  if (isEndDate) {
    timeStamp = moment(time + " 23:59:59", "iYYYY/iM/iD HH:mm:ss").format(
      "YYYY-M-D HH:mm:ss"
    );
  }
  return " TIMESTAMP '" + timeStamp + "'";
};
export const convertDateToTimeStamp = (time, isEndDate) => {
  //CREATED_DATE >= TIMESTAMP '2021-08-09 00:00:00' and CREATED_DATE <= TIMESTAMP '2021-11-05 23:59:59'
  let timeStamp =
    time.getUTCFullYear() +
    "-" +
    (time.getUTCMonth() + 1) +
    "-" +
    time.getUTCDate();
  if (!isEndDate) {
    timeStamp = timeStamp + " 00:00:00";
  } else timeStamp = timeStamp + " 23:59:59";
  return " TIMESTAMP '" + timeStamp + "'";
};
export const showLoading = function (showLoading) {
  console.log("setloading");
  window.__showLoadingItems = window.__showLoadingItems || [];

  if (showLoading) window.__showLoadingItems.push(showLoading);
  else window.__showLoadingItems.pop();

  const customEvent = new CustomEvent("showLoading", { detail: showLoading });
  document.dispatchEvent(customEvent);
};

export const showGeneralDataTable = function (param) {
  const customEvent = new CustomEvent("showGeneralDataTable", {
    detail: param,
  });
  document.dispatchEvent(customEvent);
};

export const clearGraphicLayer = (layerName, map) => {
  let graphicLayer = map.findLayerById(layerName);

  if (graphicLayer && graphicLayer.removeAll) graphicLayer.removeAll();
};

export const IsNotNull = (value) => {
  value = value + "";
  return (
    value && value.toLowerCase() != "null" && value != "" && value != "nan"
  );
};

export const clearGraphics = (graphicLayerName, map) => {
  if (typeof graphicLayerName === "string") {
    let layerOnMap = map.findLayerById(graphicLayerName);
    if (layerOnMap) layerOnMap.removeAll();
  } else if (graphicLayerName) {
    graphicLayerName.forEach((gr) => {
      let layerOnMap = map.findLayerById(gr);
      if (layerOnMap) layerOnMap.removeAll();
    });
  }
};
export const executeGPTool = async (
  gpUrl,
  params,
  callback,
  callbackError,
  outputName = "output_value",
  consumeType = 'submitJob',
  token
) => {
  try {
    // register token to enable access the gp tool with token with each request
    if(token)
    IdentityManager.registerToken({
      token: token,
      server: gpUrl
  });
    let result;
    if(consumeType === 'submitJob') {
      let jobInfo = await geoprocessor.submitJob(gpUrl, params);
      console.log(jobInfo)
      result = await jobInfo.waitForJobCompletion();
      result = await result.fetchResultData(outputName);
    }else {
      result = await geoprocessor.execute(gpUrl, params);
      result = result.results[0];
    }
    if (result.value) callback(result.value);
    else callback("");
  } catch (err) {
    callbackError(err);
  }
};

export const localizeNumber = (input) => {
  // if (($translate.use() == "ar") && input != null) {

  var newValue = "";
  var tempStr = String(input);
  // //
  var engStart = "0".charCodeAt(),
    arStart = "٠".charCodeAt();
  var diff = arStart - engStart;
  for (var i = 0, n = tempStr.length; i < n; i++) {
    var ch = tempStr.charCodeAt(i);
    if (ch >= engStart && ch <= engStart + 9) {
      newValue = newValue + String.fromCharCode(ch + diff);
    } else if (tempStr[i] == "/") {
      var pre =
        tempStr.charCodeAt(i - 1) >= engStart &&
        tempStr.charCodeAt(i - 1) <= engStart + 9;
      if (i != n - 1) {
        var post =
          tempStr.charCodeAt(i + 1) >= engStart &&
          tempStr.charCodeAt(i + 1) <= engStart + 9;
      }
      if (pre && post) newValue += " / ";
      else newValue += String.fromCharCode(ch);
    } else {
      newValue = newValue + String.fromCharCode(ch);
    }
  }
  // } else {
  //     newValue = input;
  // }
  return newValue;
};

export const extractNumbers = (expr, num, isReverse) => {
  let dates = [];
  //num = localizeNumber(num);
  dates = num.toString().match(expr); // .replaceAll(" ", "")

  if (dates && Array.isArray(dates)) {
    dates.forEach((date) => {
      num =
        (!isReverse &&
          num.toString().replace(
            date,
            date
              .toString()
              ?.replace(/\d/g, (d) => "٠١٢٣٤٥٦٧٨٩"[d])
              ?.replaceAll(".", ",")
          )) ||
        num
          .toString()
          .replace(
            date,
            localizeNumber(date.toString().trim())
              ?.replaceAll(".", ",")
              ?.replaceAll(" ", "")
              .trim()
              .split("/")
              .reverse()
              .join("/")
              .trim()
          );
    });
  }

  return num;
};
export const convertToArabic = (num) => {
  if (num) {
    num = extractNumbers(/(\d{4})\/(\d{1,2})\/(\d{1,2})/g, num, false);
    num = extractNumbers(/(\d{1,2})\/(\d{1,2})\/(\d{4})/g, num, true);
    num = extractNumbers(/(\d+\/?)+/g, num, true);
    return (
      (num.toString().trim().indexOf("/") == -1 &&
        localizeNumber(num.toString().trim())?.replaceAll(".", ",")) ||
      num?.toString()?.trim()
    );
    // );
  } else {
    return num != undefined && num != null && num.toString() == "0"
      ? "0.00"
          .toString()
          ?.replace(/\d/g, (d) => "٠١٢٣٤٥٦٧٨٩"[d])
          ?.replaceAll(".", ",")
      : "";
  }
};
export const convertToEnglish = (string) => {
  return ((string || "") + "")
    .replace(/[\u0660-\u0669]/g, function (c) {
      return c.charCodeAt(0) - 0x0660;
    })
    .replace(/[\u06f0-\u06f9]/g, function (c) {
      return c.charCodeAt(0) - 0x06f0;
    });
};

//for report map
export const highlightFeatureForReportMap = function (feature, map, settings) {
  // layerName, isZoom, fillColor, strokeColor, isDashStyle, isHighlighPolygonBorder, callback, highlightWidth,zoomFactor) {
  if (feature) {
    if (settings && !settings.isSavePreviosZoom) window.extent = undefined;

    
      
        let symbol;
        let graphicLayer = map.getLayer(settings.layerName);

        if (!settings.noclear) graphicLayer.clear();

        // let highlightWidth = settings.highlightWidth || 3
        let fillColor = settings.fillColor || "black";
        let strokeColor = settings.strokeColor || "black";
        let highlighColor = settings.highlighColor || [0, 255, 255];
        let Color = window.dojo.Color;

        function highlightGeometry(feature) {
          if (feature.geometry) {
            if (feature.geometry.type == "polygon") {
              feature.geometry = new Polygon(feature.geometry);
              if (settings.isGetCenter) {
                feature.geometry = feature.geometry.getExtent().getCenter();
              }
            } else if (feature.geometry.type == "point") {
              feature.geometry = new Point(feature.geometry);
            }

            var graphic;

            if (feature.geometry.type === "point") {
              if (settings.isHiglightSymbol) {
                strokeColor = highlighColor;
                fillColor = settings.fillColor || highlighColor;
              }

              //settings.zoomFactor = 50
              symbol = new SimpleMarkerSymbol(
                SimpleMarkerSymbol.STYLE_CIRCLE,
                28,
                new SimpleLineSymbol(
                  SimpleLineSymbol.STYLE_SOLID,
                  new Color(strokeColor),
                  2
                ),
                new Color([0, 0, 0, 0.2])
              );

              if (settings.isInvest) {
                symbol = new PictureMarkerSymbol({
                  angle: 0,
                  xoffset: 0,
                  yoffset: 0,
                  type: "esriPMS",
                  url: "./images/noty.svg",
                  contentType: "image/png",
                  width: 40,
                  height: 40,
                });
              } else if (settings.isInvestPoint) {
                symbol = new PictureMarkerSymbol({
                  angle: 0,
                  xoffset: 0,
                  yoffset: 0,
                  type: "esriPMS",
                  url: "./images/invest_point.svg",
                  contentType: "image/png",
                  width: 40,
                  height: 40,
                });
              } else if (settings.isLocation) {
                console.log("1");
                symbol = new PictureMarkerSymbol({
                  angle: 0,
                  xoffset: 0,
                  yoffset: 0,
                  type: "esriPMS",
                  url: "./images/marker2.png",
                  contentType: "image/png",
                  width: 40,
                  height: 40,
                });
              }
            } else {
              symbol = GetSymbol(
                settings,
                settings.fillColor || fillColor,
                strokeColor,
                SimpleFillSymbol,
                SimpleLineSymbol,
                PictureMarkerSymbol
              );
            }
            graphic = new Graphic(feature.geometry, symbol, settings.attr);
          } else {
            if (feature.type === "point") {
              if (settings.isHiglightSymbol) {
                strokeColor = highlighColor;
                fillColor = settings.fillColor || highlighColor;
              }
              settings.zoomFactor = 50;
              symbol = new SimpleMarkerSymbol(
                SimpleMarkerSymbol.STYLE_CIRCLE,
                28,
                new SimpleLineSymbol(
                  SimpleLineSymbol.STYLE_SOLID,
                  new Color(strokeColor),
                  2
                ),
                new Color(fillColor)
              );

              if (settings.isLocation) {
                console.log("2");
                symbol = new PictureMarkerSymbol({
                  angle: 0,
                  xoffset: 0,
                  yoffset: 0,
                  type: "esriPMS",
                  url: "./images/marker2.png",
                  contentType: "image/png",
                  width: 40,
                  height: 40,
                });
              }
            } else if (feature.type === "polyline") {
              symbol = new SimpleLineSymbol(
                SimpleLineSymbol.STYLE_SOLID,
                new Color(fillColor),
                7
              );
            } else {
              symbol = GetSymbol(
                settings,
                settings.fillColor || fillColor,
                strokeColor,
                SimpleFillSymbol,
                SimpleLineSymbol,
                PictureMarkerSymbol
              );
            }
            graphic = new Graphic(feature, symbol, settings.attr, null);
          }
          graphicLayer.add(graphic);

          if (!settings.listOfFeatures && settings.isZoom) {
            if (!feature.length) {
              zoomToFeature(
                [feature],
                map,
                settings.zoomFactor || 150,
                settings.callback
              );
            } else {
              zoomToFeature(
                feature,
                map,
                settings.zoomFactor || 150,
                settings.callback
              );
            }
          }

          graphicLayer.redraw();
        }
        if (feature && !feature.length) {
          if (feature.geometry || feature.type) {
            highlightGeometry(feature);
          }
        } else {
          if (
            feature &&
            feature[0] &&
            feature[0].geometry &&
            feature[0].geometry.type === "point"
          ) {
            if (settings.isHiglightSymbol) {
              strokeColor = highlighColor;
              fillColor = settings.fillColor || highlighColor;
            }
            settings.zoomFactor = 50;
            symbol = new SimpleMarkerSymbol(
              SimpleMarkerSymbol.STYLE_CIRCLE,
              10,
              new SimpleLineSymbol(
                SimpleLineSymbol.STYLE_SOLID,
                new Color(strokeColor),
                2
              ),
              new Color(fillColor)
            );
          } else {
            symbol = GetSymbol(
              settings,
              settings.fillColor || fillColor,
              strokeColor,
              SimpleFillSymbol,
              SimpleLineSymbol,
              PictureMarkerSymbol
            );
          }

          feature.forEach(function (elem) {
            if (elem.geometry) {
              if (elem.geometry.type == "polygon") {
                elem.geometry = new Polygon(elem.geometry);
                if (settings.isGetCenter) {
                  elem.geometry = elem.geometry.getExtent().getCenter();
                }
              }

              if (settings.isInvest) {
                symbol = new PictureMarkerSymbol({
                  angle: 0,
                  xoffset: 0,
                  yoffset: 0,
                  type: "esriPMS",
                  url: "./images/noty.svg",
                  contentType: "image/png",
                  width: 40,
                  height: 40,
                });
              } else if (settings.isInvestPoint) {
                symbol = new PictureMarkerSymbol({
                  angle: 0,
                  xoffset: 0,
                  yoffset: 0,
                  type: "esriPMS",
                  url: "./images/invest_point.svg",
                  contentType: "image/png",
                  width: 40,
                  height: 40,
                });
              } else if (settings.isLocation) {
                symbol = new PictureMarkerSymbol({
                  angle: 0,
                  xoffset: 0,
                  yoffset: 0,
                  type: "esriPMS",
                  url: "./images/marker2.png",
                  contentType: "image/png",
                  width: 40,
                  height: 40,
                });
              }

              var graphic = new Graphic(
                elem.geometry,
                symbol,
                settings.attr,
                null
              );
              graphicLayer.add(graphic);
            } else if (elem.type == "point") {
              if (settings.isInvest) {
                symbol = new PictureMarkerSymbol({
                  angle: 0,
                  xoffset: 0,
                  yoffset: 0,
                  type: "esriPMS",
                  url: "./images/noty.svg",
                  contentType: "image/png",
                  width: 40,
                  height: 40,
                });
              } else if (settings.isInvestPoint) {
                symbol = new PictureMarkerSymbol({
                  angle: 0,
                  xoffset: 0,
                  yoffset: 0,
                  type: "esriPMS",
                  url: "./images/invest_point.svg",
                  contentType: "image/png",
                  width: 40,
                  height: 40,
                });
              } else if (settings.isLocation) {
                symbol = new PictureMarkerSymbol({
                  angle: 0,
                  xoffset: 0,
                  yoffset: 0,
                  type: "esriPMS",
                  url: "./images/marker2.png",
                  contentType: "image/png",
                  width: 40,
                  height: 40,
                });
              }

              graphic = new Graphic(elem, symbol, settings.attr, null);
              graphicLayer.add(graphic);
            }
          });

          if (settings.isZoom) {
            if (!feature.length) {
              zoomToFeature(
                [feature],
                map,
                settings.zoomFactor || 150,
                settings.callback
              );
            } else {
              zoomToFeature(
                feature,
                map,
                settings.zoomFactor || 150,
                settings.callback
              );
            }
          }
          graphicLayer.redraw();
        }
      }
    
  
};


// export const convertToArabic = (num) => {
//   if (num) {
//     let dates = [];
//     //num = localizeNumber(num);
//     dates =
//       num
//         .toString()
//         .replaceAll(" ", "")
//         .match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/) ||
//       num
//         .toString()
//         .replaceAll(" ", "")
//         .match(/^(\d+)\/(\d{4})$/);

//     if (dates && Array.isArray(dates)) {
//       dates.forEach((date) => {
//         num = num
//           .toString()
//           .replaceAll(date, date.split("/").reverse().join("/"));
//       });
//     } else {
//       dates =
//         num
//           .toString()
//           .replaceAll(" ", "")
//           .match(/^(\d{4})\/(\d{1,2})\/(\d{1,2})$/) ||
//         num
//           .toString()
//           .replaceAll(" ", "")
//           .match(/^(\d{4})\/(\d+)$/);
//       if (dates && Array.isArray(dates)) {
//         dates.forEach((date) => {
//           num = num.toString().replaceAll(date, date.split("/").join("/"));
//         });
//       }
//     }

//     let result = localizeNumber(num.toString().trim())?.replaceAll(".", ",");
//     return (
//       (result.trim().indexOf("/") != -1 &&
//         result.trim().split("/").reverse().join("/").trim()) ||
//       result.trim()
//     );
//   } else {
//     return num != undefined && num != null && num.toString() == "0"
//       ? "0.00"
//           .toString()
//           ?.replace(/\d/g, (d) => "٠١٢٣٤٥٦٧٨٩"[d])
//           ?.replaceAll(".", ",")
//       : "";
//   }
// };
