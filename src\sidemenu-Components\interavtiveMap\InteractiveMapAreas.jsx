import { Form, Button, Input, message, Modal, Select } from "antd";
import { useTranslation } from "react-i18next";
import edit_icon from "../../assets/images/sidemenu/edit.svg";
import interactive_edit from "../../assets/icons/interactive_edit.svg";
import delete_icon from "../../assets/icons/Trash Bin 2.svg";
import { RiArrowDropDownFill } from "react-icons/ri";
import { useEffect, useState } from "react";
import axios from "axios";
import Extent from "@arcgis/core/geometry/Extent";

export default function InteractiveMapAreas(props) {
  const initialFormState = { zoneName: "", ediZoneName: "" };
  const [formValues, setFormValues] = useState(initialFormState);

  const { t } = useTranslation("common");

  const [selectedRegion, setSelectedRegion] = useState(undefined);
  const [zonesList, setZonesList] = useState([]);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [activeEditName, setActiveEditName] = useState();
  const [activeDeleteName, setActiveDeleteName] = useState();
  const [deleteModalVisible, setdeleteModalVisible] = useState(false);
  const [regionsData, setRegionsData] = useState([]);

  const getAllRegions = async () => {
    await axios
      .get(`${window.ApiUrl}/StudyRegion/GetAll?pageSize=100`)
      .then((response) => {
        let regions = response.data.results.map((region) => {
          return {
            id: region.id,
            name: region.name,
            zones: region.zones,
          };
        });
        setRegionsData([...regions]);
      })
      .catch((error) => {
        message.warning(t("ErrorRequest"));
      });
  };

  useEffect(() => {
    getAllRegions();
  }, []);
  const handleSelectChange = (e) => {
    setSelectedRegion(e);
    if (e) {
      let selectedRegion = regionsData.find((region) => region.name == e);
      setZonesList(() => {
        if (selectedRegion.zones) {
          return [...selectedRegion.zones];
        } else {
          return [];
        }
      });
    } else {
      setZonesList([]);
    }
  };

  const handleChangeInput = (e) => {
    setFormValues({ ...formValues, [e.target.name]: e.target.value });
  };

  ///// add new zone ////

  const postZone = async (regionId) => {
    try {
      const postUrl = `${window.ApiUrl}/StudyZone`;
      const data = {
        name: formValues.zoneName,
        region_id: regionId,
        extent: JSON.stringify(props.map.view.extent.toJSON()),
      };
      const response = await axios.post(postUrl, data);
      setZonesList((prevState) => [{ ...response.data }, ...prevState]);

      let regions = [...regionsData];
      let regionToEdit = regions.find((region) => region.id == regionId);
      regionToEdit.zones = [...zonesList, { ...response.data }];
      setRegionsData(regions);
      return { success: true };
    } catch (error) {
      return { success: false };
    }
  };

  const onSubmitZone = async (e) => {
    if (!selectedRegion) {
      message.warning(t("regionMustBeSelected"));
    } else if (formValues.zoneName.trim().length === 0) {
      message.warning(t("emptyZoneName"));
    } else if (zonesList.some((zone) => zone.name == formValues.zoneName)) {
      message.warning(t("newZoneNameRequired"));
    } else {
      const region = regionsData.find(
        (region) => region.name == selectedRegion
      );
      const result = await postZone(region.id);
      if (!result.success) {
        message.warning(t("ErrorRequest"));
      } else {
        message.warning(t("zoneNamedAddSuccessfully"));
        setFormValues(initialFormState);
      }
    }
  };

  //// edit zone name ///

  const editZone = async (zoneToEdit) => {
    try {
      const putUrl = `${window.ApiUrl}/StudyZone/${zoneToEdit.id}`;
      await axios.put(putUrl, {
        name: zoneToEdit.name,
        region_id: zoneToEdit.region_id,
        extent: zoneToEdit.extent,
      });
      return { success: true };
    } catch (error) {
      return { success: false };
    }
  };

  const showEdit = (zoneName) => {
    setActiveEditName(zoneName);
    setEditModalVisible(true);
  };

  const SubmitEditName = async (e) => {
    let otherZoneNames = zonesList
      .filter((zone) => zone.name != activeEditName)
      .map((zone) => zone.name);
    if (
      formValues.ediZoneName == undefined ||
      formValues.ediZoneName.trim().length == 0
    ) {
      message.warning(t("emptyZoneName"));
    } else if (otherZoneNames.includes(formValues.ediZoneName)) {
      message.warning(t("newZoneNameRequired"));
    } else if (formValues.ediZoneName == activeEditName) {
      message.warning(t("SameOldNewZoneName"));
    } else {
      let zoneToEdit = zonesList.find((zone) => zone.name == activeEditName);
      zoneToEdit.name = formValues.ediZoneName;
      const result = await editZone(zoneToEdit);
      if (!result.success) {
        message.warning(t("ErrorRequest"));
      } else {
        const otherZones = zonesList.filter(
          (zone) => zone.name != activeEditName
        );
        setZonesList([...otherZones]);
        zoneEditedSuccessfully();
      }
    }
  };

  const zoneEditedSuccessfully = () => {
    setEditModalVisible(false);
    setFormValues(initialFormState);
    message.warning(t("zoneNamedEditSuccessfully"));
  };

  //// delete region ////

  const deleteZone = async (zoneId) => {
    try {
      const delteUrl = `${window.ApiUrl}/StudyZone/${zoneId}`;
      await axios.delete(delteUrl);
      return { success: true };
    } catch (error) {
      return { success: false };
    }
  };

  const showDelete = (zoneName) => {
    const zoneToDelete = zonesList.find((zone) => zone.name == zoneName);

    if (zoneToDelete.has_drawings) {
      message.warning(t("CannotDeleteZone"));
    } else {
      setActiveDeleteName(zoneName);
      setdeleteModalVisible(true);
    }
  };

  const submitDeleteDrawing = async () => {
    const zoneToDelete = zonesList.find(
      (zone) => zone.name == activeDeleteName
    );

    const result = await deleteZone(zoneToDelete.id);
    if (!result.success) {
      message.warning(t("ErrorRequest"));
    } else {
      const otherZones = zonesList.filter((zone) => zone.id != zoneToDelete.id);
      setZonesList(otherZones);
      zoneDeletedSuccessfully();
    }
  };

  const zoneDeletedSuccessfully = () => {
    setActiveDeleteName();
    setdeleteModalVisible(false);
    message.warning(t("zoneDeletedSuccessfully"));
  };

  const handleZoneSelection = (zone) => {
    if (zone.extent) {
      props.map.view.extent = Extent.fromJSON(JSON.parse(zone.extent));
    }
  };

  return (
    <>
      <>
        <div
          style={{
            display: "flex",
            gap: "10px",
            flexDirection: "column",
            marginTop: "10px",
          }}
        >
          {/* <div style={{ display: "grid" }}>
            <label style={{ textAlign: "start" }}>
              {t("area_name", { ns: "sidemenu" })}
            </label>
            <Input
              className="searchInput"
              placeholder={t("area_name", { ns: "sidemenu" })}
              value={formValues.zoneName}
              maxLength={50}
              name="zoneName"
              onChange={handleChangeInput}
            />
          </div> */}
          <div className="map_area">
            <Form.Item
              label={t("area_name", { ns: "sidemenu" })}
              className="select-cust"
              style={{ padding: "0px 6px" }}
            >
              <Input
                type="text"
                //  onKeyPress={e=>setFormValues({ ...formValues,bookmark: convertToArabic(e.target.value) })}
                // onBlur={(e) => {
                //   setFormValues({ ...formValues, [e.target.name]: convertToArabic(e.target.value) });
                // }}
                placeholder={t("area_name", { ns: "sidemenu" })}
                value={formValues.zoneName}
                onChange={handleChangeInput}
                name="zoneName"
                style={{ marginTop: "-10px" }}
              />
              {/* {console.log(convertToArabic(formValues.bookmark))} */}
            </Form.Item>
          </div>
          {/* <div style={{ display: "grid" }}>
            <label style={{ textAlign: "start" }}>
              {" "}
              {t("study_areas", { ns: "sidemenu" })}
            </label>
            <Select
              virtual={false}
              // suffixIcon={<DownCircleFilled />}
              value={selectedRegion}
              onChange={handleSelectChange}
              suffixIcon={<RiArrowDropDownFill size={30} />}
              showSearch
              allowClear
              className="dont-show"
              placeholder={t("choose_study_area", { ns: "sidemenu" })}
              optionFilterProp="v"
            >
              {regionsData.map((region) => {
                return (
                  <Select.Option
                    value={region.name}
                    id={region.id}
                    key={region.id}
                  >
                    {region.name}
                  </Select.Option>
                );
              })}
            </Select>
          </div> */}
          <div className="select-cust select-cust_mapArea">
            <label className="selectLabelStyle">
              {" "}
              {t("study_areas", { ns: "sidemenu" })}
            </label>
            <Select
              virtual={false}
              suffixIcon={<RiArrowDropDownFill size={30} />}
              className="searchInput englishFont compareLayers-select"
              showSearch
              placeholder={t("choose_study_area", { ns: "sidemenu" })}
              value={selectedRegion}
              onChange={handleSelectChange}
              getPopupContainer={(trigger) => trigger.parentNode}
              optionFilterProp="v"
              filterOption={(input, option) => option.v.indexOf(input) >= 0}
            >
              {regionsData.map((region) => {
                return (
                  <Select.Option
                    value={region.name}
                    id={region.id}
                    key={region.id}
                  >
                    {region.name}
                  </Select.Option>
                );
              })}
            </Select>
          </div>
        </div>

        <button className="SearchBtn mt-3" size="large" onClick={onSubmitZone}>
          {t("add", { ns: "sidemenu" })}
        </button>

        {zonesList.length > 0 &&
          zonesList.map((zone, indx) => {
            return (
              <div
                key={indx}
                className="generalSearchCard"
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  padding: "16px",
                  borderRadius: "16px",
                  background: "#FFFFFF99",
                }}
                onClick={() => handleZoneSelection(zone)}
              >
                <div className="interactive_drawing_label"> {zone.name} </div>
                <div style={{ display: "flex", gap: "10px" }}>
                  <img
                    src={interactive_edit}
                    alt="edit icon"
                    onClick={() => {
                      showEdit(zone.name);
                    }}
                  />
                  <img
                    src={delete_icon}
                    alt="delete icon"
                    onClick={() => showDelete(zone.name)}
                  />
                </div>
              </div>
            );
          })}
      </>
      <>
        <Modal
          title={t("editZoneName") + ": " + activeEditName}
          centered
          visible={editModalVisible}
          onCancel={() => setEditModalVisible(false)}
          okText={t("edit")}
          cancelText={t("cancel")}
        >
          <Input
            name="ediZoneName"
            onChange={handleChangeInput}
            value={formValues.ediZoneName}
            maxLength={50}
            placeholder={t("area_name", { ns: "sidemenu" })}
          />

          <div
            style={{
              display: "flex",
              gap: "10px",
              marginTop: "10px",
              justifyContent: "end",
            }}
          >
            <Button
              type="primary"
              style={{
                backgroundColor: "#b45333",
              }}
              onClick={() => {
                SubmitEditName();
              }}
            >
              {t("edit")}
            </Button>

            <Button
              type="primary"
              style={{
                backgroundColor: "#b45333",
              }}
              onClick={() => setEditModalVisible(false)}
            >
              {t("close")}
            </Button>
          </div>
        </Modal>

        <Modal
          title={t("deleteZoneConfirmation") + activeDeleteName}
          centered
          visible={deleteModalVisible}
          //onOk={() => afterEditModal()}
          onCancel={() => setdeleteModalVisible(false)}
          okText={t("yes")}
          cancelText={t("no")}
        >
          <div
            style={{
              display: "flex",
              gap: "10px",
              marginTop: "10px",
              justifyContent: "end",
            }}
          >
            <Button
              type="primary"
              style={{
                backgroundColor: "#b45333",
              }}
              onClick={() => {
                submitDeleteDrawing();
              }}
            >
              {t("yes")}
            </Button>

            <Button
              type="primary"
              style={{
                backgroundColor: "#b45333",
              }}
              onClick={() => setdeleteModalVisible(false)}
            >
              {t("no")}
            </Button>
          </div>
        </Modal>
      </>
    </>
  );
}
