import React from "react";
import axios from "axios";
import { useTranslation } from "react-i18next";

import { LoaderContext } from "../../../Contexts/LoaderContext";
import { getLayerId, queryTask } from "../../../helper/common_func";
import {
  DISTRICT_DATA_LAYER_NAME,
  PARCEL_LANDS_LAYER_NAME,
  PLAN_DATA_LAYER_NAME,
  plannedLandsGroupsIDs,
  plannedLandsGroupsIDsForDistrict,
} from "../../../helper/constants";

import {
  notificationMessage,
} from "../../../helper/utilsFunc";

import DistrictLandStatisticsModal from "./DistrictLandsStatisticsModal";

function DistrictDataModal({
  map,
  open,
  closeModal,
  districtData,
  landbaseParcelData,
  userGroups

}) {
  const [t] = useTranslation("common");
  const { setIsLoading } = React.useContext(LoaderContext);
  const [shownData, setShownData] = React.useState([]);
  const orderLandsInTbl = {
    plannedLands: 0,
    usedLands: 1,
    nonUsedLands: 2,
    resedential: 3,
    commercial: 4,
    private: 5,
    royal: 6,
    sales: 7,
    generalServices: 8,
    assets: 9,
    invest: 10,
    licensed: 11,
    withElectric: 12,
    farz: 13,
  };
  const landsColors = {
    resedential: "rgba(255, 255, 175, 1)",
    commercial: "rgba(255,127,127,1)",
    assets: "rgba(255,225,225,1)",
    generalServices: "rgba(141, 191, 230, 1)",
    usedLands: "rgba(0, 145, 65, 1)",
    nonUsedLands: "rgb(163, 160, 0)",
    sales: "rgba(66, 255, 0, 1)",
    royal: "rgba(66, 183, 0, 1)",
    private: "rgba(54, 90, 58, 1)",
    invest: "rgba(201, 42, 42, 1)",
    plannedLands: "rgb(60, 60, 60)",
    licensed: "rgb(1, 34, 83)",
    withElectric: "rgb(2, 37, 23)",
    farz: "rgb(158, 6, 6)",
  };
  React.useEffect(() => {
    if (map) {
      //todo --> parse data to required format
      getDistrictLandsData();
    }
    return () => {
      setShownData();
    };
  }, []);
  const getDistrictLandsData = async () => {
    setIsLoading(true);
    try {
      // let districtNumbersWithGovernmentClass = await new Promise(
      //   (resolve, reject) => {
      //     let layerID = getLayerId(map.__mapInfo, DISTRICT_DATA_LAYER_NAME);
      //     let queryParams = {
      //       url: window.mapUrl + "/" + layerID,
      //       notShowLoading: true,
      //       returnGeometry: false,
      //       where: `1=1`,
      //       outFields: "DISTRICT_NAME",
      //     };
      //     queryTask({
      //       ...queryParams,
      //       callbackResult: ({ features }) => {
      //         let planNos =
      //           features
      //             ?.filter((item) => item.attributes.PLAN_NO)
      //             .map((item) => item.attributes.PLAN_NO) || [];
      //         resolve(planNos);
      //       },
      //       callbackError: (err) => {
      //         notificationMessage(t("common:retrievError"), 4);
      //         reject(err);
      //       },
      //     });
      //   }
      // );
      let userGroupIds = userGroups.map(gr=>gr.id);
      let isCurrentUserAuthToRpyalGovSalesLands = userGroupIds.find (grID => plannedLandsGroupsIDsForDistrict.includes(grID));
      let promises = [];

      //total lands [الاراضي المخططة] order = 0
      promises.push(
        new Promise((resolve, reject) => {
          let layerID = getLayerId(map.__mapInfo, PARCEL_LANDS_LAYER_NAME);
          let queryParams = {
            url: window.mapUrl + "/" + layerID,
            notShowLoading: true,
            returnGeometry: false,
            where: `DISTRICT_NAME =${districtData.DISTRICT_NAME} AND MUNICIPALITY_NAME=${districtData.MUNICIPALITY_NAME}`,
            statistics: [
              {
                type: "count",
                field: "USING_SYMBOL",
                name: "count",
              },
            ],
            //   groupByFields:["USING_SYMBOL"]
          };
          queryTask({
            ...queryParams,
            callbackResult: ({ features }) => {
              resolve([
                {
                  label: t("common:plannedLandNum"),
                  value: features[0]?.attributes?.COUNT || 0,
                  whereClause: `DISTRICT_NAME =${districtData.DISTRICT_NAME} AND MUNICIPALITY_NAME=${districtData.MUNICIPALITY_NAME}`,
                  id: 0,
                  color: landsColors.plannedLands,
                  dontShowOnChart: true,
                  order: orderLandsInTbl["plannedLands"],
                  type: "plannedLands",
                },
              ]);
            },
            callbackError: (err) => {
              notificationMessage(t("common:retrievError"), 4);

              reject(err);
            },
          });
        })
      );
      //resident lands [الاراضي السكنية] order 3
      promises.push(
        new Promise((resolve, reject) => {
          let layerID = getLayerId(map.__mapInfo, PARCEL_LANDS_LAYER_NAME);
          let queryParams = {
            url: window.mapUrl + "/" + layerID,
            notShowLoading: true,
            returnGeometry: false,
            where: `DISTRICT_NAME =${districtData.DISTRICT_NAME} AND MUNICIPALITY_NAME=${districtData.MUNICIPALITY_NAME} AND PARCEL_MAIN_LUSE IN (30,40)`, //10 resident, 30 general services, 40 marafiq
            statistics: [
              {
                type: "count",
                field: "PARCEL_MAIN_LUSE",
                name: "count",
              },
            ],
            groupByFields: ["PARCEL_MAIN_LUSE"],
          };
          queryTask({
            ...queryParams,
            callbackResult: ({ features }) => {
              let feats = features.map((i) => {
                return {
                  label:
                    i.attributes?.PARCEL_MAIN_LUSE === 30
                      ? t("common:generalServicesLandsNum")
                      : t("common:assetsLandNum"),
                  value: i?.attributes?.COUNT || 0,
                  whereClause: `DISTRICT_NAME =${districtData.DISTRICT_NAME} AND MUNICIPALITY_NAME=${districtData.MUNICIPALITY_NAME} AND PARCEL_MAIN_LUSE=${i.attributes?.PARCEL_MAIN_LUSE}`,
                  id: i.attributes?.PARCEL_MAIN_LUSE === 30 ? 2 : 3,
                  color:
                    i.attributes?.PARCEL_MAIN_LUSE === 30
                      ? landsColors.generalServices
                      : landsColors.assets,
                  type:
                    i.attributes?.PARCEL_MAIN_LUSE === 30
                      ? "generalServices"
                      : "assets",
                  order:
                    i.attributes?.PARCEL_MAIN_LUSE === 30
                      ? orderLandsInTbl["generalServices"]
                      : orderLandsInTbl["assets"],
                };
              });
              if (feats.length < 2) {
                let reqAddedFeats = [];
                ["generalServices", "assets"].forEach((type) => {
                  if (!feats.find((i) => i.type === type)) {
                    if (type === "generalServices")
                      reqAddedFeats.push({
                        label: t("common:generalServicesLandsNum"),
                        value: 0,
                        whereClause: `DISTRICT_NAME =${districtData.DISTRICT_NAME} AND MUNICIPALITY_NAME=${districtData.MUNICIPALITY_NAME} AND PARCEL_MAIN_LUSE=30`,

                        id: 2,
                        color: landsColors.generalServices,
                        type: type,
                        order: orderLandsInTbl["generalServices"],
                      });
                    else
                      reqAddedFeats.push({
                        label: t("common:assetsLandNum"),
                        value: 0,
                        whereClause: `DISTRICT_NAME =${districtData.DISTRICT_NAME} AND MUNICIPALITY_NAME=${districtData.MUNICIPALITY_NAME} AND PARCEL_MAIN_LUSE=40`,

                        id: 3,
                        color: landsColors.assets,
                        type: type,
                        order: orderLandsInTbl["assets"],
                      });
                  }
                });
                feats = [...feats, ...reqAddedFeats];
              }
              resolve(feats);
            },
            callbackError: (err) => {
              notificationMessage(t("common:retrievError"), 4);

              reject(err);
            },
          });
        })
      );
      //commercial, investment order = 3
      promises.push(
        new Promise((resolve, reject) => {
          let layerID = getLayerId(map.__mapInfo, PARCEL_LANDS_LAYER_NAME);
          let queryParams = {
            url: window.mapUrl + "/" + layerID,
            notShowLoading: true,
            returnGeometry: false,
            where: `DISTRICT_NAME =${districtData.DISTRICT_NAME} AND MUNICIPALITY_NAME=${districtData.MUNICIPALITY_NAME} AND (USING_SYMBOL LIKE 'ت%' OR USING_SYMBOL LIKE 'س%')`, //10 resident, 30 general services, 40 marafiq
            statistics: [
              {
                type: "count",
                field: "USING_SYMBOL",
                name: "count",
              },
            ],
            groupByFields: ["USING_SYMBOL"],
          };
          queryTask({
            ...queryParams,
            callbackResult: ({ features }) => {
              let feats = features.reduce((cumulative, item) => {
                if (!cumulative.length) {
                  cumulative.push(getNeededItemObj(item?.attributes));
                } else {
                  let isExistBefore = cumulative.find(
                    (i) => i.symbol === getSymbolCharacters(item?.attributes)
                  );
                  if (!isExistBefore)
                    cumulative.push(getNeededItemObj(item?.attributes));
                  else {
                    isExistBefore.value += item?.attributes?.COUNT || 0;
                  }
                }
                return cumulative;
              }, []);
              if (feats.length < 2) {
                let reqAddedFeats = [];
                ["resedential", "commercial"].forEach((type) => {
                  if (!feats.find((i) => i.type === type)) {
                    if (type === "commercial")
                      reqAddedFeats.push({
                        label: t("common:commercialLandsNum"),
                        value: 0,
                        whereClause: `DISTRICT_NAME =${districtData.DISTRICT_NAME} AND MUNICIPALITY_NAME=${districtData.MUNICIPALITY_NAME} AND USING_SYMBOL LIKE 'ت%'`,
                        symbol: "ت",
                        id: 6,
                        color: landsColors.commercial,
                        type: "commercial",
                        order: orderLandsInTbl["commercial"],
                      });
                    else
                      reqAddedFeats.push({
                        label: t("common:resedntialLandsNum"),
                        value: 0,
                        whereClause: `DISTRICT_NAME =${districtData.DISTRICT_NAME} AND MUNICIPALITY_NAME=${districtData.MUNICIPALITY_NAME} AND USING_SYMBOL LIKE 'س%'`,
                        symbol: "س",
                        id: 1,
                        color: landsColors.resedential,
                        // dontShowOnChart: true,
                        type: "resedential",
                        order: orderLandsInTbl["resedential"],
                      });
                  }
                });
                feats = [...feats, ...reqAddedFeats];
              }
              resolve(feats);
            },
            callbackError: (err) => {
              notificationMessage(t("common:retrievError"), 4);

              reject(err);
            },
          });
        })
      );
      //invest
      promises.push(
        new Promise((resolve, reject) => {
            resolve([
              {
                label: t("common:investLandsNum"),
                value: 0,
                whereClause: `DISTRICT_NAME =${districtData.DISTRICT_NAME} AND MUNICIPALITY_NAME=${districtData.MUNICIPALITY_NAME} AND PARCEL_MAIN_LUSE=20 AND USING_SYMBOL LIKE 'ت%'`,
                symbol: "ت",
                id: 5,
                type: "invest",
                order: orderLandsInTbl["invest"],
                color: landsColors.invest,
              },
            ]);
     
          let layerID = getLayerId(map.__mapInfo, PARCEL_LANDS_LAYER_NAME);
          let queryParams = {
            url: window.mapUrl + "/" + layerID,
            notShowLoading: true,
            returnGeometry: false,
            where: `DISTRICT_NAME =${districtData.DISTRICT_NAME} AND MUNICIPALITY_NAME=${districtData.MUNICIPALITY_NAME} AND PARCEL_MAIN_LUSE=20 AND USING_SYMBOL LIKE 'ت%'`, // replace PLAN_CLASS with PLAN_NO in (plan no with class = 2)
            statistics: [
              {
                type: "count",
                field: "USING_SYMBOL",
                name: "count",
              },
            ],
            //   groupByFields:["USING_SYMBOL"]
          };
          queryTask({
            ...queryParams,
            callbackResult: ({ features }) => {
              resolve([
                {
                  label: t("common:investLandsNum"),
                  value: features[0]?.attributes?.COUNT || 0,
                  whereClause: `DISTRICT_NAME =${districtData.DISTRICT_NAME} AND MUNICIPALITY_NAME=${districtData.MUNICIPALITY_NAME} AND PARCEL_MAIN_LUSE=20 AND USING_SYMBOL LIKE 'ت%'`,
                  symbol: "ت",
                  id: 5,
                  type: "invest",
                  order: orderLandsInTbl["invest"],
                  color: landsColors.invest,
                },
              ]);
            },
            callbackError: (err) => {
              notificationMessage(t("common:retrievError"), 4);

              reject(err);
            },
          });
        })
      );
      //todo: add royal, government lands to promises
      // TODO: add condition checking the allowed user group
      // owner type [private - royal - sales] + used lands order 3

      if (isCurrentUserAuthToRpyalGovSalesLands){
      promises.push(
        new Promise((resolve, reject) => {
          let layerID = getLayerId(map.__mapInfo, PARCEL_LANDS_LAYER_NAME);
          let queryParams = {
            url: window.mapUrl + "/" + layerID,
            notShowLoading: false,
            returnGeometry: false,
            where: `DISTRICT_NAME =${districtData.DISTRICT_NAME} AND OWNER_TYPE IS NOT Null`, //1 private, 2 royal, 3 sales
            statistics: [
              {
                type: "count",
                field: "OWNER_TYPE",
                name: "count",
              },
            ],
            groupByFields: ["OWNER_TYPE"],
          };
          queryTask({
            ...queryParams,
            callbackResult: ({ features }) => {
              let ownerTypeLands = [];
              if (features.length) {
                ownerTypeLands = features.map((i) => {
                  return {
                    label:
                      i.attributes?.OWNER_TYPE === 1
                        ? t("common:allocatedLandsNum")
                        : i.attributes?.OWNER_TYPE === 2
                        ? t("common:royalLandsNum")
                        : t("common:salesLandsNum"),
                    value: i?.attributes?.COUNT || 0,
                    whereClause: `DISTRICT_NAME =${districtData.DISTRICT_NAME} AND OWNER_TYPE='${i.attributes?.OWNER_TYPE}'`,
                    id: i.attributes?.OWNER_TYPE,
                    type:
                      i.attributes?.OWNER_TYPE === 1
                        ? "private"
                        : i.attributes?.OWNER_TYPE === 2
                        ? "royal"
                        : "sales",
                    order: 3,
                    color:
                      i.attributes?.OWNER_TYPE === 1
                        ? landsColors.private
                        : i.attributes?.OWNER_TYPE === 2
                        ? landsColors.royal
                        : landsColors.sales,
                  };
                });
                if (ownerTypeLands.length !== 3) {
                  // add the missing one
                  ownerTypeLands = [1, 2, 3].map((ownerTypeVal) => {
                    let isOwnerTypeLandExist = ownerTypeLands.find(
                      (i) => i.id === ownerTypeVal
                    );
                    if (!isOwnerTypeLandExist) {
                      return {
                        label:
                          ownerTypeVal === 1
                            ? t("common:allocatedLandsNum")
                            : ownerTypeVal === 2
                            ? t("common:royalLandsNum")
                            : t("common:salesLandsNum"),
                        value: 0,
                        whereClause: `DISTRICT_NAME =${districtData.DISTRICT_NAME} AND OWNER_TYPE='${ownerTypeVal}'`,
                        id: ownerTypeVal,
                        type:
                          ownerTypeVal === 1
                            ? "private"
                            : ownerTypeVal === 2
                            ? "royal"
                            : "sales",
                        order:
                          ownerTypeVal === 1
                            ? orderLandsInTbl["private"]
                            : ownerTypeVal === 2
                            ? orderLandsInTbl["royal"]
                            : orderLandsInTbl["sales"],
                        color:
                          ownerTypeVal === 1
                            ? landsColors.private
                            : ownerTypeVal === 2
                            ? landsColors.royal
                            : landsColors.sales,
                      };
                    } else {
                      return isOwnerTypeLandExist;
                    }
                  });
                }
              } else {
                ownerTypeLands = [1, 2, 3].map((i) => {
                  return {
                    label:
                      i === 1
                        ? t("common:allocatedLandsNum")
                        : i === 2
                        ? t("common:royalLandsNum")
                        : t("common:salesLandsNum"),
                    value: 0,
                    whereClause: `DISTRICT_NAME =${districtData.DISTRICT_NAME} AND OWNER_TYPE='${i}'`,
                    id: i,
                    type: i === 1 ? "private" : i === 2 ? "royal" : "sales",
                    order: 3,
                    color:
                      i === 1
                        ? landsColors.private
                        : i === 2
                        ? landsColors.royal
                        : landsColors.sales,
                  };
                });
              }
              resolve([...ownerTypeLands]);
            },
            callbackError: (err) => {
              notificationMessage(t("common:retrievError"), 4);

              reject(err);
            },
          });
        })
      );
    }
    
      // with used lands
      if (isCurrentUserAuthToRpyalGovSalesLands) {
      promises.push(
        new Promise((resolve, reject) => {
          let layerID = getLayerId(map.__mapInfo, PARCEL_LANDS_LAYER_NAME);
          let queryParams = {
            url: window.mapUrl + "/" + layerID,
            notShowLoading: true,
            returnGeometry: false,
            where: `DISTRICT_NAME =${districtData.DISTRICT_NAME} AND MUNICIPALITY_NAME=${districtData.MUNICIPALITY_NAME} AND (((ISBUILD is not null OR PARCEL_LICENSED = 1 OR REQ_TASK_TEXT is not null OR SPLIT_TYPE = 1 OR SPLIT_TYPE = 2) AND OWNER_TYPE IS Null) OR OWNER_TYPE IS NOT Null)`, // replace PLAN_CLASS with PLAN_NO in (plan no with class = 2)
            statistics: [
              {
                type: "count",
                field: "USING_SYMBOL",
                name: "count",
              },
            ],
          };
          queryTask({
            ...queryParams,
            callbackResult: ({ features }) => {
              resolve([
                {
                  label: t("common:usedLandsNum"),
                  value: features[0]?.attributes?.COUNT || 0,
                  whereClause: `DISTRICT_NAME =${districtData.DISTRICT_NAME} AND MUNICIPALITY_NAME=${districtData.MUNICIPALITY_NAME} AND (((ISBUILD is not null OR PARCEL_LICENSED = 1 OR REQ_TASK_TEXT is not null OR SPLIT_TYPE = 1 OR SPLIT_TYPE = 2) AND OWNER_TYPE IS Null) OR OWNER_TYPE IS NOT Null)`,
                  id: 1,
                  type: "usedLands",
                  order: 1,
                  color: landsColors.usedLands,
                },
              ]);
            },
            callbackError: (err) => {
              notificationMessage(t("common:retrievError"), 4);

              reject(err);
            },
          });
        })
      );
    }
      let results = await Promise.all(promises);
      let resultsFlat = results.flat();
      let plannedLands = resultsFlat.find((i) => i.type === "plannedLands");
       if (isCurrentUserAuthToRpyalGovSalesLands) {
          let usedLands = resultsFlat.find((i) => i.type === "usedLands");
        let nonUsedLands = {
          label: t("common:nonUsedLandsNum"),
          id: 9,
          whereClause: `DISTRICT_NAME =${districtData.DISTRICT_NAME} AND MUNICIPALITY_NAME=${districtData.MUNICIPALITY_NAME} AND ((ISBUILD is null AND PARCEL_LICENSED is null AND REQ_TASK_TEXT is null AND SPLIT_TYPE <> 1 AND SPLIT_TYPE <> 2 AND OWNER_TYPE IS Null)) `,
          value: plannedLands.value - usedLands.value,
          type: "nonUsedLands",
          order: 2,
          color: landsColors.nonUsedLands,
        };
        resultsFlat.push(nonUsedLands);
      }
      resultsFlat = resultsFlat.map((item) => {
        return {
          ...item,
          percentage: item.value / plannedLands.value,
        };
      });
      let sortedResults = resultsFlat.sort((a, b) => a.order - b.order);
      console.log(sortedResults);
      setShownData(sortedResults);
      setIsLoading(false);
    } catch (err) {
      console.log({ err });
      closeModal();
      setIsLoading(false);
    }
  };
  //helpers
  function getNeededItemObj(item) {
    // if (item.USING_SYMBOL.startsWith("ت-ث"))
    //     return {
    //         label: t('common:investLandsNum'),
    //         value: item.COUNT,
    //         whereClause: `DISTRICT_NAME =${districtData.DISTRICT_NAME} AND MUNICIPALITY_NAME=${districtData.MUNICIPALITY_NAME} AND USING_SYMBOL LIKE 'ت-ث%'`,
    //         symbol: "ت-ث",
    //         id: 5,
    //         color: generateRandomColor(),
    //         type: 'invest',
    //         order: 3
    //     };
    // else
    if (
      item.USING_SYMBOL.startsWith("ت") &&
      !item.USING_SYMBOL.startsWith("ت-ث")
    )
      return {
        label: t("common:commercialLandsNum"),
        value: item.COUNT,
        whereClause: `DISTRICT_NAME =${districtData.DISTRICT_NAME} AND MUNICIPALITY_NAME=${districtData.MUNICIPALITY_NAME} AND USING_SYMBOL LIKE 'ت%'`,
        symbol: "ت",
        id: 6,
        color: landsColors.commercial,
        type: "commercial",
        order: 3,
      };
    else if (item.USING_SYMBOL.startsWith("س"))
      return {
        label: t("common:resedntialLandsNum"),
        value: item.COUNT,
        whereClause: `DISTRICT_NAME =${districtData.DISTRICT_NAME} AND MUNICIPALITY_NAME=${districtData.MUNICIPALITY_NAME} AND USING_SYMBOL LIKE 'س%'`,
        symbol: "س",
        id: 1,
        color: landsColors.resedential,
        // dontShowOnChart: true,
        type: "resedential",
        order: 3,
      };
  }
  function getSymbolCharacters(item) {
    if (item.USING_SYMBOL.startsWith("ت-ث")) return "ت-ث";
    else if (
      item.USING_SYMBOL.startsWith("ت") &&
      !item.USING_SYMBOL.startsWith("ت-ث")
    )
      return "ت";
    else if (item.USING_SYMBOL.startsWith("خ")) return "خ";
    else if (item.USING_SYMBOL.startsWith("س")) return "س";
    else if (item.USING_SYMBOL.startsWith("م")) return "م";
  }
  async function getGovRoyalLandsData(url, type) {
    let data = {},
      res = {};
    try {
      res = await axios.get(url);
    } catch (err) {
      notificationMessage(t("common:retrievError"), 4);
      // return err
    }
    let spatialIDs = [];
    if (type === "royal") {
      spatialIDs = res?.data?.results
        ? res?.data?.results
            ?.filter((item) => item.spatial_id)
            ?.map((item) => item.spatial_id) || []
        : [];
      if (spatialIDs.length > 1000) spatialIDs = spatialIDs.slice(0, 1000);
    } else {
      spatialIDs = res?.data?.results
        ? res?.data?.results
            ?.filter((item) => item.parcel_part_no)
            ?.map((item) => item.parcel_part_no) || []
        : [];
      if (spatialIDs.length > 1000) spatialIDs = spatialIDs.slice(0, 1000);
    }
    let hasSpatialIds = spatialIDs?.length;
    switch (type) {
      case "royal":
        console.log({ type, res });
        data = {
          label: t("common:royalLandsNum"),
          value: res?.data?.count ? res?.data?.count : 0,
          whereClause: hasSpatialIds
            ? `DISTRICT_NAME =${districtData.DISTRICT_NAME} AND MUNICIPALITY_NAME=${
                districtData.MUNICIPALITY_NAME
              } AND PARCEL_SPATIAL_ID IN (${spatialIDs.join(" , ")})`
            : "",
          color: landsColors.royal,
          id: 55,
        };
        break;

      default:
        console.log({ type, res });
        data = {
          label: t("common:allocatedLandsNum"),
          value: res?.data?.count ? res?.data?.count : 0,
          whereClause: hasSpatialIds
            ? `DISTRICT_NAME =${districtData.DISTRICT_NAME} AND MUNICIPALITY_NAME=${
                districtData.MUNICIPALITY_NAME
              } AND PARCEL_SPATIAL_ID IN (${spatialIDs.join(" , ")})`
            : "",
          color: landsColors.private,
          id: 56,
        };
        break;
    }
    return data;
  }
  return (
    <div>
      {shownData?.length ? (
        <DistrictLandStatisticsModal
          map={map}
          districtData={districtData}
          landbaseParcelData={landbaseParcelData}
          shownData={shownData}
          showDistrictLandsStat={shownData?.length}
          openDistrictLandsStatModal={() => {
            closeModal();
            setShownData([]);
          }}
        />
      ) : null}
    </div>
  );
}

export default DistrictDataModal;
