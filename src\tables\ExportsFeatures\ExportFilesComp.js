import React from "react";
import { Menu, Dropdown, Space, Button, Tooltip } from "antd";
import { useTranslation } from "react-i18next";

import { faFileExport } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import ExportCSV from "./ExportCSV";
import {
  executeGPTool,
  showLoading,
  getLayerId,
  queryTask,
  getFeatureDomainName,
  getFromEsriRequest,
} from "../../helper/common_func";
import { PARCEL_LANDS_LAYER_NAME, externalBtnsForTblData } from "../../helper/constants";
import { notificationMessage } from "../../helper/utilsFunc";

function ExportFilesComp({
  layerData,
  dataSet,
  labels,
  columns,
  filterWhereClause,
  isDepend,
  map,
  colFilterWhere
}) {
  const { t } = useTranslation("print", "common");
  const [exportedData, setExportedData] = React.useState({
    dataSet: [], columns: [], labels: [], layerName: '', whereClause: ''
  })
  const exportFile = (fileType) => {
    if (dataSet.length) {
      let neededWhereClause = isDepend
        ? filterWhereClause.dep.filtered
          ? filterWhereClause.dep.filtered
          : filterWhereClause.dep.default
        : filterWhereClause.current;
      if (isDepend) neededWhereClause = isDepend==="1=1"?colFilterWhere.dep.join(" AND "):isDepend+ " AND " + colFilterWhere.dep.join(" AND ");
      else neededWhereClause =neededWhereClause==="1=1"?colFilterWhere.current.join(" AND "):neededWhereClause+ " AND " + colFilterWhere.current.join(" AND ");
      neededWhereClause = neededWhereClause||"1=1"
      let whereClause = [
        {
          ["SDE." + layerData.layerName]: neededWhereClause,
        },
      ];
      let userObj = localStorage.getItem('user');
      if(userObj) userObj = JSON.parse(userObj);
      let params = {
        Filters: whereClause,
        FileType: fileType,
        token : userObj ? userObj.esriToken : ''
      };
      showLoading(true);
      //notification with it is succeeded
      notificationMessage(t("print:fileUploading"), 5);
      executeGPTool(
        window.exportFeaturesGPUrl,
        params,
        callBackExportFile,
        callbackExportError,
        "output_value",
        fileType === 'CAD' ? 'execute' : 'submitJob',
        userObj?.esriToken
      );
    } else {
      //notify there is no data to export
      notificationMessage(t("common:noDataAvail"))
    }
  };

  function callBackExportFile(result) {
    if (result) {
      let anchor = document.createElement("a");
      anchor.href = window.filesURL + result;
      // anchor.download = layersNames[activeLink].layerName
      document.body.appendChild(anchor);
      anchor.click();
    }
    showLoading(false);
  }
  function callbackExportError(err) {
    console.log(err);
    //notification with something error happened
    notificationMessage(t("print:ErrorOccurd"), 5);
    showLoading(false);
  }
  const exportPDF =async () => {
    let mapInfo = await getFromEsriRequest(window.dashboardMapUrl + "?f=pjson");

    let neededWhereClause = isDepend
        ? filterWhereClause.dep.filtered
          ? filterWhereClause.dep.filtered
          : filterWhereClause.dep.default
        : filterWhereClause.current;
      if (isDepend) neededWhereClause = isDepend==="1=1"?colFilterWhere.dep.join(" AND "):isDepend+ " AND " + colFilterWhere.dep.join(" AND ");
      else neededWhereClause =neededWhereClause==="1=1"?colFilterWhere.current.join(" AND "):neededWhereClause+ " AND " + colFilterWhere.current.join(" AND ");
      neededWhereClause = neededWhereClause||"1=1"
    let layerID = getLayerId({
      info:{
        $layers:mapInfo
      }
    }, layerData?.layerName);

    localStorage.setItem(
      "dataForExportPDF",
      layerID +
      ";" +
      neededWhereClause +
      ";" +
      layerData.layerMetadata.outFields
    );
    window.open(process.env.PUBLIC_URL + "/ExportPdf");
    // exportPDFRef.current.click();
  };
  const isMenuShown =()=>{
    let xlIcon = columns.length &&
    layerData.layerMetadata?.dependencies?.find(
      (d) => d.name === externalBtnsForTblData.exportXlAttrTbl
    );
    let kmlIcon = layerData.layerMetadata?.dependencies?.find(
      (d) => d.name === externalBtnsForTblData.exportKmlAttrTbl
    );
    let googleIcon = layerData.layerMetadata?.dependencies?.find(
      (d) => d.name === externalBtnsForTblData.googleMapsAttrTbl
    );
    let cadIcon = layerData.layerMetadata?.dependencies?.find(
      (d) => d.name === externalBtnsForTblData.exportCadAttrTbl
    );
let shpIcon =layerData.layerMetadata?.dependencies?.find(
(d) => d.name === externalBtnsForTblData.exportShpAttrTbl
)
let pdfIcon = layerData.layerMetadata?.dependencies?.find(
(d) => d.name === externalBtnsForTblData.exportPdfAttrTbl
)

return xlIcon || kmlIcon || googleIcon || cadIcon||pdfIcon;
  }
  const exportMenu = () => {
    let isLayer = map.__mapInfo.info.$layers.layers.find(
      (lay) => lay.name === layerData.layerName
    );
    const convertToCSV = (dataSet, columns, labels) => {
      // Add BOM for UTF-8 to keep the arabic charchters as it is in file
      const bom = '\uFEFF';
      const header = labels.join(",") + "\n";
  
      const rows = dataSet.map(obj => {
          return columns.map(col => {
              const value = obj[col] !== undefined ? obj[col] : '';
              return `"${value}"`; // Wrap values in quotes to handle commas
          }).join(",");
      }).join("\n");
  
      return bom + header + rows; // Include BOM in the final CSV string
  };
  
  const downloadCSV = (exportedData) => {
      const { dataSet, columns, labels } = exportedData;
      const csv = convertToCSV(dataSet, columns, labels);
      const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement("a");
      const url = URL.createObjectURL(blob);
      link.setAttribute("href", url);
      link.setAttribute("download", `${exportedData.layerName || 'data'}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
  };
  async  function exportCSVFile(evt) {

    if (evt.target !== evt.currentTarget && evt.currentTarget.querySelector("#main-elem-for-export")) return;
      let neededWhereClause = isDepend
        ? filterWhereClause.dep.filtered
          ? filterWhereClause.dep.filtered
          : filterWhereClause.dep.default
        : filterWhereClause.current;
      if (isDepend) neededWhereClause = isDepend==="1=1"?colFilterWhere.dep.join(" AND "):isDepend+ " AND " + colFilterWhere.dep.join(" AND ");
      else neededWhereClause =neededWhereClause==="1=1"?colFilterWhere.current.join(" AND "):neededWhereClause+ " AND " + colFilterWhere.current.join(" AND ");
      neededWhereClause = neededWhereClause||"1=1"
      let mapInfo = await getFromEsriRequest(window.dashboardMapUrl + "?f=pjson");
      let layerID = getLayerId({
        info:{
          $layers:mapInfo
        }
      }, layerData?.layerName);

      let queryParams = {
        url: window.dashboardMapUrl + "/" + layerID,
        notShowLoading: false,
        returnGeometry: false,
        where: neededWhereClause
      };
      queryTask({
        ...queryParams,
        callbackResult: ({ features }) => {
          if (features.length)
            getFeatureDomainName(features, layerID,false,window.dashboardMapUrl).then((feats) => {
              let reqData = feats.map((f) => {
                return { ...f.attributes };
              });
              downloadCSV({
                dataSet: reqData,
                columns: columns.filter(
                  (c) => !["OBJECTID"].includes(c) && !c.includes('_SPATIAL_ID')
                ),
                labels: labels,
              })
              setExportedData({
                dataSet: reqData,
                columns: columns.filter(
                  (c) => !["OBJECTID"].includes(c) && !c.includes('_SPATIAL_ID')
                ),
                labels: labels,
                layerName: layerData?.layerName,
                whereClause: neededWhereClause
              })
            });
        }, 
      })
    }

    return (
      <Menu className="exportMenu">
         {columns.length &&
              layerData.layerMetadata?.dependencies?.find(
                (d) => d.name === externalBtnsForTblData.exportXlAttrTbl
              ) &&
        <Menu.Item >

           
                <div onClick={(e) => exportCSVFile(e)}>
                  {/* {exportedData.dataSet.length && exportedData.whereClause? */}
                                 {/* <ExportCSV isForceClick={true} {...exportedData} setExportedData={setExportedData} />: */}
                                {t("common:extractExcelFile")}
                              {/* } */}
                </div>
                
                </Menu.Item>
                }
        {layerData.layerMetadata?.dependencies?.find(
            (d) => d.name === externalBtnsForTblData.exportKmlAttrTbl
          ) && (
        <Menu.Item disabled={!isLayer}>
          
          
              <span onClick={() => exportFile("KML")}>
                {t("print:ExtractFile")} KML
              </span>
        </Menu.Item>
            )}
        {layerData.layerMetadata?.dependencies?.find(
            (d) => d.name === externalBtnsForTblData.googleMapsAttrTbl
          ) &&
        <Menu.Item disabled={!isLayer}>
           <span>{t("print:googleMapExtr")}</span>
        </Menu.Item>
           }
{layerData.layerMetadata?.dependencies?.find(
            (d) => d.name === externalBtnsForTblData.exportCadAttrTbl
          ) && (
        <Menu.Item disabled={!isLayer}>
          
              <span onClick={() => exportFile("CAD")}>{t("print:ExtractFile")} CAD</span>
        </Menu.Item>
            )}
        {layerData.layerMetadata?.dependencies?.find(
            (d) => d.name === externalBtnsForTblData.exportShpAttrTbl
          ) && (
        <Menu.Item disabled={!isLayer}>
          
              <span onClick={() => exportFile("Shape")}>
                {t("print:ExtractFile")} Shape
              </span>
        </Menu.Item>
            )}
          {layerData.layerMetadata?.dependencies?.find(
            (d) => d.name === externalBtnsForTblData.exportPdfAttrTbl
          ) &&
        <Menu.Item disabled={!isLayer}>
        
         <span onClick={exportPDF}>{t("print:ExtractFile")} PDF</span>
        </Menu.Item>
         }
      </Menu>
    );
  };

  return isMenuShown() ? (
    <Dropdown overlay={exportMenu()}>
      <a onClick={(e) => e.preventDefault()}>
        <Space>
          <Button className="tableHeaderBtn">
            <Tooltip placement={'left-start'} title={t("print:export")}>
              <FontAwesomeIcon icon={faFileExport} />
            </Tooltip>
          </Button>
        </Space>
      </a>
    </Dropdown>
  ):null;
}

export default ExportFilesComp;
