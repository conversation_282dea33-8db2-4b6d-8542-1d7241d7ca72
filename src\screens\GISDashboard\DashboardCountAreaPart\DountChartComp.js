import React, { useState } from "react";

import CustomDonutChart from "../dashboardCharts/customCharts/CustomDonutChart";

export default function DountChartComp({
  chartsData,
  type,
  queryData,
  map,
  activeHeatMapFactor,
  setActiveHeatMapFactor,shownListVal
 
}) {

 
  return (
   <>
      {type === "count" ? (
        chartsData[type] && typeof chartsData[type] === "number" ? (
          <h2 style={{ textAlign: "center" }}>{chartsData[type]}</h2>
        ) : chartsData[type] ? (
          <CustomDonutChart
            data={chartsData[type]}
            shownListVal={shownListVal}
            boundAdminType={queryData.selectedBoundaryType.value}
            statisticsType={type}
            map={map}
            queryData={queryData}
            activeHeatMapFactor={activeHeatMapFactor}
          />
        ) : null
      ) : type !== "count" ? (
        chartsData[type]?.value &&
        typeof chartsData[type]?.value === "number" ? (
          <h2>{chartsData[type]?.value}</h2>
        ) : chartsData[type]?.value ? (
          <CustomDonutChart
            data={chartsData[type]?.value}
            shownListVal={shownListVal}
            boundAdminType={queryData.selectedBoundaryType.value}
            statisticsType={type}
            map={map}
            activeHeatMapFactor={activeHeatMapFactor}
            queryData={queryData}
          />
        ) : null
      ) : null}
    </>
  );
}
