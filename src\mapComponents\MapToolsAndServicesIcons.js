import React, { useState, useEffect } from "react";
import leftIcon from "../assets/images/leftMenuIcon.svg";
import { FaBagShopping } from "react-icons/fa6";
import * as watchUtils from "@arcgis/core/core/watchUtils";
import Fade from "react-reveal/Fade";
import Draw from "@arcgis/core/views/draw/Draw";
import Extent from "@arcgis/core/geometry/Extent";
import Graphic from "@arcgis/core/Graphic";
import Zoom from "@arcgis/core/widgets/Zoom";
import { Tooltip } from "@mui/material";
import compareLayers from "../assets/images/tools/tool1.svg";
import { toJpeg, toPng, toSvg } from "html-to-image";
import help from "../assets/images/tools/help.svg";
import inquiry from "../assets/images/tools/tool2.svg";
import googleMaps from "../assets/images/tools/tool3.svg";
import smallMap from "../assets/images/tools/tool4.svg";
import layersMenu from "../assets/images/tools/tool5.svg";
import print from "../assets/images/sidemenu/print.svg"; //====================================
import legend from "../assets/images/tools/tool1.svg";
import traffic from "../assets/images/tools/tool7.svg";
import screenshot from "../assets/images/tools/tool7.svg";
import hashIcon from "../assets/images/tools/tool9.svg";
import gas from "../assets/images/services/gas.svg";
import water from "../assets/images/services/water.svg";
import less from "../assets/images/services/less.svg";
import homeIcon from "../assets/images/services/homeIcon.svg";
import fullScreenIcon from "../assets/images/services/fullScreenIcon.svg";
import more from "../assets/images/services/more.svg";
import pharmacy from "../assets/images/services/pharmacy.svg";
import foodcart from "../assets/images/services/foodcart.svg";
import hospital from "../assets/images/services/hospital.svg";
import setting from "../assets/images/services/setting.svg";
import ServicesSearch from "./Services/ServicesSearch";
import AllTools from "./tools/AllTools";
import Swipe from "@arcgis/core/widgets/Swipe";
import { RiFileListFill } from "react-icons/ri";
import {
  CustomTileLayer,
  clearGraphicLayer,
  getLayerId,
  getFeatureDomainName,
} from "../helper/common_func";
import { useTranslation } from "react-i18next";
import html2canvas from "html2canvas";
import { BsArrowsAngleExpand } from "react-icons/bs";
import { CgArrowsExpandLeft } from "react-icons/cg";
import { MdOutlineTune } from "react-icons/md";
import { RiFullscreenFill, RiGoogleLine, RiImageAddFill } from "react-icons/ri";
import { FiArrowUpRight, FiRefreshCw } from "react-icons/fi";
import { CiTrash, CiZoomOut } from "react-icons/ci";
import UpdateLand from "./tools/UpdateLand";
import {
  GoArrowLeft,
  GoArrowRight,
  GoInfo,
  GoProjectRoadmap,
} from "react-icons/go";
import { IoKeyOutline, IoLayersOutline } from "react-icons/io5";
import {
  FaExchangeAlt,
  FaArrowsAlt,
  FaRoute,
  FaArrowRight,
  FaArrowLeft,
  FaRegMap,
} from "react-icons/fa";
import SketchViewModel from "@arcgis/core/widgets/Sketch/SketchViewModel";
import GraphicsLayer from "@arcgis/core/layers/GraphicsLayer";
import SimpleFillSymbol from "@arcgis/core/symbols/SimpleFillSymbol";
import SimpleLineSymbol from "@arcgis/core/symbols/SimpleLineSymbol";
import SimpleMarkerSymbol from "@arcgis/core/symbols/SimpleMarkerSymbol";
import TextSymbol from "@arcgis/core/symbols/TextSymbol";
import Query from "@arcgis/core/rest/support/Query";
import * as query from "@arcgis/core/rest/query";
import Polygon from "@arcgis/core/geometry/Polygon";
import Point from "@arcgis/core/geometry/Point";

import trash_img from "../assets/icons/left-bar/trash.svg";
import help_img from "../assets/icons/left-bar/help.svg";
import move_img from "../assets/icons/left-bar/move.svg";

// import full_screen_img from "../assets/icons/left-bar/fullscreen.png";
import full_map_img from "../assets/icons/left-bar/fullMap.svg";
import google_maps_img from "../assets/icons/left-bar/googleMaps.svg";
import inquiry_img from "../assets/icons/left-bar/inquiry.svg";
import layers_menu_img from "../assets/icons/left-bar/layersMenu.svg";
import next_img from "../assets/icons/left-bar/next.svg";
import prev_img from "../assets/icons/left-bar/prev.svg";
import screenshot_img from "../assets/icons/left-bar/screenshot.svg";
import small_map_img from "../assets/icons/left-bar/smallMap.svg";
import zoom_in_img from "../assets/icons/left-bar/zoom.svg";
import zoom_out_img from "../assets/icons/left-bar/zoom-out.svg";
import traffic_img from "../assets/icons/left-bar/traffic.svg";
import map_key_img from "../assets/icons/left-bar/map_key.svg";
import compare_layer_img from "../assets/icons/left-bar/compare_layers.svg";
import collapse_img from "../assets/icons/left-bar/collapse.svg";

import { TbScanEye } from "react-icons/tb";
import { GiEarthAmerica } from "react-icons/gi";
import { LuZoomIn, LuZoomOut } from "react-icons/lu";
import { FaPrint, FaCameraRetro } from "react-icons/fa";
//import compare_layers_img from "../assets/images/compare_layers.svg";//==========================
export default function MapToolsAndServicesIcons(props) {
  const { t } = useTranslation("map");
  const [openServSearch, setServSearch] = useState(false);
  const [activeService, setActiveService] = useState(0);
  const [activeServiceItem, setActiveServiceItem] = useState(null);
  const [openToolData, setToolData] = useState(false);
  const [activeTool, setActiveTool] = useState(0);
  const [sketch, setSketch] = useState(null);
  const [selectedFeatures, setSelectedFeatures] = useState([]);
  const [showUpdateLand, setShowUpdateLand] = useState(false);

  const enablePolygonSelectionMode = (map, onResult) => {
    if (!map) {
      console.error("Map is undefined");
      return;
    }

    const graphicsLayer = map.findLayerById("highlightGraphicLayer");

    if (!graphicsLayer) {
      console.error("Graphics layer not found!");
      return;
    }

    // Create a separate layer for measurements and labels
    let measurementsLayer = map.findLayerById("MeasurementsLayer");
    if (!measurementsLayer) {
      measurementsLayer = new GraphicsLayer({ id: "MeasurementsLayer" });
      map.add(measurementsLayer);
    }

    let SketchLayer = map.findLayerById("SelectGraphicLayer");
    console.log("SketchLayer", SketchLayer);

    if (!SketchLayer) {
      SketchLayer = new GraphicsLayer({ id: "SelectGraphicLayer" });
      map.add(SketchLayer);
    }

    const view = window.__view || map.view;
    if (!view) {
      console.error("View is undefined");
      return;
    }

    const sketchViewModel = new SketchViewModel({
      view: view,
      layer: SketchLayer,
      creationMode: "update",
      availableCreateTools: ["polygon"],
      visibleElements: {
        createTools: { polygon: true },
        undoRedoMenu: true,
      },
      polygonSymbol: {
        type: "simple-fill",
        color: [255, 255, 255, 0.4],
        outline: {
          color: [255, 165, 0],
          width: 2,
        },
      },
    });

    setSketch(sketchViewModel);
    sketchViewModel.create("polygon");

    const createBufferPolygon = (point, bufferSize = 2) => {
      // Create a small square polygon around the point
      const x = point.x;
      const y = point.y;
      const half = bufferSize / 2;

      return new Polygon({
        rings: [
          [
            [x - half, y - half], // top left
            [x + half, y - half], // top right
            [x + half, y + half], // bottom right
            [x - half, y + half], // bottom left
            [x - half, y - half], // close the polygon
          ],
        ],
        spatialReference: point.spatialReference,
      });
    };

    // Function to calculate polygon area in square meters
    const calculateArea = (geometry) => {
      // Check if the geometry has a built-in method for area calculation
      if (geometry.area) {
        return geometry.area;
      }

      // Manual calculation for polygon area if needed
      // This is a simplified version - for actual implementation,
      // use appropriate geodesic calculations based on spatial reference
      return Math.abs(
        geometry.rings[0].reduce((area, point, i, points) => {
          const nextPoint = points[(i + 1) % points.length];
          return area + (point[0] * nextPoint[1] - nextPoint[0] * point[1]);
        }, 0) / 2
      );
    };

    // Function to calculate length between two points in map units
    const calculateDistance = (point1, point2) => {
      const dx = point2[0] - point1[0];
      const dy = point2[1] - point1[1];
      return Math.sqrt(dx * dx + dy * dy);
    };

    // Function to convert map point to geographic coordinates (latitude, longitude)
    const toLatLong = async (point, spatialReference) => {
      try {
        // Use ArcGIS projection functions to convert to geographic coordinates
        // This requires importing additional modules like geometryEngine or projection

        // For demonstration, assuming webMercatorUtils is available
        if (
          window.webMercatorUtils &&
          window.webMercatorUtils.webMercatorToGeographic
        ) {
          const geoPoint = window.webMercatorUtils.webMercatorToGeographic({
            x: point[0],
            y: point[1],
            spatialReference: spatialReference,
          });
          return { lat: geoPoint.y.toFixed(6), long: geoPoint.x.toFixed(6) };
        }

        // Fallback to original coordinates if conversion utils aren't available
        return {
          x: point[0].toFixed(2),
          y: point[1].toFixed(2),
          spatialReference: spatialReference,
        };
      } catch (error) {
        console.error("Error converting to lat/long:", error);
        return { x: point[0].toFixed(2), y: point[1].toFixed(2) };
      }
    };

    // Add measurements and labels to the polygon
    const addMeasurementsToPolygon = async (geometry) => {
      try {
        //  measurementsLayer.removeAll();

        if (!geometry || !geometry.rings || geometry.rings.length === 0) return;

        const rings = geometry.rings[0];
        const spatialReference = geometry.spatialReference;

        // Calculate and display edge lengths
        for (let i = 0; i < rings.length - 1; i++) {
          const point1 = rings[i];
          const point2 = rings[i + 1];

          // Calculate midpoint for placing the label
          const midPoint = [
            (point1[0] + point2[0]) / 2,
            (point1[1] + point2[1]) / 2,
          ];

          // Calculate distance between points
          const distance = calculateDistance(point1, point2);

          // Create text symbol for the distance
          const lengthLabel = new TextSymbol({
            color: "black",
            haloColor: "white",
            haloSize: 1,
            text: `${distance.toFixed(2)} م`,
            font: {
              size: 10,
              family: "sans-serif",
              weight: "bold",
            },
          });

          // Add length label at midpoint
          measurementsLayer.add(
            new Graphic({
              geometry: new Point({
                x: midPoint[0],
                y: midPoint[1],
                spatialReference: spatialReference,
              }),
              symbol: lengthLabel,
            })
          );
        }

        // Add corner coordinates (lat/long)
        for (let i = 0; i < rings.length - 1; i++) {
          const corner = rings[i];
          const coordinates = await toLatLong(corner, spatialReference);

          // Create marker for corner point
          const markerSymbol = new SimpleMarkerSymbol({
            style: "circle",
            color: "red",
            size: 6,
            outline: {
              color: "white",
              width: 1,
            },
          });

          // Add corner point
          measurementsLayer.add(
            new Graphic({
              geometry: new Point({
                x: corner[0],
                y: corner[1],
                spatialReference: spatialReference,
              }),
              symbol: markerSymbol,
            })
          );

          // Create text for coordinates
          const coordLabel = new TextSymbol({
            color: "blue",
            haloColor: "white",
            haloSize: 1,
            text: ` ${coordinates.x}, ${coordinates.y}`,
            font: {
              size: 8,
              family: "sans-serif",
            },
            yoffset: 15,
          });

          // Add coordinate label
          measurementsLayer.add(
            new Graphic({
              geometry: new Point({
                x: corner[0],
                y: corner[1],
                spatialReference: spatialReference,
              }),
              symbol: coordLabel,
            })
          );
        }

        // Calculate centroid (simplified)
        let sumX = 0,
          sumY = 0;
        for (let i = 0; i < rings.length - 1; i++) {
          sumX += rings[i][0];
          sumY += rings[i][1];
        }

        const centroidX = sumX / (rings.length - 1);
        const centroidY = sumY / (rings.length - 1);

        // Calculate area
        const area = calculateArea(geometry);

        // Create area label at centroid
        const areaLabel = new TextSymbol({
          color: "darkgreen",
          haloColor: "white",
          haloSize: 2,
          text: ` ${area.toFixed(2)} `,
          font: {
            size: 12,
            family: "sans-serif",
            weight: "bold",
          },
        });

        // Add area label at centroid
        measurementsLayer.add(
          new Graphic({
            geometry: new Point({
              x: centroidX,
              y: centroidY,
              spatialReference: spatialReference,
            }),
            symbol: areaLabel,
          })
        );
      } catch (error) {
        console.error("Error adding measurements:", error);
      }
    };

    const handleQuery = (geometry) => {
      try {
        const queryObject = new Query({
          geometry,
          spatialRelationship: "intersects",
          outFields: ["*"],
          returnGeometry: true,
        });

        // Using the imported query module directly
        return query
          .executeQueryJSON(
            `${window.mapUrl}/${getLayerId(map.__mapInfo, "Landbase_Parcel")}`,
            queryObject
          )
          .then((result) => {
            if (result.features?.length > 0) {
              return getFeatureDomainName(
                result.features,
                getLayerId(map.__mapInfo, "Landbase_Parcel")
              ).then((rfeatures) => {
                const highlightSymbol = new SimpleFillSymbol({
                  color: [0, 0, 0, 0],
                  outline: new SimpleLineSymbol({
                    color: [0, 0, 0],
                    width: 3,
                  }),
                });

                graphicsLayer.removeAll();

                result.features.forEach((land) => {
                  const highlightGraphic = new Graphic({
                    geometry: land.geometry,
                    symbol: highlightSymbol,
                  });
                  graphicsLayer.add(highlightGraphic);

                  // Add measurements to the selected land parcel
                  addMeasurementsToPolygon(land.geometry);
                });

                return rfeatures;
              });
            } else {
              console.log("No intersecting lands found.");
              measurementsLayer.removeAll();
              return [];
            }
          })
          .catch((error) => {
            console.error("Error executing query:", error);
            throw error;
          });
      } catch (error) {
        console.error("Error in query setup:", error);
        throw error;
      }
    };

    sketchViewModel.on("create", (event) => {
      if (event.state === "start") {
        try {
          // Create a buffer polygon around the initial point
          const point = new Point({
            x: event.graphic.geometry.rings[0][0][0],
            y: event.graphic.geometry.rings[0][0][1],
            spatialReference: event.graphic.geometry.spatialReference,
          });

          const bufferPolygon = createBufferPolygon(point);
          handleQuery(bufferPolygon)
            .then((rfeatures) => {
              console.log("Start point query result:", rfeatures);
              if (onResult) onResult(rfeatures);
            })
            .catch((error) => {
              console.error("Error during start point query:", error);
            });
        } catch (error) {
          console.error("Error creating buffer polygon:", error);
        }
      } else if (
        event.state === "complete" ||
        event.toolEventInfo?.type === "vertex-add"
      ) {
        try {
          handleQuery(event.graphic.geometry)
            .then((rfeatures) => {
              console.log("Create result:", rfeatures);
              if (onResult) onResult(rfeatures);

              // Also add measurements to the sketch polygon itself
              //     addMeasurementsToPolygon(event.graphic.geometry);
            })
            .catch((error) => {
              console.error("Error during create:", error);
            });
        } catch (error) {
          console.error("Error during create setup:", error);
        }
      }
    });

    sketchViewModel.on("update", (event) => {
      if (event.state === "complete" && !event.aborted) {
        try {
          handleQuery(event.graphics[0].geometry)
            .then((rfeatures) => {
              console.log("Update result:", rfeatures);
              if (onResult) onResult(rfeatures);
            })
            .catch((error) => {
              console.error("Error during update:", error);
            });
        } catch (error) {
          console.error("Error during update setup:", error);
        }
      }
    });
  };

  const Cleanup = () => {
    if (props.sketch) {
      sketch.destroy();
      setSketch(null);
    }

    // Clean up measurements layer when component unmounts or tool changes
    if (props.map) {
      const measurementsLayer = props.map.findLayerById("MeasurementsLayer");
      const SketchLayer = props.map.findLayerById("SelectGraphicLayer");
      const graphicsLayer = props.map.findLayerById("highlightGraphicLayer");
      if (measurementsLayer) {
        measurementsLayer.removeAll();
      }
      if (SketchLayer) {
        SketchLayer.removeAll();
      }
      if (graphicsLayer) {
        graphicsLayer.removeAll();
      }
    }
  };
  const extentChangeHandler = (evt) => {
    if (window.__prevExtent || window.__nextExtent) {
      window.__currentExtent = evt;
    } else {
      window.__preExtent = window.__currentExtent;
      window.__currentExtent = evt;
      window.__extentHistory = window.__extentHistory || [];
      window.__extentHistory.push({
        preExtent: window.__preExtent,
        currentExtent: window.__currentExtent,
      });
      window.__extentHistoryIndx = window.__extentHistory.length - 1;
    }
    window.__prevExtent = window.__nextExtent = false;
    //console.log('extent--------',_extentHistory);
    //extentHistoryChange();
  };

  useEffect(() => {
    watchUtils.whenTrue(props.map.view, "ready", () => {
      window.__fullExtent = props.map.view.extent.clone();
      window.__draw = new Draw({
        view: props.map.view,
      });
      watchUtils.whenOnce(props.map.view, "extent", () => {
        watchUtils.when(props.map.view, "stationary", (evt) => {
          if (evt) {
            extentChangeHandler(props.map.view.extent);
          }
        });
      });
    });
  }, []);

  const openServiceSearch = (e) => {
    setServSearch(true);
    setActiveService(e.id);
    setActiveServiceItem(e);
    setToolData(false);
    setActiveTool("");
  };

  const closeServiceSearch = () => {
    setServSearch(false);
    setActiveService(0);
  };

  const removeAllGraphicsOnMap = () => {
    props.map.view.graphics.removeAll();

    props.map.layers.items.forEach((layer) => {
      clearGraphicLayer(layer.id, props.map);
    });
  };

  const removeCurrentSelTool = () => {
    props.map.view.popup.close();
  };

  const enableViewPanning = () => {
    if (window.__evtViewDragHandler) {
      window.__evtViewDragHandler.remove();
      window.__evtViewDragHandler = null;
    }
    if (window.__evtViewKeyDownHandler) {
      window.__evtViewKeyDownHandler.remove();
      window.__evtViewKeyDownHandler = null;
    }
  };

  const displayDefaultCursor = () => {
    props.map.view &&
      props.map.view.container &&
      props.map.view.container.style &&
      "default" !== props.map.view.container.style.cursor &&
      (props.map.view.container.style.cursor = "default");
  };

  const disableActiveTool = () => {
    // you should put a condition here to disable the active tool
    setActiveTool("dis");
    removeCurrentSelTool();

    enableViewPanning();
    displayDefaultCursor();
    if (!window.__draw) {
      window.__draw = new Draw({
        view: props.map.view,
      });
    }
    window.__draw.reset();
  };

  window.DisableActiveTool = () => {
    disableActiveTool();
  };

  const disableViewPanning = () => {
    if (window.__evtViewDragHandler) {
      window.__evtViewDragHandler.remove();
      window.__evtViewDragHandler = null;
    }
    if (window.__evtViewKeyDownHandler) {
      window.__evtViewKeyDownHandler.remove();
      window.__evtViewKeyDownHandler = null;
    }
    window.__evtViewDragHandler = props.map.view.on("drag", (event) => {
      // prevents panning with the mouse drag event
      if (activeTool != "dis") event.stopPropagation();
    });

    window.__evtViewKeyDownHandler = props.map.view.on("key-down", (event) => {
      // prevents panning with the arrow keys
      var keyPressed = event.key;
      if (keyPressed.slice(0, 5) === "Arrow") {
        if (activeTool != "dis") event.stopPropagation();
      }
    });
  };

  const displayCrosshairCursor = () => {
    props.map.view.container.style.cursor = "crosshair";
  };

  const activeZoomIn = (e) => {
    if (true) {
      setActiveTool("zoomIn");
      removeCurrentSelTool();
      disableViewPanning();
      props.map.view.graphics.removeAll();
      if (!window.__draw) {
        window.__draw = new Draw({
          view: props.map.view,
        });
      }
      var action = window.__draw.create("rectangle");
      displayCrosshairCursor();
      //props.map.view.focus();
      action.on("vertex-add", drawRect);
      action.on("draw-complete", zoomIn);
      action.on("cursor-update", drawRect);
    } else {
      var zoom = new Zoom({
        view: props.map.view,
        visible: false,
      });

      zoom.zoomIn();
    }
  };

  const getExtentfromVertices = (vertices) => {
    var sx = vertices[0][0],
      sy = vertices[0][1];
    var ex = vertices[1][0],
      ey = vertices[1][1];
    var rect = {
      x: Math.min(sx, ex),
      y: Math.max(sy, ey),
      width: Math.abs(sx - ex),
      height: Math.abs(sy - ey),
      spatialReference: props.map.view.spatialReference,
    };
    if (rect.width !== 0 || rect.height !== 0) {
      return new Extent({
        xmin: parseFloat(rect.x),
        ymin: parseFloat(rect.y) - parseFloat(rect.height),
        xmax: parseFloat(rect.x) + parseFloat(rect.width),
        ymax: parseFloat(rect.y),
        spatialReference: rect.spatialReference,
      });
    } else {
      return null;
    }
  };

  const drawRect = (event) => {
    var vertices = event.vertices;
    //remove existing graphic
    props.map.view.graphics.removeAll();
    if (vertices.length < 2) {
      return;
    }

    // create a new extent
    var extent = getExtentfromVertices(vertices);

    var graphic = new Graphic({
      geometry: extent,
      symbol: {
        type: "simple-fill", // autocasts as SimpleFillSymbol
        color: [0, 0, 0, 0.3],
        style: "solid",
        outline: {
          // autocasts as SimpleLineSymbol
          color: [255, 0, 0],
          width: 1,
        },
      },
    });

    props.map.view.graphics.add(graphic);
  };

  const goToFullExtent = () => {
    setActiveTool("fullMap");
    props.map.view.goTo(window.__fullExtent);
    if (props.setIndicatorFullExtent) {
      props.setIndicatorFullExtent();
    }
  };

  function zoomOut(evt) {
    var vertices = evt.vertices;
    if (!window.__draw) {
      window.__draw = new Draw({
        view: props.map.view,
      });
    }
    window.__draw.reset();
    props.map.view.graphics.removeAll();
    var action = window.__draw.create("rectangle");
    //view.focus();
    action.on("vertex-add", drawRect);
    action.on("draw-complete", zoomOut);
    action.on("cursor-update", drawRect);
    if (evt.vertices.length === 1) {
      props.map.view.goTo({ scale: props.map.view.scale * 2 });
      return;
    }
    var sx = vertices[0][0],
      sy = vertices[0][1];
    var ex = vertices[1][0],
      ey = vertices[1][1];
    var rect = {
      x: Math.min(sx, ex),
      y: Math.max(sy, ey),
      width: Math.abs(sx - ex),
      height: Math.abs(sy - ey),
      spatialReference: props.map.view.spatialReference,
    };
    if (rect.width !== 0 || rect.height !== 0) {
      var scrPnt1 = props.map.view.toScreen(rect);
      var scrPnt2 = props.map.view.toScreen({
        x: rect.x + rect.width,
        y: rect.y,
        spatialReference: rect.spatialReference,
      });
      var mWidth = props.map.view.extent.width;
      var delta =
        ((mWidth * props.map.view.width) / Math.abs(scrPnt2.x - scrPnt1.x) -
          mWidth) /
        2;
      var vExtent = props.map.view.extent;
      props.map.view.goTo(
        new Extent({
          xmin: vExtent.xmin - delta,
          ymin: vExtent.ymin - delta,
          xmax: vExtent.xmax + delta,
          ymax: vExtent.ymax + delta,
          spatialReference: vExtent.spatialReference,
        })
      );
    }
  }

  const zoomIn = (evt) => {
    if (!window.__draw) {
      window.__draw = new Draw({
        view: props.map.view,
      });
    }
    window.__draw.reset();
    props.map.view.graphics.removeAll();
    var action = window.__draw.create("rectangle");
    //props.map.view.focus();
    action.on("vertex-add", drawRect);
    action.on("draw-complete", zoomIn);
    action.on("cursor-update", drawRect);
    if (evt.vertices.length === 1) {
      props.map.view.goTo({ scale: props.map.view.scale * 0.5 });
      return;
    }
    var extent = getExtentfromVertices(evt.vertices);
    if (extent.width !== 0 || extent.height !== 0) {
      props.map.view.goTo(extent);
    }
  };
  const activeZoomOut = () => {
    if (true) {
      setActiveTool("zoomOut");
      removeCurrentSelTool();
      disableViewPanning();
      props.map.view.graphics.removeAll();
      if (!window.__draw) {
        window.__draw = new Draw({
          view: props.map.view,
        });
      }
      var action = window.__draw.create("rectangle");
      displayCrosshairCursor();
      //view.focus();
      action.on("vertex-add", drawRect);
      action.on("draw-complete", zoomOut);
      action.on("cursor-update", drawRect);
    } else {
      var zoom = new Zoom({
        view: props.map.view,
        visible: false,
      });

      zoom.zoomOut();
    }
  };

  const goToPreviousExtent = () => {
    setActiveTool("prev");
    if (window.__extentHistory[window.__extentHistoryIndx].preExtent) {
      window.__prevExtent = true;
      if (window.__extentHistoryIndx > 0) {
        props.map.view.goTo(
          window.__extentHistory[window.__extentHistoryIndx].preExtent
        );
        window.__extentHistoryIndx--;
      }
    }
  };

  const goToNextExtent = () => {
    setActiveTool("next");
    window.__nextExtent = true;
    if (window.__extentHistory.length > window.__extentHistoryIndx + 1) {
      window.__extentHistoryIndx++;
      props.map.view.goTo(
        window.__extentHistory[window.__extentHistoryIndx].currentExtent
      );
    }
  };

  const openToolsData = (e, name) => {
    setServSearch(false);
    sketch?.destroy();
    setActiveService(0);
    setToolData(true);
    e !== undefined && e.target !== undefined
      ? setActiveTool(name)
      : setActiveTool("");
    console.log(e);
    // const customEvent = new CustomEvent("showPrintBox", {
    //   detail: { show: false },
    // });
    // document.dispatchEvent(customEvent);
    if (name == "compareLayers") {
      var swipe = props.map.view.ui._components.find(
        (x) =>
          x.widget._container &&
          x.widget._container.className.indexOf("swipe") > -1
      );

      if (swipe) {
        swipe.widget.destroy();
        props.map.view.ui.remove(swipe);
        setActiveTool("");
      } else {
        var swipeLayer = props.map.findLayerById("baseMap");
        let swipe = new Swipe({
          view: props.map.view,
          leadingLayers: [swipeLayer],
          direction: "horizontal", // swipe widget will move from top to bottom of view
          position: 50, // position set to middle of the view (50%)
        });
        props.map.view.ui.add(swipe);
      }
    } else if (name == "next") {
      goToNextExtent();
    } else if (name == "prev") {
      goToPreviousExtent();
    } else if (name == "zoomIn") {
      activeZoomIn();
    } else if (name == "zoomOut") {
      activeZoomOut();
    } else if (name == "fullMap") {
      goToFullExtent();
    } else if (name == "traffic") {
      if (!props.map.findLayerById("trafficLayerId")) {
        let layer = new CustomTileLayer({
          urlTemplate: window.trafficUrl,
          id: "trafficLayerId",
        });

        props.map.layers.add(layer);
      } else {
        props.map.remove(props.map.findLayerById("trafficLayerId"));
      }
    } else if (name == "screenshot") {
      //  debugger;
      async function convertIconsToImages() {
        const icons = document.querySelectorAll(".fas");
        //   debugger;
        for (const icon of icons) {
          const rect = icon.getBoundingClientRect();
          const canvas = document.createElement("canvas");
          canvas.width = rect.width;
          canvas.height = rect.height;

          const ctx = canvas.getContext("2d");

          // Draw the icon onto the canvas
          await html2canvas(icon, {
            backgroundColor: null,
          }).then((iconCanvas) => {
            ctx.drawImage(iconCanvas, 0, 0);
          });

          // Replace the icon with the canvas
          const img = document.createElement("img");
          img.src = canvas.toDataURL();
          img.style.width = `${rect.width}px`;
          img.style.height = `${rect.height}px`;
          icon.parentNode.replaceChild(img, icon);
        }
      }
      document.querySelector('[role="tooltip"]').style.visibility = "hidden";
      props.map.view.takeScreenshot().then(async (screenshot) => {
        const mapImg = new Image();
        mapImg.src = screenshot.dataUrl;
        mapImg.style.position = "absolute";
        mapImg.style.top = "0";
        mapImg.style.left = "0";
        mapImg.style.zIndex = "0.5"; // Place it behind the UI

        // Temporarily replace the map canvas with the image
        const mapContainer = document.querySelector("#mapDiv");
        mapContainer.appendChild(mapImg);
        //await convertIconsToImages();
        toJpeg(document.querySelector("body"), {
          cacheBust: true,
          style: {
            direction: "rtl",
            textAlign: "right",
          },
        }).then((dataUrl) => {
          const link = document.createElement("a");
          link.download = "arabic-ui-screenshot.jpeg";
          link.href = dataUrl;
          link.click();
          mapContainer.removeChild(mapImg);
        });
      });
    }
  };
  const [openToolservice, setToolService] = useState(true);
  const openToolsMenu = () => {
    if (openToolservice) {
      setToolService(false);
      setToolData(false);
      setActiveTool("");
      setActiveService(0);
      setServSearch(false);
    } else {
      setToolService(true);
      setToolData(false);
      setActiveTool("");
      setActiveService(0);
      setServSearch(false);
    }
  };
  const closeToolsData = (e) => {
    setToolData(false);
    setActiveTool("");
  };
  /*   const [tools] = useState([
    {
      id: 2,
      icon: inquiry,
      name: "inquiry",
      tooltip: "mapToolsServices.inquiry",
    },
    {
      id: 5,
      icon: layersMenu,
      name: "layersMenu",
      tooltip: "mapToolsServices.layersMenu",
    },
    {
      id: 3,
      icon: googleMaps,
      name: "googleMaps",
      tooltip: "mapToolsServices.googleMaps",
      className: "googleMapToolClass",
    },
    {
      id: 4,
      icon: smallMap,
      name: "smallMap",
      tooltip: "mapToolsServices.smallMap",
    },

    {
      id: 7,
      icon: traffic,
      name: "traffic",
      tooltip: "mapToolsServices.traffic",
    },

    //=============================================================================  button print Left
    {
      id: 6,
      icon: print,
      name: "print",
      tooltip: "mapToolsServices.print",
    },
  ]); */
  const [tools] = useState([
    {
      id: 2,
      icon: inquiry_img,
      name: "inquiry",
      tooltip: "mapToolsServices.inquiry",
    },
    {
      id: 5,
      icon: layers_menu_img,
      name: "layersMenu",
      tooltip: "mapToolsServices.layersMenu",
    },
    {
      id: 3,
      icon: google_maps_img,
      name: "googleMaps",
      tooltip: "mapToolsServices.googleMaps",
      className: "googleMapToolClass",
    },
    {
      id: 4,
      icon: small_map_img,
      name: "smallMap",
      tooltip: "mapToolsServices.smallMap",
    },

    {
      id: 7,
      icon: traffic_img,
      name: "traffic",
      tooltip: "mapToolsServices.traffic",
    },

    //=============================================================================  button print Left
    {
      id: 6,
      icon: screenshot_img,
      name: "screenshot",
      tooltip: "mapToolsServices.screenshot",
    },
    {
      id: 8,
      icon: full_map_img,
      name: "fullMap",
      tooltip: "mapTools.fullMap",
    },
    {
      id: 9,
      icon: zoom_in_img,
      name: "zoomIn",
      tooltip: "mapTools.zoomIn",
    },
    {
      id: 10,
      icon: zoom_out_img,
      name: "zoomOut",
      tooltip: "mapTools.zoomOut",
    },
    {
      id: 11,
      icon: next_img,
      name: "next",
      tooltip: "mapTools.next",
    },
    {
      id: 12,
      icon: prev_img,
      name: "prev",
      tooltip: "mapTools.prev",
    },
    {
      id: 13,
      // icon: FaPrint,
      name: "print",
      tooltip: "mapToolsServices.print",
      fallbackIcon: FaPrint,
    },
    // {
    //   id: 14,
    //   icon: FaCameraRetro,
    //   name: "screenshot",
    //   tooltip: "mapToolsServices.screenshot",
    // },
    // {
    //   //=========================
    //   id: 14,
    //   name: "compareLayers",
    //   tooltip: "mapToolsServices.compareLayers",
    //   onClick: openToolsData,
    //   icon: compare_layer_img,
    // },
    // trash icon
    {
      id: 14,
      name: "trash",
      tooltip: "mapToolsServices.trash",
      // onClick: openToolsData,
      icon: trash_img,
    },
    // {
    //   id: 15, //========================================== legend
    //   icon: map_key_img,
    //   name: "legend",
    //   tooltip: "mapToolsServices.legend",
    // },
    // {
    //   id: 16,
    //   // icon: full_screen_img,
    //   name: "select",
    //   tooltip: "mapToolsServices.select",
    //   fallbackIcon: RiFullscreenFill,
    // },
    // {
    //   id: 17,
    //   icon: traffic_img,
    //   name: "selectToUpdate",
    //   tooltip: "mapToolsServices.selectToUpdate",
    //   fallbackIcon: RiFullscreenFill,
    // },
  ]);

  // const [moreservices] = useState([
  //   {
  //     id: 4,
  //     icon: setting,
  //     tooltip: "mapToolsServices.maintenance",
  //     where: " SRVC_SUBTYPE = '10015' ",
  //   },
  //   {
  //     id: 5,
  //     icon: hospital,
  //     tooltip: "mapToolsServices.hospitals",
  //     where: " SRVC_TYPE = '700' ",
  //   },
  //   {
  //     id: 6,
  //     icon: foodcart,
  //     tooltip: "mapToolsServices.catering",
  //     where: " SRVC_SUBTYPE = '10005' ",
  //   },
  // ]);
  // const [openMoreSer, setOpenMoreSer] = useState(false);
  // const openMoreServices = () => {
  //   setOpenMoreSer(true);
  // };
  // const closeMoreServices = () => {
  //   setOpenMoreSer(false);
  // };
  //=================================================================

  const openHelp = (e) => {
    e.preventDefault();
    setServSearch(false);
    setActiveService(0);
    setToolData(true);
    e !== undefined && e.target !== undefined
      ? setActiveTool(e.target.name)
      : setActiveTool("");
    localStorage.removeItem("showHelp");
    localStorage.removeItem("showMetaHelp");
    localStorage.removeItem("showOpenSideHelp");
    localStorage.removeItem("showCardsResultHelp");
    localStorage.removeItem("showCardDetailsHelp");
    setTimeout(() => {
      props.setHelpShow(false);
      props.setHelpShow(true);
    }, 1);
    props.setHelpShow(false);
    console.log("zzz");
  };

  return (
    <div>
      <Tooltip
        title={
          openToolservice ? t("mapTools.CloseTools") : t("mapTools.OpenTools")
        }
        placement="top"
      >
        <div
          className="leftIconMenu openCloseToolServHelp"
          onClick={openToolsMenu}
        >
          {/* <img src={leftIcon} alt="" /> */}
          {/* <FaBagShopping className="hashTest" /> */}
          <img
            src={collapse_img}
            alt=""
            style={{ width: "100%", height: "100%", filter: "unset" }}
          />
        </div>
      </Tooltip>

      <Fade left delay={500}>
        <div
          className={
            openToolservice
              ? "openedservicesMenu servicesHelp"
              : "closedservicesMenu servicesHelp"
          }
        >
          <ul>
            {/* {openMoreSer ? (
              <Tooltip title={t("mapToolsServices.lessServ")} placement="top">
                <li onClick={closeMoreServices} className="moreLessIcon">
                  <img
                    src={less}
                    style={{ transform: "rotate(180deg" }}
                    alt="lessServices"
                  />
                </li>
              </Tooltip>
            ) : (
              <Tooltip title={t("mapToolsServices.moreServ")} placement="top">
                <li onClick={openMoreServices} className="moreLessIcon">
                  <img src={more} alt="moreServices" />
                </li>
              </Tooltip>
            )}
            {openMoreSer
              ? moreservices.map((s, index) => (
                  <Tooltip title={t(s.tooltip)} placement="top" key={index}>
                    <li
                      id={s.id}
                      onClick={() => openServiceSearch(s)}
                      className={
                        Number(activeService) === Number(s.id)
                          ? "activeService"
                          : "serviceLi"
                      }
                    >
                      <img src={s.icon} alt="servicesIcon" id={s.id} />
                    </li>
                  </Tooltip>
                ))
              : null} */}{" "}
            {/* <Tooltip
              title={t("mapToolsServices.compareLayers")}
              placement="top"
            >
              <li
                name="compareLayers"
                onClick={openToolsData}
                className="serviceLi"
              >
                <img
                  src={compareLayers}
                  name="compareLayers"
                  className="openedservicesMenuImg"
                  alt="servicesIcon"
                />
              </li>
            </Tooltip> */}
            {/* <Tooltip title={t("mapTools.fullScreen")} placement="top">
              <li
                className="fullscreenServHelp serviceLi"
                onClick={props.handle.enter}
              >
                <img
                  src={fullScreenIcon}
                  className="openedservicesMenuImg"
                  alt="fullScreenIcon"
                />
              </li>
            </Tooltip> */}
            {/* <Tooltip title={t("mapToolsServices.home")} placement="top">
              <li className=" serviceLi">
                <a
                  href={`${window.hostURL}/home/<USER>
                  target="_blank"
                  rel="noreferrer"
                >
                  <img
                    src={homeIcon}
                    alt="homeIcon"
                    className="openedservicesMenuImg"
                  />
                </a>
              </li>
            </Tooltip> */}
            {/* <Tooltip title={t("mapToolsServices.help")} placement="top">
              <li className="serviceLi " onClick={openHelp} name="openhelp">
                <img
                  src={help}
                  alt="servicesIcon"
                  name="openhelp"
                  className="openedservicesMenuImg"
                />
              </li>
            </Tooltip> */}
            <Tooltip title={t("mapTools.removeAll")} placement="top">
              <li
                className="serviceLi "
                onClick={removeAllGraphicsOnMap}
                name="openhelp"
              >
                {/* <CiTrash className="hashTest " /> */}
                <img src={trash_img} alt="" />
              </li>
            </Tooltip>
            <Tooltip title={t("mapTools.move")} placement="top">
              <li
                className="serviceLi "
                onClick={disableActiveTool}
                name="openhelp"
              >
                {/* <FaArrowsAlt className="hashTest " /> */}
                <img src={move_img} alt="" />
              </li>
            </Tooltip>
            {/* {
      id: 15, //========================================== legend
      icon: map_key_img,
      name: "legend",
      tooltip: "mapToolsServices.legend",
    },
    {
      id: 16,
      // icon: full_screen_img,
      name: "select",
      tooltip: "mapToolsServices.select",
      fallbackIcon: RiFullscreenFill,
    }, */}
            {selectedFeatures?.length > 0 && (
              <>
                <Tooltip title={t("mapTools.update")} placement="top">
                  <li
                    className="serviceLi"
                    onClick={() => setShowUpdateLand(true)}
                    name="legend"
                  >
                    <img src={map_key_img} alt="" />
                  </li>
                </Tooltip>

                {/* {showUpdateLand && (
                  <UpdateLand
                    languageState={props.languageState}
                    mainData={props.mainData}
                    map={props.map}
                    activeTool={activeTool}
                    closeToolsData={closeToolsData}
                    openToolsData={openToolsData}
                    openToolData={openToolData}
                    selectedFeatures={selectedFeatures}
                    Cleanup={Cleanup}
                  />
                )} */}
              </>
            )}
            <Tooltip title={t("mapTools.select")} placement="top">
              <li
                className="serviceLi "
                onClick={() => {
                  if (props.sketch) {
                    setSketch(null);
                  }

                  enablePolygonSelectionMode(props.map, (rfeatures) => {
                    // Handle the results here
                    setSelectedFeatures(rfeatures);
                    console.log("Selected features:", rfeatures);
                  });
                }}
                name="select"
              >
                {/* <FaArrowsAlt className="hashTest " /> */}
                {/* <img src={move_img} alt="" /> */}
                <RiFullscreenFill className="hashTest" name={"select"} />
              </li>
            </Tooltip>
            {/* <Tooltip title={t("mapToolsServices.select")} placement="top">
              <li
                className="serviceLi"
                name="select"
                onClick={(event) => {
                  openToolsData(event, "select");
                }}
              >
                <RiFullscreenFill
                  style={{ borderRadius: "30px" }}
                  className="hashTest "
                />
              </li>
            </Tooltip> */}
            {/* <Tooltip title={t("mapToolsServices.help")} placement="top">
              <li className="serviceLi " onClick={openHelp} name="openhelp">
                <MdOutlineTune className="hashTest CgArrowsExpandLeft" />
              </li>
            </Tooltip> */}
            <Tooltip
              title={t("mapToolsServices.compareLayers")}
              placement="top"
            >
              <li
                className="serviceLi "
                onClick={(event) => openToolsData(event, "compareLayers")}
                name="compareLayers"
              >
                <img src={compare_layer_img} alt="" />
              </li>
            </Tooltip>
            <Tooltip title={t("mapToolsServices.help")} placement="top">
              <li className="serviceLi " onClick={openHelp} name="openhelp">
                {/* <CgArrowsExpandLeft className="hashTest CgArrowsExpandLeft" /> */}
                <img src={help_img} alt="" />
              </li>
            </Tooltip>
          </ul>

          {showUpdateLand && (
            <UpdateLand
              languageState={props.languageState}
              mainData={props.mainData}
              map={props.map}
              activeTool={activeTool}
              closeToolsData={closeToolsData}
              openToolsData={openToolsData}
              openToolData={openToolData}
              selectedFeatures={selectedFeatures}
              Cleanup={Cleanup}
              onClose={() => setShowUpdateLand(false)}
            />
          )}
        </div>
      </Fade>
      <Fade top delay={500}>
        <div
          className={
            openToolservice
              ? "openedToolsMenu toolsHelp"
              : "closedToolsMenu toolsHelp"
          }
        >
          <ul>
            {tools.map((tool, index) => {
              console.log(
                "---------openToolData && String(activeTool) === String(tool.name)",
                openToolData && String(activeTool) === String(tool.name)
              );

              return (
                <>
                  {tool.name !== "openHelp" ? (
                    tool.name === "selectToUpdate" ? (
                      selectedFeatures?.length > 0 && (
                        <Tooltip
                          title={t(tool.tooltip)}
                          placement="right"
                          key={index + "tright"}
                        >
                          <li
                            onClick={(event) => {
                              openToolsData(event, tool.name);
                            }}
                            name={tool.name}
                            id={openToolservice ? "openedToolsMenuLi" : ""}
                            className={`serviceButton ${
                              String(activeTool) === String(tool.name)
                                ? "activeService"
                                : ""
                            }`.trim()}
                          >
                            {tool.icon ? (
                              // <tool.icon
                              //   className="hashTest"
                              //   id={tool.id}
                              //   name={tool.name}
                              // />
                              <img src={tool.icon} alt="" />
                            ) : (
                              <tool.fallbackIcon
                                className="hashTest"
                                id={tool.id}
                                name={tool.name}
                              />
                            )}
                          </li>
                        </Tooltip>
                      )
                    ) : (
                      <Tooltip
                        title={t(tool.tooltip || "Default Tooltip")}
                        placement="right"
                        key={`${index}-right`}
                        className="darksouls"
                        slotProps={{
                          popper: {
                            modifiers: [
                              {
                                name: "offset",
                                options: {
                                  // offset: [0, 10],
                                },
                              },
                            ],
                          },
                          tooltip: {
                            sx: {
                              bgcolor: "#111", // Tooltip background
                              color: "#ffff", // Tooltip text color
                              fontSize: "14px",
                              // padding: "8px 12px",
                              borderRadius: "8px",
                              boxShadow: 3,
                              background: "#338C9A",
                              fontWeight: "400",
                            },
                          },
                        }}
                      >
                        <li
                          onClick={(event) => openToolsData?.(event, tool.name)}
                          name={tool.name}
                          id={openToolservice ? "openedToolsMenuLi" : ""}
                          className={`serviceButton ${
                            String(activeTool) === String(tool.name)
                              ? "activeService"
                              : ""
                          }`.trim()}
                        >
                          {tool.icon ? (
                            <img
                              className="openedservicesMenuImg"
                              src={tool.icon}
                              alt={tool.name || "toolsIcon"}
                              id={tool.id || `tool-${index}`}
                              name={tool.name}
                              onError={(e) =>
                                (e.target.src = "/path/to/fallback-image.png")
                              } // Fallback image
                            />
                          ) : (
                            tool.fallbackIcon && (
                              <tool.fallbackIcon
                                className="hashTest"
                                id={tool.id || `tool-${index}`}
                                name={tool.name}
                              />
                            )
                          )}
                        </li>
                      </Tooltip>
                    )
                  ) : (
                    <Tooltip
                      key={index + "t"}
                      title={t(tool.tooltip)}
                      placement="right"
                    >
                      <li
                        onClick={openHelp}
                        // id={tool.id}
                        name={tool.name}
                        id={openToolservice ? "openedToolsMenuLi" : ""}
                        className={
                          String(activeTool) === String(tool.name)
                            ? "activeService "
                            : ""
                        }
                      >
                        <img
                          className="openedservicesMenuImg"
                          src={tool.icon}
                          alt="toolsIcon"
                          id={tool.id}
                          name={tool.name}
                        />
                      </li>
                    </Tooltip>
                  )}

                  {}
                  {openToolData && String(activeTool) === String(tool.name) ? (
                    <AllTools
                      languageState={props.languageState}
                      mainData={props.mainData}
                      setPopupInfo={props.setPopupInfo}
                      popupInfo={props.popupInfo}
                      activeTool={tool.name}
                      map={props.map}
                      closeToolsData={closeToolsData}
                      openToolsData={openToolsData}
                      setSketch={setSketch}
                      sketch={sketch}
                      openToolData={openToolData}
                      selectedFeatures={selectedFeatures}
                      setSelectedFeatures={setSelectedFeatures}
                    />
                  ) : null}
                </>
              );
            })}
          </ul>
        </div>
      </Fade>
      {openServSearch ? (
        <ServicesSearch
          mainData={props.mainData}
          outerResultMenuShown={props.outerResultMenuShown}
          outerOpenResultMenu={props.outerOpenResultMenu}
          handleDrawerOpen={props.handleDrawerOpen}
          setFilteredResult={props.setFilteredResult}
          activeService={activeServiceItem}
          map={props.map}
          closeServiceSearch={closeServiceSearch}
        />
      ) : null}
    </div>
  );
}
