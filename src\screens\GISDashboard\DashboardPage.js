import React, { useState, useEffect } from "react";
import moment from "moment-hijri";
import { useTranslation } from "react-i18next";
import Loader from "../../containers/Loader";
import MapComponent from "../../mapComponents/Map";
import DashHeader from "./DashboardHeader/index";
import SideTbls from "./DashboardTbls/SideTbls";
import LeftSideChartsContainer from "./dashboardCharts/LeftChartsComp/LeftSideChartsContainer";
import { getLayerId,queryTask ,queryChartData} from "../../helper/common_func";
import {
  getCountPerTimeContext,
  getDefaultStatistics,
  getDomainValuesForDashboard,
} from "./helpers/helperFunc";
import { notificationMessage } from "../../helper/utilsFunc";
import { modulesIDs } from "../../helper/constants";

function GISNewDashboardPage(props) {
  const { i18n, t } = useTranslation("dashboard", "common", "print");
  const [chartsLayersData, setChartsLayersData] = React.useState([
    {
      layerName: "GOV_boundary",
      outFields: ["OBJECTID"],
      title: "munName",
      type: "count",
      shownData: ['count'],
      dependentFields: [],
      chartName:"عدد المحافظات",
      position: "top"
    },
    {
      layerName: "GOV_boundary",
      outFields: ["POPULATION2022"],
      title: "munName",
      type: "sum",
      shownData: ['sum'],
      dependentFields: ["POPULATION2022"],
      chartName:"عدد  سكان المحافظات",
      position: "bottom"
    },
    {
      layerName: "Municipality_Boundary",
      name: "MUNICIPALITY_NAME",
      outFields: ["MUNICIPALITY_NAME"],
      title: "munName",
      type: "count",
      shownData: ['count'],
      dependentFields: [],
      chartName:"عدد البلديات",
      position: "top"
    },
    // {
    //   layerName: "Landbase_Parcel",
    //   name: "Landbase_Parcel",
    //   title: "Landbase_Parcel",
    //   outFields: ["Landbase_Parcel"],
    //   type: "count",
    //   shownData: ['count'],
    //   dependentFields: ['MUNICIPALITY_NAME'],
    //   chartName:"عدد الاراضي",
    //   position: "top"
    // },
    {
      name: "PLAN_CLASS",
      outFields: ["PLAN_CLASS"],
      title: "planClass",
      type: "count",
      position: "top",
      shownData: ['count'],
      layerName: "Plan_Data",
      dependentFields: ['MUNICIPALITY_NAME'],
      chartName:"عدد ملكيات المخططات الحكومي",
      restrictionWhereClause: "PLAN_CLASS = 2",
    },

    {
      name: "PLAN_CLASS",
      outFields: ["PLAN_CLASS"],
      title: "planClass",
      type: "count",
      position: "top",
      shownData: ['count'],
      layerName: "Plan_Data",
      dependentFields: ['MUNICIPALITY_NAME'],
      chartName:"عدد ملكيات المخططات الخاص",
      restrictionWhereClause: "PLAN_CLASS = 1",
    },
    {
      layerName: "Invest_Site_Polygon",
      name: "PARCEL_SPATIAL_ID",
      outFields: ["MUNICIPALITY_NAME"],
      title: "munName",
      type: "count",
      shownData: ['count'],
      dependentFields: ['MUNICIPALITY_NAME'],
      position:"top",
      chartName:"عدد المواقع الاستثمارية",

    },

    // {
    //   layerName: "Landbase_Parcel",
    //   name: "MUNICIPALITY_NAME",
    //   outFields: ["MUNICIPALITY_NAME"],
    //   title: "munName",
    //   type: "bar",
    //   shownData: ['count'],
    //   dependentFields: ['MUNICIPALITY_NAME'],
    //   position: "top"
    // },

    // {
    //   layerName: "Landbase_Parcel",
    //   name: "MUNICIPALITY_NAME",
    //   outFields: ["MUNICIPALITY_NAME"],
    //   title: "munName",
    //   type: "bar",
    //   shownData: ['count'],
    //   dependentFields: ['MUNICIPALITY_NAME'],
    //   position: "top"
    // },
    // {
    //   layerName: "Municipality_Boundary",
    //   name: "MUNICIPALITY_NAME",
    //   outFields: ["MUNICIPALITY_NAME"],
    //   title: "munName",
    //   type: "count",
    //   shownData: ['count'],
    //   dependentFields: [],
    //   position: "top"
    // },

    {
      layerName: "MuncipiltiesBuiltArea",
      outFields: ["SHAPE.AREA"],
      title: "munName",
      type: "sum",
      shownData: ['sum'],
      dependentFields: ['SHAPE.AREA'],
      sumField :["SHAPE_AREA"],
      position: "bottom"
    },
     {
      layerName: "Landbase_Parcel",
      name: "PARCEL_MAIN_LUSE",
      outFields: ["PARCEL_MAIN_LUSE"],
      title: "munName",
      type: "count",
      shownData: ['count'],
      dependentFields: ['MUNICIPALITY_NAME', 'PLAN_NO', 'DISTRICT_NAME'],
      position: "left",
      subPosition: 'top'
    },
    {
      layerName: "Landbase_Parcel",
      name: "PARCEL_MAIN_LUSE",
      outFields: ["PARCEL_MAIN_LUSE"],
      title: "munName",
      type: "count",
      shownData: ['count'],
      dependentFields: ['MUNICIPALITY_NAME', 'PLAN_NO', 'DISTRICT_NAME'],
      position: "left",
      subPosition: 'top'
    },
   
    {
      layerName: "Landbase_Parcel",
      name: "PARCEL_MAIN_LUSE",
      outFields: ["PARCEL_MAIN_LUSE"],
      title: "munName",
      type: "count",
      shownData: ['count'],
      dependentFields: ['MUNICIPALITY_NAME', 'PLAN_NO', 'DISTRICT_NAME'],
      position: "left",
      subPosition: 'top'
    },
   
    
  ])
  const [chartCounts, setChartCounts] = useState({
    topCounts: [],
    bottomCounts: [],
    leftCounts: []
  });
  const [loading, setLoading] = useState(false);
  const [map, setMap] = useState();
  const [mapTablesShow, setMapTablesShow] = useState(false);
  const [hasAccessToPage, setHasAccessToPage] = useState(false);
  const [layersNames, setLayersNames] = useState([]);
  const [queryData, setQueryData] = useState({
    selectedLayer: props.isIncident ? "incidents940" : "Landbase_Parcel",
    selectedBoundaryType: {
      value: undefined,
      boundariesArr: [],
      selectedBoundary: null,
      preNeededBound: {
        value: "MUNICIPALITY_NAME",
        preNeededBoundariesArr: [],
        selectedPreNeededBoundary: null,
      },
    },
    selectedTimeContext: {
      type: undefined,
      dateData: [],
    },
  });
    const dateDataRef = React.useRef();
    const [isModalOpen, setIsModalOpen] = useState(null);
  
    // const navigate = useNavigate();
    const [activeHeatMapFactor, setActiveHeatMapFactor] = useState("");
    // const [routeName, setNavRouteName] = useState("plans");
    const [leftChartsExist, setLeftChartsExist] = useState(false);
 
  const [chartsData, setChartsData] = useState({
    count: undefined,
    areaOrLength: {
      value: undefined,
      type: undefined,
    },
    countPerPeriod: undefined,
    // restChartData: [],    //initial total value
    geoType: undefined, // to check if there is area of not if geoType === polygon
  });

   useEffect(() => {
      if (!props.mainData.logged)
        window.open(window.hostURL + "/home/<USER>", "_self");
      else {
        if (map) {
          let isUserHasPermission = props.mainData?.mainFunctions?.find((gr) =>
            gr?.groups_permissions?.find(
              (gp) => gp?.module_id === modulesIDs.incidentsModule
            )
          );
  
          if (!isUserHasPermission) {
            window.open(window.hostURL + "/home/<USER>", "_self");
          } else setHasAccessToPage(true);
          window.__moment__ = moment;
          //intialize layersNames
          let mapAllLayers = map.__mapInfo.info.$layers.layers;
          // let mapAllTbls = props.map.__mapInfo.info.$layers.tables;
          let layersSetting = props.mainData.layers;
          let layersNames = Object.entries(layersSetting)
            ?.filter((l) => {
              let mapLayerNames = mapAllLayers.map((lay) => lay.name);
              if (
                mapLayerNames.includes(l[1].englishName) &&
                !l[1].isHiddenOnDashboard
              )
                return l;
              else return undefined;
            })
            ?.map((l) => {
              let mapLayer = mapAllLayers.find((lay) => lay.name === l[0]);
              return {
                englishName: l[0],
                arabicName: l[1].arabicName,
                geoType: mapLayer.geometryType,
                statistics: l[1]?.statistics,
                dashboardCharts: l[1]?.dashboardCharts,
                fields: l[1]?.fields,
                restrictionWhereClause: l[1]?.restrictionWhereClause || undefined,
              };
            });
          let params = {
            changeType: layersNames,
            getTotalData: true,
            boundAdminType: null,
            callBack: handlerToSetQueryDChartsD,
            changedVal: null,
          };
        
          /////////////
          setLayersNames(layersNames);
          getDefaultData(params);
        }
        // else {
        //   window.open(window.hostURL + "/home/<USER>", '_self');
        // }
      }
      return () => null;
    }, [map, props.mainData]);
    useEffect(() => {
      // Check login status
      if (!props.mainData.logged) {
        window.open(window.hostURL + "/home/<USER>", "_self");
        return;
      }
    
      // Fetch chart data when map is loaded
      if (map) {
        fetchChartData(map);
      }
    
      // Cleanup function
      return () => {
        // Any cleanup logic if needed
      };
    }, [map, props.mainData]);
    
  
  const [sideTblData, setSideTblData] = useState({
    title: "",
    data: [],
  });
  const onMapLoaded = (map) => {
    setMap(map);
  };
const getDefaultData = async ({
    changeType,
    getTotalData,
    boundAdminType,
    callBack,
    changedVal,
    munDomains,
    wClause,
    preserveTypeContext
  }) => {
    let layersNs = typeof changeType === "object" ? changeType : layersNames;
    let currentLayer = layersNs.find((lay) => {
      let layerName =
        changedVal && changeType === "layer"
          ? changedVal
          : queryData.selectedLayer;
      return lay?.englishName === layerName;
    });
    if (currentLayer) {
      let { englishName, statistics, geoType, restrictionWhereClause } =
        currentLayer;
      let layerID = getLayerId(map.__mapInfo, englishName);
      let whereClause = restrictionWhereClause;
      if (whereClause && wClause) {
        if (wClause) whereClause = whereClause + " AND " + wClause;
      } else if (wClause) {
        whereClause = wClause;
      }
      let layerObj = {
        layerID,
        statistics,
        boundAdminType,
        getTotalData,
        restrictionWhereClause: whereClause,
      };
      try {
        setLoading(true);
        //count + area (parts)
        let statData =
          changeType === "calendar"
            ? {
                count: chartsData.count,
                areaOrLength: chartsData.areaOrLength,
              }
            : await getDefaultStatistics(layerObj); //count, area or only count based on layer's statistics
        // cout per time (middle part belopw map)
        let yearlyData;
        if (getTotalData)
          yearlyData = await getCountPerTimeContext(
            { layerID, restrictionWhereClause: whereClause },
            "yearly",
            undefined
          );
        callBack(
          {
            statData,
            yearlyData,
          },
          getTotalData,
          geoType,
          changeType,
          changedVal,
          boundAdminType,
          munDomains,
          preserveTypeContext
        );
      } catch (err) {
        notificationMessage(t("common:retrievError"));
        setLoading(false);
      }
    } else {
      notificationMessage(t("common:retrievError"));
      setLoading(false);
    }
  };
  const fetchChartData = async (map) => {
    if (!map) return;
  
    try {
      setLoading(true);
      const topCounts = [], bottomCounts = [], leftCounts = [];
  
      const updatedChartsLayersData = await Promise.all(
        chartsLayersData.map(async (layerConfig) => {
          try {
            const layerUrl = `${window.mapUrl}/${getLayerId(map.__mapInfo, layerConfig.layerName)}`;
            const data = await queryChartData({
              url: layerUrl,
              layerName: layerConfig.layerName,
              outFields: layerConfig.outFields,
              chartType: layerConfig.type,
              chartName: layerConfig.chartName,
              where: layerConfig.restrictionWhereClause || "1=1",
              dependentFields: layerConfig.dependentFields ,// Pass dependentFields
              sumField: layerConfig.sumField,
            });
  
            // Determine countValue based on type
            let countValue;
            if (layerConfig.type === 'count') {
              countValue = data;
            } else if (layerConfig.type === 'sum') {
              countValue = data; // data is the sum value from query
            } else {
              countValue = Array.isArray(data) ? data.length : 0;
            }
  
            // Push to respective position arrays
            const countObj = {
              layerName: layerConfig.layerName,
              name: layerConfig.name,
              type: layerConfig.type,
              chartName: layerConfig.chartName,
              count: countValue
            };
            switch (layerConfig.position) {
              case 'top': topCounts.push(countObj); break;
              case 'bottom': bottomCounts.push(countObj); break;
              case 'left': leftCounts.push(countObj); break;
            }
  
            return { ...layerConfig, data };
          } catch (error) {
            console.error(`Error in ${layerConfig.layerName}:`, error);
            return layerConfig;
          }
        })
      );
  
      setChartsLayersData(updatedChartsLayersData);
      setChartCounts({ topCounts, bottomCounts, leftCounts });
      setLoading(false);
    } catch (error) {
      console.error("Fetch error:", error);
      setLoading(false);
    }
  };

const handlerToSetQueryDChartsD = (
  data,
  getTotalData,
  geoType,
  changeType,
  changedVal,
  boundAdminType,
  munDomains,
  preserveTypeContext
) => {
  let { statData, yearlyData } = data;
  let queryD = getQueryDataBasedOnStatData(
    statData,
    boundAdminType,
    getTotalData,
    changeType,
    preserveTypeContext
  );
  switch (changeType) {
    case "layer":
      queryD.selectedLayer = changedVal;
      break;
    case "boundAdmin":
      queryD.selectedBoundaryType.value = changedVal;
      break;
    case "subBound":
      queryD.selectedBoundaryType.selectedBoundary = changedVal;
      break;
    case "preSubBound":
      if (munDomains) {
        queryD.selectedBoundaryType.selectedBoundary = undefined;
        queryD.selectedBoundaryType.boundariesArr = [];
        queryD.selectedBoundaryType.value = "DISTRICT_NAME";
        queryD.selectedBoundaryType.preNeededBound.preNeededBoundariesArr =
          munDomains;
        queryD.selectedBoundaryType.preNeededBound.selectedPreNeededBoundary =
          undefined;
      } else {
        queryD.selectedBoundaryType.preNeededBound.selectedPreNeededBoundary =
          changedVal;
        if (!changedVal) queryD.selectedBoundaryType.boundariesArr = [];
      }
      break;
    default:
      break;
  }
  setQueryData({ ...queryD });
  if (yearlyData) {
    let chartD = getChartsDataBasedOnStatData(yearlyData);
    setChartsData({
      ...chartD,
      ...statData,
      geoType,
    });
  } else {
    setChartsData({
      ...chartsData,
      ...statData,
      geoType,
    });
  }
  setLoading(false);
};

const getQueryDataBasedOnStatData = (
  statData,
  boundAdminType,
  getTotalData,
  changeType,
  preserveTypeContext
) => {
  let queryDataClone = { ...queryData };
  //if there is a count data array
  if (typeof statData.count === "object" && boundAdminType) {
    queryDataClone.selectedBoundaryType = {
      ...queryDataClone.selectedBoundaryType,
      selectedBoundary:
        queryData.selectedBoundaryType.selectedBoundary !== null
          ? undefined
          : null,
      boundariesArr: statData.count
        .filter((item) => item[boundAdminType])
        .map((item) => {
          return {
            name: item[boundAdminType],
            value:
              boundAdminType === "PLAN_NO"
                ? item[boundAdminType]
                : item[boundAdminType + "_Code"],
          };
        }),
    };
    if (getTotalData)
      queryDataClone.selectedTimeContext = {
        type: queryDataClone.selectedTimeContext?.type || undefined,
        dateData: [],
      };
  } else {
    queryDataClone.selectedBoundaryType = {
      ...queryDataClone.selectedBoundaryType,
      selectedBoundary: undefined,
      boundariesArr: [],
      preNeededBound: {
        ...queryDataClone.selectedBoundaryType.preNeededBound,
        preNeededBoundariesArr: [],
        selectedPreNeededBoundary: undefined,
      },
    };
    if (changeType === "layer")
      queryDataClone.selectedBoundaryType.value = undefined;
    if (getTotalData && !preserveTypeContext)
      queryDataClone.selectedTimeContext = {
        type: undefined,
        dateData: [],
      };
      else if(getTotalData && preserveTypeContext){
        queryDataClone.selectedTimeContext = {
          ...queryDataClone.selectedTimeContext,
          dateData: [],
        };
      }
  }
  return queryDataClone;
};

const getChartsDataBasedOnStatData = (yearlyData) => {
  let { data } = yearlyData;

  return {
    ...chartsData,
    countPerPeriod: {
      type: "yearly",
      data,
      timePeriod: undefined,
      default: true,
    },
  };
};
// Render the dashboard
  const [dropNavOpened, setDropNavOpened] = useState("headerHidden");
  const openNavDrop = () => {
    setDropNavOpened("headerShown");
  };
  const closeNavDrop = () => {
    setDropNavOpened("headerHidden");
  };
   const handleAdminBoundChange = async (
      adminBoundary,
      preNeededBoundWhereClause
    ) => {
      let params = {
        changeType: "boundAdmin",
        getTotalData: false,
        boundAdminType: null,
        callBack: handlerToSetQueryDChartsD,
        changedVal: adminBoundary,
      };
      if (!adminBoundary) {
        if (activeHeatMapFactor) setActiveHeatMapFactor("");
  
        if (!chartsData.count) return;
        else if (typeof chartsData.count === "object") {
          //reset sub boundary array to hide from UI
          if (queryData.selectedTimeContext.dateData.length) {
            params.changeType = "boundAdmin";
            if (preNeededBoundWhereClause) {
              params.wClause =
                typeof preNeededBoundWhereClause === "string"
                  ? preNeededBoundWhereClause
                  : "";
              params.boundAdminType = queryData.selectedBoundaryType.value;
              params.changeType = "preSubBound";
            }
            getDefaultData(params);
          } else {
            params.changeType = "boundAdmin";
            params.getTotalData = chartsData.countPerPeriod?.default
              ? false
              : true;
            if (preNeededBoundWhereClause) {
              params.wClause =
                typeof preNeededBoundWhereClause === "string"
                  ? preNeededBoundWhereClause
                  : "";
              params.boundAdminType = queryData.selectedBoundaryType.value;
              params.changeType = "preSubBound";
            }
            getDefaultData(params);
          }
        } else {
          params.changeType = "boundAdmin";
          params.getTotalData = chartsData.countPerPeriod?.default ? false : true;
          getDefaultData(params);
        }
      } else {
        if (adminBoundary && preNeededBoundWhereClause) {
          setActiveHeatMapFactor("count"); //count is default for drawing heat map
          // params.boundAdminType = adminBoundary;
          if (preNeededBoundWhereClause) {
            params.wClause =
              typeof preNeededBoundWhereClause === "string"
                ? preNeededBoundWhereClause
                : "";
            params.boundAdminType = queryData.selectedBoundaryType.value;
            params.changeType = "preSubBound";
          }
          getDefaultData(params);
        } else if (adminBoundary === "DISTRICT_NAME") {
          if (activeHeatMapFactor) setActiveHeatMapFactor("");
  
          let currentLayer = layersNames.find(
            (lay) => lay.englishName === queryData.selectedLayer
          );
          let layerID = getLayerId(map.__mapInfo, currentLayer.englishName);
          setLoading(true);
  
          let munDomains = await getDomainValuesForDashboard({
            layerID,
            boundAdminType: queryData.selectedBoundaryType.preNeededBound.value,
          });
          setLoading(false);
  
          let params = {
            changeType: "preSubBound",
            getTotalData: false,
            boundAdminType: null,
            callBack: handlerToSetQueryDChartsD,
            changedVal: queryData.selectedLayer,
            munDomains: munDomains?.data || [],
          };
  
          getDefaultData(params);
        } else {
          setActiveHeatMapFactor("count"); //count is default for drawing heat map
          params.boundAdminType = adminBoundary;
          getDefaultData(params);
        }
      }
    };
    const handleLayerChange = (layerName) => {
      if (mapTablesShow) {
        setSideTblData();
        setMapTablesShow(false);
      }
      //set is there left charts for this layer or not
      let isLedtChartsExist = layersNames?.find(
        (l) => l.englishName === layerName
      )?.dashboardCharts;
      if (isLedtChartsExist?.length) setLeftChartsExist(true);
      else if (leftChartsExist) setLeftChartsExist(false);
      //it should reset all dropdown lists and
      //just display the default data (count, area, line bar with count per years),
      //reset select of max 6, min-6 and all in count, area pie charts to be max-6
      //reset select of max-6
      let getTotalData = true;
      let params = {
        changeType: "layer",
        getTotalData,
        boundAdminType: null,
        callBack: handlerToSetQueryDChartsD,
        changedVal: layerName,
      };
  
      getDefaultData(params);
    };
    const handleChangeSubBoundary = async (subBound) => {
      let { selectedBoundaryType } = queryData;
      let currentLayer = layersNames.find(
        (lay) => lay.englishName === queryData.selectedLayer
      );
      let layerID = getLayerId(map.__mapInfo, currentLayer.englishName);
      if (subBound && selectedBoundaryType.boundariesArr.length) {
        //get data of sub boundary
        let boundAdminType = selectedBoundaryType.value;
        await getCountDataSubBound(
          layerID,
          subBound,
          boundAdminType,
          selectedBoundaryType
        );
      } else if (
        subBound === undefined &&
        selectedBoundaryType.boundariesArr.length
      ) {
        let params = {
          changeType: "subBound",
          getTotalData: chartsData.countPerPeriod?.default ? true : false,
          boundAdminType: selectedBoundaryType.value,
          callBack: handlerToSetQueryDChartsD,
          changedVal: subBound,
        };
        getDefaultData(params);
      }
    };
      const getCountDataSubBound = async (
        layerID,
        subBoundary,
        boundAdminType,
        selectedBoundaryType,
        isDateCleared
      ) => {
        setLoading(true);
        try {
          let whereStatement = "";
          switch (boundAdminType) {
            case "PLAN_NO":
              whereStatement = subBoundary
                ? `${boundAdminType} = '${subBoundary}'`
                : "";
              break;
            case "DISTRICT_NAME":
              whereStatement = subBoundary
                ? `${boundAdminType} = '${subBoundary}'`
                : "";
              if (
                selectedBoundaryType?.preNeededBound?.value &&
                selectedBoundaryType?.preNeededBound?.selectedPreNeededBoundary
              )
                whereStatement = whereStatement
                  ? whereStatement +` AND ${selectedBoundaryType?.preNeededBound?.value} = ${selectedBoundaryType?.preNeededBound?.selectedPreNeededBoundary}`
                  : whereStatement;
              break;
    
            default:
              whereStatement = subBoundary
                ? `${boundAdminType} = '${subBoundary}'`
                : "";
              break;
          }
    
          let currentLayer = layersNames.find(
            (lay) => lay.englishName === queryData.selectedLayer
          );
    
          //getStatisticsForFeatLayer
          let data = await getCountPerTimeContext(
            {
              layerID,
              restrictionWhereClause: currentLayer?.restrictionWhereClause,
            },
    
            (queryData.selectedTimeContext.type && !isDateCleared)
              ? queryData.selectedTimeContext.type
              : "yearly",
            null,
            whereStatement
          );
          if (data) {
            setChartsData({
              ...chartsData,
              countPerPeriod: {
                ...chartsData.countPerPeriod,
                data: data.data,
                default: true,
              },
            });
            setLoading(false);
          }
        } catch (err) {
          console.log(err);
          notificationMessage(t("common:retrievError"));
          setLoading(false);
        }
      };
return (
  <div className="dashboardPage">
    {loading && <Loader />}

    <DashHeader
      dropNavOpened={dropNavOpened}
      openNavDrop={openNavDrop}
      closeNavDrop={closeNavDrop}
      defaultKey="eastern"
      dash
      languageStatus={i18n.language}
      queryData={queryData}
      setQueryData={setQueryData}
      handleAdminBoundChange={handleAdminBoundChange}
      handleChangeSubBoundary={handleChangeSubBoundary}
      map={map}
    />
    <div
      className="gis-dashboard-page-layout dashboard-page-layout no-print"
     >
      {/**on right: (map+2tbls) 74vw [50vw, 24vw], on left: charts 26vw[13vw+13vw] */}
      {mapTablesShow && sideTblData.title ? (
        <SideTbls
          data={sideTblData}
    //      closeSideTblHandler={closeSideTblHandler}
        />
      ) : null}
      {/**Map Part */}
      {/* <ResizableBox
        width={200}
        height={200}
        minConstraints={[100, 100]}
        maxConstraints={[300, 300]}> */}
      <div
        className="map-wrapper"
        style={{
          width: mapTablesShow ? "50vw" : "74vw",
        }}>
        <div className="top-section" style={{ height: '20%', display: 'flex', flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
        {chartCounts.topCounts.map((chart, index) => (
            <div key={index} className="mapSquare">
              <div>{chart.layerName }</div>
              <div>{chart.chartName}</div>
              <div>{chart.count}</div>
            </div>
          ))}

        </div>
        {/**Map Container */}
        <div
          className="dashMap"
        // id={mapTablesShow ? "dashMapHeight" : "dashMapHeightDefault"}
        >
          <MapComponent
            mapload={onMapLoaded}
            mainData={props.mainData}
            currentLayer={queryData.selectedLayer}
            isDashboard={true}
          />
        </div>
        {/**End Map Container */}
        {/**Charts and Sum containers below map */}
        <div className="charts-below-map">
        {chartCounts.bottomCounts.map((chart, index) => (
            <div key={index} className="mapSquare">
              <div>{chart.layerName}</div>
              <div>{chart.chartName}</div>
              <div>{chart.count}</div>
            </div>
          ))}



        </div>
        {/**End Charts and Sum containers below map */}
      </div>
      {/* </ResizableBox> */}
      {/*************** */}
      {/**Left side Charts */}
     {/* {map &&  <LeftSideChartsContainer
          map={map}
          chartsDataArr={chartsLayersData.filter(chart => chart.position==='left')}
        //  changeMapTablesShow={changeMapTablesShow}
        layersNames={layersNames}
        selectedLayer={queryData.selectedLayer}
        mapTablesShow={mapTablesShow}
        queryData={queryData}
        />} */}
           {layersNames.length && queryData?.selectedLayer && map ? (
          <LeftSideChartsContainer
            layersNames={layersNames}
            map={map}
            selectedLayers={["Landbase_Parcel"]}
          //  changeMapTablesShow={changeMapTablesShow}
        //  chartsDataArr={chartsLayersData.filter(chart => chart.position==='left')}
            mapTablesShow={mapTablesShow}
            queryData={queryData}
          />
        ) : null}
        
      {/* <div className="left-section">
        {chartsLayersData.filter(chart => chart.position === 'left').map(chart => <div className="mapSquare">
          <div>
            <h5>Title</h5>
            <div>
              Chart
            </div>
          </div>
        </div>)}
      </div> */}
      {/**End Left side Charts */}
    </div>
    {/**Details Table on the bottom of the page */}
    {/* <div className="routesData">
      <DataTable
        boundAdmin={queryData.selectedBoundaryType.preNeededBound}
        isIncident={props.isIncident}
        chartsData={chartsData}
        title={queryData.selectedBoundaryType.value}
        hasArea={chartsData.areaOrLength.value}
      />
    </div> */}
    {/**End Details Table on the bottom of the page */}
  </div>
);
}

export default GISNewDashboardPage;