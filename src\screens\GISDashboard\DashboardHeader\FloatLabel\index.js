import React, { useState } from "react";

import Style from "./style.module.css";

const FloatInput = (props) => {
  const [focus, setFocus] = useState(false);
  let { label, value, placeholder, children } = props;

  if (!placeholder) placeholder = label;

  const isOccupied = focus || value;

  const labelClass = isOccupied ? [Style.customLabel, Style.asCustomLabel]:[Style.customLabel,Style.asPlaceholder];


  return (
    <div
      className={Style.floatCustomLabel}
      onBlur={() => setFocus(false)}
      onFocus={() => setFocus(true)}
    >
      <label className={labelClass.join(" ")}>
        {isOccupied ? label : placeholder}
      </label>
     {children}
    </div>
  );
};

export default FloatInput;
