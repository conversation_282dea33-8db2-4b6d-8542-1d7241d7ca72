import React from "react";
import ReactExport from "react-export-excel";
import { useTranslation } from "react-i18next";

const ExcelFile = ReactExport.ExcelFile;
const ExcelSheet = ReactExport.ExcelFile.ExcelSheet;
const ExcelColumn = ReactExport.ExcelFile.ExcelColumn;

function ExportCSV({ dataSet, columns, labels, layerName, isForceClick,setExportedData }) {
  const [t] = useTranslation("common");
  const downloadExcelRef = React.useRef();
  React.useEffect(()=>{
    if(dataSet?.length && isForceClick){
      if(setExportedData) setExportedData({
        dataSet: [], columns: [], labels: [], layerName: '', whereClause: ''
      })
      downloadExcelRef?.current?.click();
    }
  },[dataSet])
console.log(dataSet, columns, labels, layerName);
  return (
    <ExcelFile
      filename={layerName + "CSV"}
      element={
        <span ref={downloadExcelRef}>
          {/* <Tooltip placement="topLeft" title={` استخراج ملف CSV `}> */}
         
          {t("extractExcelFile")}
          {/* </Tooltip> */}
        </span>
      }
    >
      <ExcelSheet data={dataSet} name="AttributeTable">
        {labels.map((head, index) => (
          <ExcelColumn
            label={head}
            value={(col) => {
              return col[columns[index]] ? col[columns[index]] : t("without");
            }}
          />
        ))}
      </ExcelSheet>
    </ExcelFile>
  );
}

export default ExportCSV;
