import React, { useEffect, useState } from 'react';
import ReactApexChart from 'react-apexcharts';
import { useTranslation } from "react-i18next";

function BarChartComp(props) {
  const { i18n, t } = useTranslation("dashboard");
  const [series, setSeries] = useState([]);
  const [options, setOptions] = useState({});
  useEffect(() => {

    console.log(i18n.language, t("dashboard:menu"));
    setSeries([{
      name: props.title,
      data: props.data?.map(i => i.size)
    }]);
    setOptions({
      colors: props.data?.map(i => i.color),

      chart: {
        defaultLocale: i18n.language === "ar" ? "ar" : "en",
        locales: [
          {
            name: i18n.language === "ar" ? "ar" : "en",
            options: {
              toolbar: {
                exportToSVG: t("dashboard:exportToSVG"),
                exportToPNG: t("dashboard:exportToPNG"),
                exportToCSV: t("dashboard:exportToCSV"),
                menu: t("dashboard:menu"),
                selection: t("dashboard:selection"),
                selectionZoom: t("dashboard:selectionZoom"),
                download: t("dashboard:downloadSVG"),
                zoomIn: t("dashboard:zoomIn"),
                zoomOut: t("dashboard:zoomOut"),
                pan: t("dashboard:pan"),
                reset: t("dashboard:reset"),
              },
            },
          },
        ],
        toolbar: {
          tools: {
            download: true,
          },
          export: {
            csv: {
              filename: props.title,
              columnDelimiter: ",",
              headerCategory: props.title,
              headerValue: t("dashboard:count"),
              dateFormatter(timestamp) {
                return new Date(timestamp).toDateString();
              },
            },
            svg: {
              filename: props.title,
            },
            png: {
              filename: props.title,
            },
          },
        },
        id: "basic-bar",
        height: "100%",
      },
      legend: {
        show: false,
        fontWeight: 800,
        onItemClick: {
          toggleDataSeries: true
        },
        onItemHover: {
          highlightDataSeries: true
        },
        labels: {
          colors: ["#fffff"],
          useSeriesColors: false
        },
      },

      plotOptions: {
        bar: {
          // dataLabels: {
          //     position: 'bottom', // top, center, bottom
          // },

          horizontal: false,
          distributed: true,
          dataLabels: {
            position: "top",
          },
          columnWidth: "90%",
        },
      },
      dataLabels: {
        enabled: true,
        formatter: function (val) {
          return val;
        },
        offsetY: -20,
        style: {
          fontSize: '14px',
          fontFamily: 'arabicNums'
          // colors: ["#304758"]
        }
      },
      xaxis: {
        categories: props.data?.map(i => i.name),
        position: 'bottom',
        labels: {
          show: false,
          // formatter: function (val) {
          //     return val;
          // }
        },
        axisBorder: {
          show: false
        },
        axisTicks: {
          show: false
        },
        crosshairs: {
          fill: {
            type: 'gradient',
            gradient: {
              colorFrom: '#D8E3F0',
              colorTo: '#BED1E6',
              stops: [0, 100],
              opacityFrom: 0.4,
              opacityTo: 0.5,
            }
          }
        },
        tooltip: {
          enabled: true,
        }
      },
      yaxis: {
        axisBorder: {
          show: false
        },
        axisTicks: {
          show: false,
        },
        labels: {
          show: false,
          // formatter: function (val) {
          //     return val;
          // }
        }
      },
      title: {
        show: false,
        text: '',
        floating: true,
        offsetY: 330,
        align: 'center',
        style: {
          color: '#444',
          fontFamily: 'arabicNums'
        }
      },
      tooltip: {

        shared: true,
        intersect: false,
        onDatasetHover: {
          highlightDataSeries: true,
        },
        items: {
          display: 'flex',
        },
        fixed: {
          enabled: true,
          position: 'topRight',
          offsetX: 0,
          offsetY: 0,
          //fontFamily:'arabicNums'
        },
        style: {
          fontSize: '12px',
          fontFamily: 'arabicNums',
          textAlign:'center',
          direction:'rtl'
        },
        y: {
          formatter: undefined,
          title: {
              formatter: (seriesName) => `${t('dashboard:count')}: `,
          },
      },
      }
    }
    )
  }, [])



  return (

    <div id="Columnchart1">
      <ReactApexChart options={options} series={series} type="bar"
      // height={props.height||200}
      // width={props.width||200}
      />
    </div>

  );
}

export default BarChartComp;