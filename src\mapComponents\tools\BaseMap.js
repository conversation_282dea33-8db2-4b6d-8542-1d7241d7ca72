import Fade from "react-reveal/Fade";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTimes } from "@fortawesome/free-solid-svg-icons";
import img from "../../assets/images/Frame 26080043.png";
import BasemapGallery from "@arcgis/core/widgets/BasemapGallery";
import React, { useEffect, useState } from "react";
import topoImg from "../../assets/images/sidemenu/ksa500.jpg";
import ncwImg from "../../assets/images/sidemenu/ncw.jpeg";
import blendImg from "../../assets/images/sidemenu/blendmap.jpg";
import { useTranslation } from "react-i18next";
import { MdKeyboardArrowDown } from "react-icons/md";
import MapImageLayer from "@arcgis/core/layers/MapImageLayer.js";
import VectorTileLayer from "@arcgis/core/layers/VectorTileLayer.js";
import TileLayer from "@arcgis/core/layers/TileLayer";
import WebTileLayer from "@arcgis/core/layers/WebTileLayer.js";
import * as watchUtils from "@arcgis/core/core/watchUtils";
import Draggable from "react-draggable";
import IdentityManager from "@arcgis/core/identity/IdentityManager";

export default function BaseMap(props) {
  let activeBaseMap;
  const [showGallery, setShowGallery] = useState(false);
  const { i18n, t } = useTranslation(["map", "layers"]);
  const [selectedBaseMapIndex, setSelectedBaseMapIndex] = useState(
    window.___selectedBaseMapIndex || 0
  );

  let mainBaseMaps = [
    {
      name: "NCW",
      mapSrc: "satellite",
      imageSrc: ncwImg,
      custom: false,
    },
    {
      name: "Topography 500",
      mapSrc: window.KSA_500k_2_Url,
      imageSrc: topoImg,
      custom: true,
      isTile: true,
    },
    {
      name: "streets",
      mapSrc: "streets",
      imageSrc:
        "https://www.arcgis.com/sharing/rest/content/items/7e7bba5ba2f746629a9599d78b5170fe/info/thumbnail/street_thumb_b2wm.jpg?f=json",
      custom: false,
    },
  ];

  if (localStorage.user) {
    mainBaseMaps.push({
      name: props.languageState === "ar" ? "خريطة الأساس" : "Basemap",
      mapSrc: window.blendLayerUrl,
      imageSrc: blendImg,
      custom: true,
      isVector: true,
    });
    mainBaseMaps.push({
      name: props.languageState === "ar" ? "خريطه الاساس ٢" : "Basemap 2",
      mapSrc: window.contourLayerUrl,
      custom: true,
      isVector: true,
    });
    mainBaseMaps.push({
      name: props.languageState === "ar" ? "خريطه الاساس 3" : "Basemap 3",
      mapSrc: window.baseMapThreeUrl,
      custom: true,
      isVector: true,
    });
    mainBaseMaps.push({
      name: props.languageState === "ar" ? "خريطه الاساس 4" : "Basemap 4",
      mapSrc: window.baseMapFourUrl,
      custom: true,
      isVector: false,
      isCombined: true,
      combinedMapUrl: window.baseMapThreeUrl,
    });
    mainBaseMaps.push({
      name: props.languageState === "ar" ? "خريطه الاساس 5" : "Basemap 5",
      mapSrc: window.baseMapFiveUrl,
      custom: true,
      isTile: true,
      isCombined: true,
      combinedMapUrl: window.baseMapThreeUrl,
    });
    IdentityManager.registerToken({
      token: JSON.parse(localStorage.user)?.esriToken,
      server: window.blendLayerUrl,
    });
  }

  useEffect(() => {
    window.___selectedBaseMapIndex = selectedBaseMapIndex;
  }, [selectedBaseMapIndex]);

  useEffect(() => {
    let basemapGallery = new BasemapGallery({
      view: props.map.view,
      container: document.getElementById("basemapGallery"),
    });
    watchUtils.once(basemapGallery.source.basemaps, "length", function (state) {
      setTimeout(function () {
        basemapGallery.source.basemaps.removeAt(0);
        basemapGallery.source.basemaps.removeAt(0);
        basemapGallery.source.basemaps.removeAt(0);
      }, 400);
    });

    watchUtils.watch(props.map, "basemap", (newBasemap) => {
      let defaultBaseMapNames = [
        "satellite",
        "Topography 500",
        "streets",
        "Basemap",
      ];
      if (showGallery) {
        if (props.map.layers.items.find((x) => x.id == "blendBaseMap"))
          props.map.layers.remove(
            props.map.layers.items.find((x) => x.id == "blendBaseMap")
          );

        if (!defaultBaseMapNames.includes(newBasemap.id)) {
          window.___selectedBaseMapIndex = null;
          setSelectedBaseMapIndex(null);
        } else {
          if (activeBaseMap == "Topography 500") {
            let topoMap = mainBaseMaps.find(
              (map) => map.name == "Topography 500"
            );
            onChangeBaseMap(topoMap);
          }
        }
      }
    });
  }, [showGallery]);

  const onChangeBaseMap = (baseMap) => {
    if (activeBaseMap == baseMap.name) return;

    if (baseMap.isBlend) {
      setTimeout(() => {
        let blendBaseMap = new MapImageLayer({
          url: baseMap.mapSrc,
          id: "blendBaseMap",
        });
        let defaultBaseLayer = props.map.basemap.baseLayers.items.find(
          (lay) => lay.id != "customBaseMap"
        );
        if (defaultBaseLayer) defaultBaseLayer.visible = false;

        // Create OpenStreetMap (OSM) Basemap Layer
        let osmBasemapLayer = new WebTileLayer({
          id: "customBaseMap",
          urlTemplate:
            "https://{subDomain}.tile.openstreetmap.org/{level}/{col}/{row}.png",
          subDomains: ["a", "b", "c"],
          opacity: 1, // Initially visible
        });

        if (props.map.basemap.baseLayers.length <= 1) {
          // Event Listener: Hide Basemap When Blend Layer is Updating
          blendBaseMap.on("updating", (event) => {
            osmBasemapLayer.opacity = 0; // Hide basemap while loading
          });

          // Event Listener: Show Basemap Again When Blend Layer is Loaded
          blendBaseMap.when(() => {
            osmBasemapLayer.opacity = 0; // Show basemap again
          });

          props.map.view.watch("extent", (newExtent) => {
            osmBasemapLayer.opacity = 0;
          });

          // Alternative: Listen for LayerView Updates (More Accurate)
          props.map.view.whenLayerView(blendBaseMap).then((layerView) => {
            layerView.watch("updating", (isUpdating) => {
              if (isUpdating) {
                osmBasemapLayer.opacity = 0;
              } else {
                setTimeout(() => {
                  osmBasemapLayer.opacity = 1;
                }, 300);
              }
            });
          });

          props.map.basemap.baseLayers.add(osmBasemapLayer);
          props.map.layers.add(blendBaseMap);
        }
      }, 100);
    } else {
      if (props.map.layers.items.find((x) => x.id == "blendBaseMap"))
        props.map.layers.remove(
          props.map.layers.items.find((x) => x.id == "blendBaseMap")
        );

      if (baseMap.custom) {
        if (!["satellite", "streets"].includes(props.map.basemap.id)) {
          props.map.basemap = "streets";
        }

        setTimeout(() => {
          let customBaseMap = null;

          if (baseMap.isVector) {
            customBaseMap = new VectorTileLayer({
              url: baseMap.mapSrc,
              id: "customBaseMap",
            });
          } else if (baseMap.isTile) {
            customBaseMap = new TileLayer({
              url: baseMap.mapSrc,
              id: "customBaseMap",
            });
          } else {
            customBaseMap = new MapImageLayer({
              url: baseMap.mapSrc,
              id: "customBaseMap",
            });
          }

          let defaultBaseLayer = props.map.basemap.baseLayers.items.find(
            (lay) => lay.id != "customBaseMap"
          );
          if (defaultBaseLayer) defaultBaseLayer.visible = false;
          let customMaps = props.map.basemap.baseLayers.items.filter(
            (lay) => lay.id == "customBaseMap"
          );
          customMaps.forEach((customMap) => {
            props.map.basemap.baseLayers.remove(customMap);
          });
          if (props.map.basemap.baseLayers.length <= 1) {
            props.map.basemap.baseLayers.add(customBaseMap);
          }
          if (baseMap.isCombined) {
            let combinedBaseMap = new VectorTileLayer({
              url: baseMap.combinedMapUrl,
              id: "customBaseMap",
            });
            props.map.basemap.baseLayers.add(combinedBaseMap);
          }
        }, 100);
      } else {
        if (
          props.map.basemap.baseLayers.items.find(
            (layer) => layer.id === "customBaseMap"
          )
        ) {
          let customMap = props.map.basemap.baseLayers.items.find(
            (layer) => layer.id === "customBaseMap"
          );
          props.map.basemap.baseLayers.remove(customMap);
        }
        props.map.basemap = baseMap.mapSrc;
        if (props.map.basemap.baseLayers.items[0]) {
          props.map.basemap.baseLayers.items[0].visible = true;
        }
        if (props.map.basemap.baseLayers.items.length > 1) {
          props.map.basemap.baseLayers.remove(
            props.map.basemap.baseLayers.items[1]
          );
        }
      }
    }
  };

  const [x, setX] = React.useState(0);
  const [y, setY] = React.useState(0);

  const handleDrag = (e, position) => {
    setX(position.x);
    setY(position.y);
  };

  return (
    // <Fade left>
    <Draggable
      position={{ x, y }}
      onDrag={handleDrag}
      bounds={{
        left:
          i18n.language === "ar"
            ? -(window.innerWidth - 400)
            : -window.innerWidth,
        top: -300,
        right: i18n.language === "ar" ? window.innerWidth - 400 : 0, // Subtract component width
        bottom: showGallery
          ? window.innerHeight - 600
          : window.innerHeight - 350, // Subtract component height
      }}
    >
      <div
        className="toolsMenu inquiryTool layersMenu leftToolMenu"
        style={{
          overflow: "auto",
          height: "fit-content",
          maxHeight: "500px",
          position: "relative",
          top: "55px",
        }}
      >
        <Fade left>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              color: "#fff",
              padding: "8px",
              position: "sticky",
              top: "0",
              zIndex: "1000",
              backgroundColor: "transparent",
            }}
          >
            <span>
              <FontAwesomeIcon
                icon={faTimes}
                style={{
                  marginTop: "5px",
                  marginRight: "5px",
                  cursor: "pointer",
                }}
                onClick={(e) => {
                  e.stopPropagation();
                  props.closeToolsData();
                }}
              />
            </span>

            <div style={{ fontWeight: "bold" }}>
              {t("mapToolsServices.Basemap")}
            </div>
          </div>

          <ul
            style={{
              display: "grid",
              gridTemplateColumns: "repeat(3, 1fr)",
              gap: "10px",
              height: showGallery ? "225px" : "auto",
              overflow: "auto",
              padding: "7px",
              border: "2px solid #fff",
              borderRadius: "12px",
            }}
          >
            {mainBaseMaps.map((baseMap, index) => {
              return (
                <li
                  key={index}
                  className={`esri-basemap-gallery__item ${
                    selectedBaseMapIndex === index &&
                    "esri-basemap-gallery__item--selected"
                  }`}
                  onClick={() => {
                    onChangeBaseMap(baseMap);
                    activeBaseMap = baseMap.name;
                    setSelectedBaseMapIndex(index);
                  }}
                >
                  <img
                    className="esri-basemap-gallery__item-thumbnail"
                    src={baseMap.imageSrc}
                    alt=""
                    style={{ width: "100%", borderRadius: "8px" }}
                  />
                  <div
                    className="esri-basemap-gallery__item-title"
                    style={{
                      textWrap: "wrap",
                      fontSize: "10px",
                      color: "#fff",
                    }}
                  >
                    {t(baseMap.name, { ns: "layers" })}
                  </div>
                </li>
              );
            })}
          </ul>

          <div
            style={{
              padding: "7px",
              border: "2px solid #fff",
              borderRadius: "12px",
              height: showGallery ? "225px" : "auto",
              overflow: "auto",
            }}
          >
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
              }}
            >
              <MdKeyboardArrowDown
                size={20}
                style={{
                  cursor: "pointer",
                  transform: `rotate(${showGallery ? "180deg" : 0})`,
                }}
                onClick={() => setShowGallery(!showGallery)}
              />

              <div style={{ color: "#fff" }}>{t("mapTools.more")}</div>
            </div>

            {showGallery && <div id="basemapGallery"></div>}
          </div>
        </Fade>
      </div>
    </Draggable>
    // </Fade>
  );
}
